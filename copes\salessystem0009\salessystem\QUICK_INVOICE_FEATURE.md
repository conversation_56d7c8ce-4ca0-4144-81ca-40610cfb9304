# ميزة الفواتير السريعة - السلايد الجانبية

## 🚀 الميزة الجديدة المضافة

### **سلايد جانبية لإدراج الفواتير السريعة** ⚡

تم إضافة نظام متكامل لإنشاء فواتير المبيعات والمشتريات بشكل سريع ومباشر من الصفحة الرئيسية دون الحاجة للانتقال لصفحات منفصلة.

## 🎯 المكونات المضافة

### **1. الأزرار العائمة (Floating Buttons):**

#### **الزر الرئيسي:**
- موقع ثابت في أسفل يمين الشاشة
- تصميم دائري أنيق مع تأثيرات بصرية
- يفتح قائمة فرعية عند النقر

#### **القائمة الفرعية:**
- زر فاتورة مبيعات سريعة (أخضر)
- زر فاتورة مشتريات سريعة (أحمر)
- تأثيرات انتقال سلسة

### **2. أزرار الإجراءات السريعة:**
تم إضافة أزرار جديدة في قسم الإجراءات السريعة:
- **فاتورة مبيعات سريعة** - زر أخضر مع أيقونة البرق
- **فاتورة مشتريات سريعة** - زر أحمر مع أيقونة الصاعقة

### **3. السلايد الجانبية (Sidebar):**

#### **التصميم:**
- عرض 500px على الشاشات الكبيرة
- عرض كامل على الشاشات الصغيرة
- تأثير انزلاق من اليمين
- خلفية شفافة مع إمكانية الإغلاق بالنقر عليها

#### **المحتوى:**
```html
<!-- رأس السلايد -->
<div class="sidebar-header">
    <h5>فاتورة مبيعات/مشتريات سريعة</h5>
    <button class="btn-close">×</button>
</div>

<!-- محتوى النموذج -->
<div class="sidebar-content">
    <!-- أقسام النموذج -->
</div>

<!-- أزرار الحفظ -->
<div class="sidebar-footer">
    <button>حفظ الفاتورة</button>
    <button>إلغاء</button>
</div>
```

## 📋 أقسام النموذج

### **1. المعلومات الأساسية:**
- **العميل**: قائمة منسدلة بجميع العملاء
- **التاريخ**: حقل تاريخ (افتراضي: اليوم)
- **رقم الفاتورة**: يتم توليده تلقائياً

### **2. الأصناف:**
- **اسم الصنف**: حقل نصي
- **الكمية**: حقل رقمي (افتراضي: 1)
- **السعر**: حقل رقمي
- **زر إضافة صنف**: لإضافة أصناف متعددة
- **زر حذف**: لحذف الأصناف (مُعطل للصنف الأول)

### **3. الحسابات:**
- **نسبة الضريبة**: حقل رقمي (افتراضي: 15%)
- **الخصم**: حقل رقمي (افتراضي: 0)
- **ملخص الحسابات**: عرض تفاعلي للمجاميع

### **4. ملاحظات:**
- حقل نصي متعدد الأسطر للملاحظات الإضافية

## ⚙️ الوظائف التفاعلية

### **JavaScript Functions:**

#### **إدارة القائمة العائمة:**
```javascript
function toggleFloatingMenu() {
    // فتح/إغلاق القائمة العائمة
}

function openQuickInvoice(type) {
    // فتح السلايد حسب نوع الفاتورة
    // type: 'sale' أو 'purchase'
}

function closeQuickInvoice() {
    // إغلاق السلايد وإعادة تعيين النموذج
}
```

#### **إدارة الأصناف:**
```javascript
function addQuickItem() {
    // إضافة صنف جديد للفاتورة
}

function removeQuickItem(button) {
    // حذف صنف من الفاتورة
}

function updateDeleteButtons() {
    // تحديث حالة أزرار الحذف
}
```

#### **الحسابات التلقائية:**
```javascript
function calculateQuickTotal() {
    // حساب المجموع الفرعي
    // تطبيق الخصم
    // حساب الضريبة
    // حساب الإجمالي النهائي
}

function generateInvoiceNumber(type) {
    // توليد رقم فاتورة فريد
    // تنسيق: S/P + YYYYMMDDHHMMSS
}
```

## 🎨 التصميم والأنماط

### **الألوان:**
- **المبيعات**: تدرج أخضر (#28a745 → #1e7e34)
- **المشتريات**: تدرج أحمر (#dc3545 → #c82333)
- **الرئيسي**: تدرج أزرق (#007bff → #0056b3)

### **التأثيرات:**
- **الأزرار العائمة**: ظلال وتحويلات عند التمرير
- **السلايد**: انتقال سلس من اليمين
- **النماذج**: تركيز محسن وحدود ملونة
- **الحسابات**: تحديث فوري للمجاميع

### **الاستجابة:**
```css
@media (max-width: 768px) {
    .quick-invoice-sidebar {
        width: 100%; /* عرض كامل للشاشات الصغيرة */
    }
    
    .floating-btn {
        width: 50px; /* أزرار أصغر */
        height: 50px;
    }
}
```

## 🔧 معالجة البيانات

### **ملف المعالجة: `process_quick_invoice.php`**

#### **التحقق من البيانات:**
```php
// التحقق من نوع الفاتورة
if (!in_array($invoice_type, ['sale', 'purchase'])) {
    throw new Exception("نوع الفاتورة غير صحيح");
}

// التحقق من العميل
if (empty($customer_id)) {
    throw new Exception("يجب اختيار العميل");
}

// التحقق من الأصناف
if (empty($valid_items)) {
    throw new Exception("لا توجد أصناف صحيحة");
}
```

#### **الحسابات:**
```php
// المجموع الفرعي
$subtotal = array_sum(array_column($valid_items, 'total'));

// الخصم (لا يتجاوز المجموع الفرعي)
$discount_amount = min($discount, $subtotal);

// بعد الخصم
$after_discount = $subtotal - $discount_amount;

// الضريبة
$tax_amount = ($after_discount * $tax_rate) / 100;

// الإجمالي النهائي
$total_amount = $after_discount + $tax_amount;
```

#### **حفظ البيانات:**
```php
// بدء المعاملة
$db->begin_transaction();

// حفظ الفاتورة الرئيسية
$stmt = $db->prepare("INSERT INTO $table (...) VALUES (...)");

// حفظ أصناف الفاتورة
$item_stmt = $db->prepare("INSERT INTO $items_table (...) VALUES (...)");

// تأكيد المعاملة
$db->commit();
```

## 🔍 مميزات النظام

### **سهولة الاستخدام:**
- ✅ **وصول سريع** من الصفحة الرئيسية
- ✅ **واجهة بديهية** وسهلة الفهم
- ✅ **حسابات تلقائية** فورية
- ✅ **توليد أرقام فواتير** تلقائي

### **المرونة:**
- ✅ **أصناف متعددة** قابلة للإضافة والحذف
- ✅ **ضرائب وخصومات** قابلة للتخصيص
- ✅ **ملاحظات إضافية** للتفاصيل
- ✅ **دعم المبيعات والمشتريات**

### **الأمان:**
- ✅ **التحقق من البيانات** على الخادم والعميل
- ✅ **معاملات قاعدة البيانات** الآمنة
- ✅ **منع SQL Injection** باستخدام Prepared Statements
- ✅ **التحقق من الصلاحيات** والجلسة

### **الأداء:**
- ✅ **تحميل سريع** للسلايد
- ✅ **حسابات محلية** بـ JavaScript
- ✅ **تحديث فوري** للمجاميع
- ✅ **استجابة سريعة** للتفاعلات

## 📱 التوافق والاستجابة

### **الشاشات الكبيرة:**
- سلايد بعرض 500px
- أزرار عائمة في الزاوية اليمنى السفلى
- تخطيط متعدد الأعمدة للنموذج

### **الشاشات الصغيرة:**
- سلايد بعرض كامل
- أزرار عائمة أصغر
- تخطيط عمود واحد للنموذج

### **إمكانية الوصول:**
- دعم لوحة المفاتيح (Escape للإغلاق)
- تسميات واضحة للحقول
- تباين ألوان مناسب
- تركيز محسن للعناصر

## 🎯 طرق الاستخدام

### **1. من الأزرار العائمة:**
1. النقر على الزر العائم الرئيسي (+)
2. اختيار نوع الفاتورة (مبيعات/مشتريات)
3. ملء بيانات الفاتورة
4. حفظ الفاتورة

### **2. من قسم الإجراءات السريعة:**
1. النقر على "فاتورة مبيعات سريعة" أو "فاتورة مشتريات سريعة"
2. ملء بيانات الفاتورة في السلايد
3. حفظ الفاتورة

### **3. إدارة الأصناف:**
1. ملء بيانات الصنف الأول
2. النقر على "إضافة صنف" لأصناف إضافية
3. استخدام زر الحذف لإزالة الأصناف غير المرغوبة
4. مراقبة تحديث المجاميع تلقائياً

## ✅ النتائج المحققة

### **تحسين تجربة المستخدم:**
- ✅ **وقت أقل** لإنشاء الفواتير
- ✅ **خطوات أقل** للوصول للوظائف
- ✅ **واجهة موحدة** لا تتطلب تنقل
- ✅ **تفاعل سلس** ومتجاوب

### **زيادة الكفاءة:**
- ✅ **إنشاء فواتير سريعة** في ثوانٍ
- ✅ **حسابات تلقائية** دقيقة
- ✅ **توليد أرقام فواتير** فريدة
- ✅ **حفظ مباشر** في قاعدة البيانات

### **مرونة النظام:**
- ✅ **دعم أنواع فواتير متعددة**
- ✅ **أصناف غير محدودة**
- ✅ **ضرائب وخصومات مرنة**
- ✅ **ملاحظات مخصصة**

**النتيجة: نظام فواتير سريع ومتكامل يحسن الإنتاجية ويوفر تجربة مستخدم استثنائية!** 🎉
