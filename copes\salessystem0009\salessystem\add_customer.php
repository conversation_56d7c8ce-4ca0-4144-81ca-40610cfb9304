<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $email = trim($_POST['email']);
    $tax_number = trim($_POST['tax_number']);
    $address = trim($_POST['address']);

    $stmt = $db->prepare("INSERT INTO customers (name, phone, email, tax_number, address) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("sssss", $name, $phone, $email, $tax_number, $address);

    if ($stmt->execute()) {
        $_SESSION['success'] = "تم إضافة العميل بنجاح";
        header("Location: customers.php");
        exit();
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة العميل";
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">إضافة عميل جديد</div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم العميل *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الجوال</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label"><?php echo __('email_address'); ?></label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="tax_number" class="form-label"><?php echo __('tax_number'); ?></label>
                        <input type="text" class="form-control" id="tax_number" name="tax_number">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label"><?php echo __('address'); ?></label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <a href="customers.php" class="btn btn-secondary">إلغاء</a>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>