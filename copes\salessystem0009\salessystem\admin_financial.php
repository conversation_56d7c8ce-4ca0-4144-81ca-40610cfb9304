<?php
/**
 * صفحة التقارير المالية وتفاصيل الفواتير
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

global $main_db;

// معاملات البحث
$user_filter = $_GET['user_id'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$report_type = $_GET['report_type'] ?? 'summary';

// جلب قائمة المستخدمين
$users_result = $main_db->query("SELECT id, username, full_name FROM users WHERE is_active = 1 ORDER BY full_name");

// جلب البيانات المالية
$financial_data = [];
$total_sales = 0;
$total_purchases = 0;
$total_profit = 0;

if ($user_filter) {
    // تقرير مستخدم محدد
    $user_db_name = "sales_system_user_" . $user_filter;
    $user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);
    
    if (!$user_db->connect_error) {
        // جلب بيانات المبيعات
        $sales_query = "SELECT s.*, c.name as customer_name 
                       FROM sales s 
                       LEFT JOIN customers c ON s.customer_id = c.id 
                       WHERE s.date BETWEEN '$date_from' AND '$date_to' 
                       ORDER BY s.date DESC";
        $sales_result = $user_db->query($sales_query);
        
        // جلب بيانات المشتريات
        $purchases_query = "SELECT p.*, c.name as customer_name 
                           FROM purchases p 
                           LEFT JOIN customers c ON p.customer_id = c.id 
                           WHERE p.date BETWEEN '$date_from' AND '$date_to' 
                           ORDER BY p.date DESC";
        $purchases_result = $user_db->query($purchases_query);
        
        // حساب الإجماليات
        $sales_total_result = $user_db->query("SELECT SUM(total_amount) as total FROM sales WHERE date BETWEEN '$date_from' AND '$date_to'");
        $purchases_total_result = $user_db->query("SELECT SUM(total_amount) as total FROM purchases WHERE date BETWEEN '$date_from' AND '$date_to'");
        
        $total_sales = $sales_total_result->fetch_assoc()['total'] ?? 0;
        $total_purchases = $purchases_total_result->fetch_assoc()['total'] ?? 0;
        $total_profit = $total_sales - $total_purchases;
    }
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="admin_financial.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>التقارير المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_system.php">
                            <i class="fas fa-cogs me-2"></i>إعدادات النظام
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-file-invoice-dollar me-2 text-danger"></i>التقارير المالية
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="exportFinancialReport()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                    </div>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card shadow-sm border-danger mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>فلاتر التقرير المالي</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="user_id" class="form-label">المستخدم</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">جميع المستخدمين</option>
                                <?php while ($user = $users_result->fetch_assoc()): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['full_name']); ?>
                                </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="report_type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>ملخص</option>
                                <option value="detailed" <?php echo $report_type === 'detailed' ? 'selected' : ''; ?>>تفصيلي</option>
                                <option value="invoices" <?php echo $report_type === 'invoices' ? 'selected' : ''; ?>>الفواتير</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-search me-1"></i>إنشاء التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($user_filter): ?>
            <!-- الملخص المالي -->
            <div class="row mb-4">
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        إجمالي المبيعات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($total_sales, 2); ?> ريال
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                        إجمالي المشتريات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($total_purchases, 2); ?> ريال
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-<?php echo $total_profit >= 0 ? 'success' : 'warning'; ?> shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-<?php echo $total_profit >= 0 ? 'success' : 'warning'; ?> text-uppercase mb-1">
                                        صافي الربح
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($total_profit, 2); ?> ريال
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($report_type === 'invoices' && isset($sales_result)): ?>
            <!-- تفاصيل فواتير المبيعات -->
            <div class="card shadow border-success mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-file-invoice me-2"></i>فواتير المبيعات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>العميل</th>
                                    <th>المبلغ الفرعي</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($sale = $sales_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($sale['invoice_number']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($sale['date'])); ?></td>
                                    <td><?php echo htmlspecialchars($sale['customer_name'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo number_format($sale['subtotal'], 2); ?></td>
                                    <td><?php echo number_format($sale['tax_amount'], 2); ?></td>
                                    <td><strong><?php echo number_format($sale['total_amount'], 2); ?></strong></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewInvoiceDetails(<?php echo $sale['id']; ?>, 'sale')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تفاصيل فواتير المشتريات -->
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-file-invoice me-2"></i>فواتير المشتريات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <th>المبلغ الفرعي</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($purchases_result)): ?>
                                <?php while ($purchase = $purchases_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($purchase['invoice_number']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($purchase['date'])); ?></td>
                                    <td><?php echo htmlspecialchars($purchase['customer_name'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo number_format($purchase['subtotal'], 2); ?></td>
                                    <td><?php echo number_format($purchase['tax_amount'], 2); ?></td>
                                    <td><strong><?php echo number_format($purchase['total_amount'], 2); ?></strong></td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="viewInvoiceDetails(<?php echo $purchase['id']; ?>, 'purchase')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>اختر مستخدم لعرض التقرير المالي</h5>
                <p>يرجى اختيار مستخدم من القائمة أعلاه لعرض التقارير المالية وتفاصيل الفواتير.</p>
            </div>
            <?php endif; ?>

        </main>
    </div>
</div>

<script>
function viewInvoiceDetails(invoiceId, type) {
    // سيتم إضافة نافذة منبثقة لعرض تفاصيل الفاتورة
    alert('سيتم إضافة عرض تفاصيل الفاتورة قريباً');
}

function exportFinancialReport() {
    // سيتم إضافة وظيفة تصدير التقرير المالي
    alert('سيتم إضافة وظيفة التصدير قريباً');
}
</script>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
