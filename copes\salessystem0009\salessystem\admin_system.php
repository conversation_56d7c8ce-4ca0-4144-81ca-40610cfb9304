<?php
/**
 * صفحة إعدادات النظام للمدير
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('manage_system')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

global $main_db;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'backup_database':
            // إنشاء نسخة احتياطية
            $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
            $backup_path = __DIR__ . '/backups/' . $backup_file;
            
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            if (!is_dir(__DIR__ . '/backups')) {
                mkdir(__DIR__ . '/backups', 0755, true);
            }
            
            // تنفيذ النسخ الاحتياطي
            $command = "mysqldump -h" . MAIN_DB_HOST . " -u" . MAIN_DB_USER . " -p" . MAIN_DB_PASS . " " . MAIN_DB_NAME . " > " . $backup_path;
            exec($command, $output, $return_var);
            
            if ($return_var === 0) {
                logActivity('database_backup', 'system', null, null, null, 'تم إنشاء نسخة احتياطية: ' . $backup_file);
                $_SESSION['success'] = 'تم إنشاء النسخة الاحتياطية بنجاح';
            } else {
                $_SESSION['error'] = 'فشل في إنشاء النسخة الاحتياطية';
            }
            break;
            
        case 'clear_logs':
            // مسح السجلات القديمة
            $days = intval($_POST['days'] ?? 30);
            $stmt = $main_db->prepare("DELETE FROM activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
            $stmt->bind_param("i", $days);
            if ($stmt->execute()) {
                $deleted_rows = $stmt->affected_rows;
                logActivity('logs_cleared', 'activity_log', null, null, null, "تم حذف $deleted_rows سجل أقدم من $days يوم");
                $_SESSION['success'] = "تم حذف $deleted_rows سجل قديم";
            } else {
                $_SESSION['error'] = 'فشل في مسح السجلات';
            }
            $stmt->close();
            break;
            
        case 'optimize_database':
            // تحسين قاعدة البيانات
            $tables = ['users', 'admins', 'activity_log'];
            $optimized = 0;
            foreach ($tables as $table) {
                if ($main_db->query("OPTIMIZE TABLE $table")) {
                    $optimized++;
                }
            }
            logActivity('database_optimized', 'system', null, null, null, "تم تحسين $optimized جدول");
            $_SESSION['success'] = "تم تحسين $optimized جدول في قاعدة البيانات";
            break;
    }
    
    header("Location: admin_system.php");
    exit();
}

// جلب إحصائيات النظام
$system_stats = [];

// إحصائيات قاعدة البيانات
$db_size_query = "SELECT 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
    FROM information_schema.tables 
    WHERE table_schema = '" . MAIN_DB_NAME . "'";
$db_size_result = $main_db->query($db_size_query);
$system_stats['db_size'] = $db_size_result ? $db_size_result->fetch_assoc()['db_size_mb'] : 0;

// عدد السجلات في الجداول الرئيسية
$system_stats['users_count'] = $main_db->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$system_stats['admins_count'] = $main_db->query("SELECT COUNT(*) as count FROM admins")->fetch_assoc()['count'];
$system_stats['logs_count'] = $main_db->query("SELECT COUNT(*) as count FROM activity_log")->fetch_assoc()['count'];

// إحصائيات النسخ الاحتياطية
$backup_dir = __DIR__ . '/backups';
$backup_files = [];
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $backup_files[] = [
                'name' => $file,
                'size' => filesize($backup_dir . '/' . $file),
                'date' => filemtime($backup_dir . '/' . $file)
            ];
        }
    }
    // ترتيب حسب التاريخ (الأحدث أولاً)
    usort($backup_files, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_financial_reports.php">
                            <i class="fas fa-chart-line me-2"></i>التقارير المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="admin_system.php">
                            <i class="fas fa-cogs me-2"></i>إعدادات النظام
                        </a>
                    </li>
                    <?php if (hasAdminPermission('manage_admins')): ?>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_manage_admins.php">
                            <i class="fas fa-user-shield me-2"></i>إدارة المديرين
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-cogs me-2 text-primary"></i>إعدادات النظام
                </h1>
            </div>

            <?php displayMessages(); ?>

            <!-- إحصائيات النظام -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        حجم قاعدة البيانات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['db_size'], 2); ?> MB
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-database fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        المستخدمين المسجلين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['users_count']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        المديرين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['admins_count']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        سجلات النشاط
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($system_stats['logs_count']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- أدوات النظام -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">أدوات النظام</h6>
                        </div>
                        <div class="card-body">
                            <!-- النسخ الاحتياطي -->
                            <div class="mb-4">
                                <h6><i class="fas fa-download me-2 text-success"></i>النسخ الاحتياطي</h6>
                                <p class="text-muted">إنشاء نسخة احتياطية من قاعدة البيانات الرئيسية</p>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="backup_database">
                                    <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('هل تريد إنشاء نسخة احتياطية؟')">
                                        <i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية
                                    </button>
                                </form>
                            </div>

                            <hr>

                            <!-- مسح السجلات -->
                            <div class="mb-4">
                                <h6><i class="fas fa-trash me-2 text-warning"></i>مسح السجلات القديمة</h6>
                                <p class="text-muted">حذف سجلات النشاط الأقدم من عدد الأيام المحدد</p>
                                <form method="POST" class="d-flex align-items-end gap-2">
                                    <input type="hidden" name="action" value="clear_logs">
                                    <div>
                                        <label class="form-label">عدد الأيام</label>
                                        <input type="number" name="days" class="form-control form-control-sm" value="30" min="1" max="365" style="width: 100px;">
                                    </div>
                                    <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('هل تريد حذف السجلات القديمة؟')">
                                        <i class="fas fa-trash me-1"></i>مسح السجلات
                                    </button>
                                </form>
                            </div>

                            <hr>

                            <!-- تحسين قاعدة البيانات -->
                            <div class="mb-4">
                                <h6><i class="fas fa-tools me-2 text-info"></i>تحسين قاعدة البيانات</h6>
                                <p class="text-muted">تحسين جداول قاعدة البيانات لتحسين الأداء</p>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="optimize_database">
                                    <button type="submit" class="btn btn-info btn-sm" onclick="return confirm('هل تريد تحسين قاعدة البيانات؟')">
                                        <i class="fas fa-tools me-1"></i>تحسين قاعدة البيانات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- النسخ الاحتياطية -->
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">النسخ الاحتياطية</h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($backup_files)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>اسم الملف</th>
                                            <th>الحجم</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($backup_files, 0, 10) as $backup): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($backup['name']); ?></td>
                                            <td><?php echo number_format($backup['size'] / 1024, 2); ?> KB</td>
                                            <td><?php echo date('Y-m-d H:i', $backup['date']); ?></td>
                                            <td>
                                                <a href="backups/<?php echo urlencode($backup['name']); ?>" 
                                                   class="btn btn-sm btn-outline-primary" download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-folder-open fa-3x mb-3"></i>
                                <p>لا توجد نسخ احتياطية</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات النظام</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th>إصدار PHP:</th>
                                    <td><?php echo phpversion(); ?></td>
                                </tr>
                                <tr>
                                    <th>إصدار MySQL:</th>
                                    <td><?php echo $main_db->server_info; ?></td>
                                </tr>
                                <tr>
                                    <th>نظام التشغيل:</th>
                                    <td><?php echo php_uname('s') . ' ' . php_uname('r'); ?></td>
                                </tr>
                                <tr>
                                    <th>الذاكرة المحددة:</th>
                                    <td><?php echo ini_get('memory_limit'); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th>حد رفع الملفات:</th>
                                    <td><?php echo ini_get('upload_max_filesize'); ?></td>
                                </tr>
                                <tr>
                                    <th>حد POST:</th>
                                    <td><?php echo ini_get('post_max_size'); ?></td>
                                </tr>
                                <tr>
                                    <th>مهلة التنفيذ:</th>
                                    <td><?php echo ini_get('max_execution_time'); ?> ثانية</td>
                                </tr>
                                <tr>
                                    <th>المنطقة الزمنية:</th>
                                    <td><?php echo date_default_timezone_get(); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.sidebar {
    min-height: 100vh;
}
.nav-link.active {
    background-color: #495057 !important;
}
</style>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
