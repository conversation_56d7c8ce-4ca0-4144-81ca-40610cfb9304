/**
 * Enhanced Sales & Purchases System
 * Main JavaScript file
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    initTooltips();
    
    // Initialize search functionality
    initSearch();
    
    // Initialize table sorting
    initTableSorting();
    
    // Initialize dashboard animations
    initDashboardAnimations();
    
    // Initialize responsive behaviors
    initResponsiveBehaviors();
});

/**
 * Initialize Bootstrap tooltips
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            boundary: document.body
        });
    });
}

/**
 * Initialize search functionality for tables
 */
function initSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(input => {
        input.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const tableId = this.getAttribute('data-table');
            const table = document.getElementById(tableId);
            
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.indexOf(searchTerm) > -1) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
}

/**
 * Initialize table sorting functionality
 */
function initTableSorting() {
    const tables = document.querySelectorAll('.sortable');
    
    tables.forEach(table => {
        const headers = table.querySelectorAll('th.sortable');
        
        headers.forEach(header => {
            header.addEventListener('click', function() {
                const index = Array.from(this.parentNode.children).indexOf(this);
                const isAscending = this.classList.contains('asc');
                
                // Remove sorting classes from all headers
                headers.forEach(h => {
                    h.classList.remove('asc', 'desc');
                    h.querySelector('i.sort-icon')?.remove();
                });
                
                // Add sorting class and icon to current header
                this.classList.add(isAscending ? 'desc' : 'asc');
                const icon = document.createElement('i');
                icon.className = `fas fa-sort-${isAscending ? 'down' : 'up'} ms-1 sort-icon`;
                this.appendChild(icon);
                
                // Sort the table
                sortTable(table, index, !isAscending);
            });
        });
    });
}

/**
 * Sort a table by a specific column
 * @param {HTMLElement} table - The table element
 * @param {number} column - The column index to sort by
 * @param {boolean} asc - Sort in ascending order if true
 */
function sortTable(table, column, asc) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Sort the rows
    const sortedRows = rows.sort((a, b) => {
        const aValue = a.children[column].textContent.trim();
        const bValue = b.children[column].textContent.trim();
        
        // Check if values are numbers
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return asc ? aNum - bNum : bNum - aNum;
        }
        
        // Sort as strings
        return asc 
            ? aValue.localeCompare(bValue) 
            : bValue.localeCompare(aValue);
    });
    
    // Remove existing rows
    rows.forEach(row => tbody.removeChild(row));
    
    // Add sorted rows
    sortedRows.forEach(row => tbody.appendChild(row));
}

/**
 * Initialize dashboard animations
 */
function initDashboardAnimations() {
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    
    // Add fade-in animation to dashboard cards
    dashboardCards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${index * 0.1}s`;
    });
}

/**
 * Initialize responsive behaviors
 */
function initResponsiveBehaviors() {
    // Add responsive table behavior
    const tables = document.querySelectorAll('.table-responsive-stack');
    
    if (window.innerWidth < 768) {
        tables.forEach(table => {
            const thElements = table.querySelectorAll('th');
            const tdElements = table.querySelectorAll('tbody td');
            
            // Add data-label attribute to each td based on th content
            tdElements.forEach((td, index) => {
                const thIndex = index % thElements.length;
                td.setAttribute('data-label', thElements[thIndex].textContent);
            });
        });
    }
}

/**
 * Format a number as currency
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency symbol
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
    }).format(amount);
}

/**
 * Print an invoice
 * @param {string} elementId - The ID of the element to print
 */
function printInvoice(elementId) {
    const printContents = document.getElementById(elementId).innerHTML;
    const originalContents = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div class="invoice-print-container">
            ${printContents}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContents;
    
    // Reinitialize scripts after restoring content
    document.addEventListener('DOMContentLoaded', function() {
        initTooltips();
        initSearch();
        initTableSorting();
        initDashboardAnimations();
        initResponsiveBehaviors();
    });
}

/**
 * Export table to CSV
 * @param {string} tableId - The ID of the table to export
 * @param {string} filename - The filename for the CSV
 */
function exportTableToCSV(tableId, filename = 'export.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    for (let i = 0; i < rows.length; i++) {
        const row = [], cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            // Get text content and clean it
            let data = cols[j].textContent.replace(/(\r\n|\n|\r)/gm, '').trim();
            // Escape double quotes
            data = data.replace(/"/g, '""');
            // Add quotes around the data
            row.push('"' + data + '"');
        }
        
        csv.push(row.join(','));
    }
    
    // Create CSV file
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    
    // Create download link
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
