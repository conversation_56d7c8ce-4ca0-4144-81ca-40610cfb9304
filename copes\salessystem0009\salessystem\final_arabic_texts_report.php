<?php
/**
 * تقرير نهائي شامل عن جميع النصوص العربية المكتشفة في المشروع
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// قائمة شاملة لجميع النصوص العربية المكتشفة مصنفة حسب النوع
$discovered_arabic_texts = [
    'تعليقات_الكود' => [
        'title' => 'تعليقات الكود والوصف',
        'icon' => 'fas fa-comment',
        'color' => 'primary',
        'texts' => [
            'يجب أن تكون هذه السطور الأولى في الملف بالضبط',
            'هذا الملف يحتوي على تعريف',
            'تنظيف أي نتائج متبقية',
            'عرض أي رسائل خطأ أو نجاح',
            'إضافة معلومات تصحيح عن قاعدة البيانات',
            'التحقق من بنية جدول المشتريات',
            'بناء الاستعلام مع الفلاتر',
            'فلتر البحث',
            'فلتر العميل',
            'فلتر التاريخ من',
            'فلتر التاريخ إلى',
            'بناء جملة',
            'التحقق من وجود جدول المشتريات مرة أخرى',
            'التحقق من وجود الأعمدة المطلوبة في جدول المشتريات',
            'بناء قائمة الأعمدة بناءً على ما هو متاح'
        ]
    ],
    
    'رسائل_النظام' => [
        'title' => 'رسائل النجاح والخطأ',
        'icon' => 'fas fa-exclamation-circle',
        'color' => 'success',
        'texts' => [
            'تم إضافة العميل بنجاح',
            'حدث خطأ أثناء إضافة العميل',
            'تم التحديث بنجاح',
            'تم الحذف بنجاح',
            'حدث خطأ أثناء التحديث',
            'حدث خطأ أثناء الحذف',
            'جدول المشتريات غير موجود في قاعدة البيانات',
            'فشل في إعداد الاستعلام',
            'تم بنجاح',
            'فشل في العملية',
            'تم الإرسال',
            'تم الاستلام'
        ]
    ],
    
    'عناصر_الواجهة' => [
        'title' => 'عناصر واجهة المستخدم',
        'icon' => 'fas fa-desktop',
        'color' => 'info',
        'texts' => [
            'إضافة عميل جديد',
            'اسم العميل',
            'رقم الجوال',
            'البريد الإلكتروني',
            'الرقم الضريبي',
            'العنوان',
            'حفظ',
            'إلغاء',
            'إحصائيات المشتريات',
            'إحصائيات المبيعات',
            'جدول المشتريات',
            'جدول المبيعات',
            'القائمة الجانبية'
        ]
    ],
    
    'رسائل_قاعدة_البيانات' => [
        'title' => 'رسائل قاعدة البيانات التقنية',
        'icon' => 'fas fa-database',
        'color' => 'warning',
        'texts' => [
            'تحديد الأعمدة المطلوبة بشكل صريح بدلاً من استخدام',
            'استعلام بديل بدون عمود',
            'إضافة معلومات تصحيح للمطور',
            'إضافة معلومات تصحيح للصف الحالي',
            'التحقق من وجود الحقول المطلوبة',
            'استخدام قيمة مباشرة إذا كانت موجودة',
            'حساب من',
            'قيمة افتراضية'
        ]
    ],
    
    'حالات_النظام' => [
        'title' => 'حالات وأوضاع النظام',
        'icon' => 'fas fa-cog',
        'color' => 'secondary',
        'texts' => [
            'في الانتظار',
            'مكتمل',
            'ملغي',
            'مؤجل',
            'قيد المراجعة',
            'تمت الموافقة',
            'مرفوض'
        ]
    ],
    
    'رسائل_التصحيح' => [
        'title' => 'رسائل التصحيح والتطوير',
        'icon' => 'fas fa-bug',
        'color' => 'danger',
        'texts' => [
            'عرض رسالة الخطأ للمطور',
            'سيتم إخفاؤها في الإنتاج',
            'تعريف وضع التصحيح محليًا',
            'يمكن نقل هذا إلى ملف التكوين لاحقًا',
            'تغيير إلى في بيئة الإنتاج',
            'عرض رسالة خطأ أكثر تفصيلاً للمستخدم في بيئة التطوير'
        ]
    ],
    
    'رسائل_التفاعل' => [
        'title' => 'رسائل التفاعل والتأكيد',
        'icon' => 'fas fa-question-circle',
        'color' => 'dark',
        'texts' => [
            'هل أنت متأكد من الحذف؟',
            'لا توجد بيانات للعرض',
            'جاري التحميل',
            'يرجى الانتظار',
            'البيانات مطلوبة',
            'تنسيق غير صحيح',
            'القيمة غير صالحة',
            'الحقل مطلوب'
        ]
    ]
];

// حساب الإحصائيات
$total_categories = count($discovered_arabic_texts);
$total_texts = 0;
foreach ($discovered_arabic_texts as $category) {
    $total_texts += count($category['texts']);
}

// تحميل ملفات الترجمة للمقارنة
$ar_file = __DIR__ . '/languages/ar/lang.php';
$en_file = __DIR__ . '/languages/en/lang.php';

$ar_translations = file_exists($ar_file) ? require $ar_file : [];
$en_translations = file_exists($en_file) ? require $en_file : [];

$translated_count = count($ar_translations);
$coverage_percentage = $translated_count > 0 ? round(($translated_count / $total_texts) * 100, 1) : 0;

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-gradient-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-pie"></i>
                        تقرير نهائي شامل - جميع النصوص العربية المكتشفة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- الإحصائيات العامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo $total_categories; ?></h2>
                                    <p class="mb-0">فئة رئيسية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo $total_texts; ?></h2>
                                    <p class="mb-0">نص عربي مكتشف</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo $translated_count; ?></h2>
                                    <p class="mb-0">ترجمة متاحة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h2><?php echo $coverage_percentage; ?>%</h2>
                                    <p class="mb-0">نسبة التغطية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- تفاصيل الفئات -->
                    <div class="row">
                        <?php foreach ($discovered_arabic_texts as $category_key => $category): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-<?php echo $category['color']; ?> text-white">
                                    <h6 class="mb-0">
                                        <i class="<?php echo $category['icon']; ?>"></i>
                                        <?php echo $category['title']; ?>
                                        <span class="badge bg-light text-dark ms-2"><?php echo count($category['texts']); ?></span>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($category['texts'] as $index => $text): ?>
                                        <?php if ($index < 5): // عرض أول 5 نصوص فقط ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <span class="text-truncate" style="max-width: 300px;" title="<?php echo htmlspecialchars($text); ?>">
                                                <?php echo htmlspecialchars($text); ?>
                                            </span>
                                            <?php
                                            // فحص وجود ترجمة
                                            $has_translation = false;
                                            foreach ($ar_translations as $key => $translation) {
                                                if ($translation === $text) {
                                                    $has_translation = true;
                                                    break;
                                                }
                                            }
                                            ?>
                                            <?php if ($has_translation): ?>
                                            <span class="badge bg-success">مترجم</span>
                                            <?php else: ?>
                                            <span class="badge bg-danger">غير مترجم</span>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php endforeach; ?>
                                        
                                        <?php if (count($category['texts']) > 5): ?>
                                        <div class="list-group-item text-center">
                                            <small class="text-muted">
                                                و <?php echo count($category['texts']) - 5; ?> نص إضافي...
                                            </small>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- ملخص التوصيات -->
                    <div class="card bg-light">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-lightbulb"></i> ملخص التوصيات</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success">✅ ما تم إنجازه:</h6>
                                    <ul>
                                        <li>اكتشاف <?php echo $total_texts; ?> نص عربي في <?php echo $total_categories; ?> فئات</li>
                                        <li>تصنيف النصوص حسب النوع والاستخدام</li>
                                        <li>إنشاء أدوات ترجمة شاملة ومتقدمة</li>
                                        <li>توفير <?php echo $translated_count; ?> ترجمة جاهزة</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-warning">⚠️ الخطوات التالية:</h6>
                                    <ol>
                                        <li>تطبيق أداة الترجمة الشاملة</li>
                                        <li>مراجعة وتحسين الترجمات</li>
                                        <li>تطبيق أداة الاستبدال الشامل</li>
                                        <li>اختبار شامل للنظام</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أدوات العمل -->
                    <div class="mt-4">
                        <h6>أدوات العمل المتاحة:</h6>
                        <div class="row">
                            <div class="col-md-2">
                                <a href="comprehensive_arabic_translation.php" class="btn btn-gradient-primary w-100 mb-2">
                                    <i class="fas fa-globe-americas"></i> ترجمة شاملة
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="apply_comprehensive_translations.php" class="btn btn-gradient-success w-100 mb-2">
                                    <i class="fas fa-magic"></i> تطبيق شامل
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="scan_arabic_terms.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-search-plus"></i> فحص المصطلحات
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="translate_arabic_terms.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-language"></i> ترجمة المصطلحات
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="review_translations.php" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-check"></i> مراجعة الترجمات
                                </a>
                            </div>
                            <div class="col-md-2">
                                <a href="system_tools.php" class="btn btn-dark w-100 mb-2">
                                    <i class="fas fa-tools"></i> أدوات النظام
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle"></i> معلومات مهمة</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>الملفات المفحوصة:</strong>
                                <ul class="small mb-0">
                                    <li>add_customer.php</li>
                                    <li>customers.php</li>
                                    <li>sales.php</li>
                                    <li>purchases.php</li>
                                    <li>وملفات أخرى...</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <strong>أنواع النصوص:</strong>
                                <ul class="small mb-0">
                                    <li>تعليقات الكود</li>
                                    <li>رسائل النظام</li>
                                    <li>عناصر الواجهة</li>
                                    <li>رسائل قاعدة البيانات</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <strong>الحالة الحالية:</strong>
                                <ul class="small mb-0">
                                    <li>تم اكتشاف جميع النصوص</li>
                                    <li>تم تصنيفها وتنظيمها</li>
                                    <li>جاهزة للترجمة والتطبيق</li>
                                    <li>أدوات متقدمة متاحة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
