<?php
/**
 * ملف لإصلاح مشاكل جدول العملاء
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$fix_results = [];
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_table'])) {
    try {
        // فحص وجود جدول العملاء
        $check_table = $db->query("SHOW TABLES LIKE 'customers'");
        $table_exists = ($check_table && $check_table->num_rows > 0);
        
        if (!$table_exists) {
            // إنشاء جدول العملاء
            $create_table_sql = "
                CREATE TABLE customers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    phone VARCHAR(20) DEFAULT NULL,
                    email VARCHAR(100) DEFAULT NULL,
                    tax_number VARCHAR(50) DEFAULT NULL,
                    address TEXT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            if ($db->query($create_table_sql)) {
                $fix_results[] = "تم إنشاء جدول العملاء بنجاح";
            } else {
                $errors[] = "فشل في إنشاء جدول العملاء: " . $db->error;
            }
        } else {
            $fix_results[] = "جدول العملاء موجود بالفعل";
            
            // فحص بنية الجدول
            $structure = $db->query("DESCRIBE customers");
            $columns = [];
            while ($row = $structure->fetch_assoc()) {
                $columns[$row['Field']] = $row;
            }
            
            $required_columns = [
                'id' => "INT AUTO_INCREMENT PRIMARY KEY",
                'name' => "VARCHAR(100) NOT NULL",
                'phone' => "VARCHAR(20) DEFAULT NULL",
                'email' => "VARCHAR(100) DEFAULT NULL",
                'tax_number' => "VARCHAR(50) DEFAULT NULL",
                'address' => "TEXT DEFAULT NULL"
            ];
            
            // إضافة الأعمدة المفقودة
            foreach ($required_columns as $column => $definition) {
                if (!isset($columns[$column])) {
                    $alter_sql = "ALTER TABLE customers ADD COLUMN $column $definition";
                    if ($db->query($alter_sql)) {
                        $fix_results[] = "تم إضافة العمود: $column";
                    } else {
                        $errors[] = "فشل في إضافة العمود $column: " . $db->error;
                    }
                }
            }
            
            // إضافة أعمدة التوقيت إذا لم تكن موجودة
            if (!isset($columns['created_at'])) {
                $alter_sql = "ALTER TABLE customers ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
                if ($db->query($alter_sql)) {
                    $fix_results[] = "تم إضافة عمود created_at";
                } else {
                    $errors[] = "فشل في إضافة عمود created_at: " . $db->error;
                }
            }
            
            if (!isset($columns['updated_at'])) {
                $alter_sql = "ALTER TABLE customers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
                if ($db->query($alter_sql)) {
                    $fix_results[] = "تم إضافة عمود updated_at";
                } else {
                    $errors[] = "فشل في إضافة عمود updated_at: " . $db->error;
                }
            }
        }
        
        // إضافة فهرس على اسم العميل لتحسين الأداء
        $index_check = $db->query("SHOW INDEX FROM customers WHERE Key_name = 'idx_customer_name'");
        if (!$index_check || $index_check->num_rows == 0) {
            $index_sql = "ALTER TABLE customers ADD INDEX idx_customer_name (name)";
            if ($db->query($index_sql)) {
                $fix_results[] = "تم إضافة فهرس على اسم العميل";
            } else {
                $errors[] = "فشل في إضافة الفهرس: " . $db->error;
            }
        }
        
        // إضافة بيانات تجريبية إذا كان الجدول فارغاً
        $count_result = $db->query("SELECT COUNT(*) as count FROM customers");
        $customer_count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
        
        if ($customer_count == 0 && isset($_POST['add_sample_customers'])) {
            $sample_customers = [
                ['أحمد محمد', '0501234567', '<EMAIL>', '123456789', 'الرياض، المملكة العربية السعودية'],
                ['فاطمة علي', '0509876543', '<EMAIL>', '987654321', 'جدة، المملكة العربية السعودية'],
                ['محمد عبدالله', '0512345678', '<EMAIL>', '*********', 'الدمام، المملكة العربية السعودية'],
                ['نورا سعد', '0556789012', '<EMAIL>', '*********', 'مكة المكرمة، المملكة العربية السعودية'],
                ['خالد أحمد', '0543210987', '<EMAIL>', '*********', 'المدينة المنورة، المملكة العربية السعودية']
            ];

            $stmt = $db->prepare("INSERT INTO customers (name, phone, email, tax_number, address) VALUES (?, ?, ?, ?, ?)");
            $added_count = 0;

            foreach ($sample_customers as $customer) {
                $stmt->bind_param("sssss", $customer[0], $customer[1], $customer[2], $customer[3], $customer[4]);
                if ($stmt->execute()) {
                    $added_count++;
                }
            }
            
            if ($added_count > 0) {
                $fix_results[] = "تم إضافة $added_count عملاء تجريبيين";
            }
        }
        
        if (empty($errors)) {
            $_SESSION['success'] = "تم إصلاح جدول العملاء بنجاح!";
        } else {
            $_SESSION['error'] = "حدثت بعض الأخطاء أثناء الإصلاح";
        }
        
    } catch (Exception $e) {
        $errors[] = "حدث خطأ أثناء إصلاح الجدول: " . $e->getMessage();
        $_SESSION['error'] = "فشل في إصلاح جدول العملاء";
    }
}

// فحص حالة الجدول الحالية
$table_status = [];
try {
    $check_table = $db->query("SHOW TABLES LIKE 'customers'");
    $table_status['exists'] = ($check_table && $check_table->num_rows > 0);
    
    if ($table_status['exists']) {
        // فحص بنية الجدول
        $structure = $db->query("DESCRIBE customers");
        $table_status['columns'] = [];
        while ($row = $structure->fetch_assoc()) {
            $table_status['columns'][] = $row['Field'];
        }
        
        // عدد العملاء
        $count_result = $db->query("SELECT COUNT(*) as count FROM customers");
        $table_status['count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
        
        // فحص الفهارس
        $indexes = $db->query("SHOW INDEX FROM customers");
        $table_status['indexes'] = [];
        while ($row = $indexes->fetch_assoc()) {
            $table_status['indexes'][] = $row['Key_name'];
        }
    }
} catch (Exception $e) {
    $table_status['error'] = $e->getMessage();
}

displayMessages();
?>

<div class="container mt-4">
    <h2>إصلاح جدول العملاء</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات</h5>
        <p>هذه الأداة تقوم بفحص وإصلاح جدول العملاء في قاعدة البيانات. سيتم:</p>
        <ul>
            <li>إنشاء جدول العملاء إذا لم يكن موجوداً</li>
            <li>إضافة الأعمدة المفقودة</li>
            <li>إضافة الفهارس لتحسين الأداء</li>
            <li>إضافة بيانات تجريبية (اختياري)</li>
        </ul>
    </div>
    
    <?php if (!empty($fix_results)): ?>
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-check-circle"></i>
                الإصلاحات المطبقة
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($fix_results as $result): ?>
                <li class="list-group-item">
                    <i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($result); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($errors)): ?>
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle"></i>
                أخطاء حدثت أثناء الإصلاح
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($errors as $error): ?>
                <li class="list-group-item list-group-item-danger">
                    <i class="fas fa-times text-danger"></i> <?php echo htmlspecialchars($error); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- حالة الجدول الحالية -->
    <div class="card mb-4">
        <div class="card-header <?php echo isset($table_status['exists']) && $table_status['exists'] ? 'bg-success' : 'bg-warning'; ?> text-white">
            <h5 class="mb-0">
                <i class="fas fa-database"></i>
                حالة جدول العملاء
            </h5>
        </div>
        <div class="card-body">
            <?php if (isset($table_status['error'])): ?>
                <div class="alert alert-danger">
                    خطأ في فحص الجدول: <?php echo htmlspecialchars($table_status['error']); ?>
                </div>
            <?php elseif (isset($table_status['exists']) && $table_status['exists']): ?>
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الجدول:</h6>
                        <ul>
                            <li><strong>الحالة:</strong> <span class="badge bg-success">موجود</span></li>
                            <li><strong>عدد العملاء:</strong> <?php echo $table_status['count']; ?></li>
                            <li><strong>عدد الأعمدة:</strong> <?php echo count($table_status['columns']); ?></li>
                            <li><strong>عدد الفهارس:</strong> <?php echo count(array_unique($table_status['indexes'])); ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الأعمدة الموجودة:</h6>
                        <div class="d-flex flex-wrap">
                            <?php foreach ($table_status['columns'] as $column): ?>
                                <span class="badge bg-info me-1 mb-1"><?php echo $column; ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> جدول العملاء غير موجود
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- نموذج الإصلاح -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">إصلاح الجدول</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="add_sample_customers" name="add_sample_customers" value="1">
                        <label class="form-check-label" for="add_sample_customers">
                            إضافة عملاء تجريبيين (5 عملاء) إذا كان الجدول فارغاً
                        </label>
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <strong>تحذير:</strong> سيتم تعديل بنية قاعدة البيانات. تأكد من وجود نسخة احتياطية قبل المتابعة.
                </div>
                
                <button type="submit" name="fix_table" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من إصلاح جدول العملاء؟')">
                    <i class="fas fa-tools"></i> إصلاح جدول العملاء
                </button>
                
                <a href="test_ajax_customer.php" class="btn btn-success">
                    <i class="fas fa-vial"></i> اختبار إضافة العميل
                </a>
                
                <a href="customers.php" class="btn btn-info">
                    <i class="fas fa-users"></i> قائمة العملاء
                </a>
                
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                </a>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
