<?php
/**
 * أداة إصلاح مشاكل "Commands out of sync" في MySQLi
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_sync_errors'])) {
    $files_fixed = 0;
    $errors = [];
    
    try {
        // قائمة الملفات التي تحتاج إصلاح
        $files_to_fix = [
            'delete_customer.php',
            'delete_sale.php', 
            'delete_purchase.php',
            'edit_customer.php',
            'edit_sale.php',
            'edit_purchase.php',
            'add_customer.php',
            'add_sale.php',
            'add_purchase.php',
            'customers.php',
            'sales.php',
            'purchases.php',
            'ajax_handler.php'
        ];
        
        foreach ($files_to_fix as $file) {
            $filepath = __DIR__ . '/' . $file;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                $original_content = $content;
                $file_modified = false;
                
                // إصلاح 1: إضافة إغلاق statements
                if (preg_match_all('/(\$\w+)\s*=\s*\$db->prepare\([^;]+;/', $content, $matches)) {
                    foreach ($matches[1] as $stmt_var) {
                        // البحث عن استخدام المتغير بدون إغلاق
                        $pattern = '/(' . preg_quote($stmt_var, '/') . '->(?:bind_param|execute|fetch|get_result)[^;]*;)(?!\s*' . preg_quote($stmt_var, '/') . '->close\(\))/';
                        if (preg_match($pattern, $content)) {
                            // إضافة close() بعد آخر استخدام
                            $content = preg_replace(
                                '/(' . preg_quote($stmt_var, '/') . '->(?:bind_param|execute|fetch|get_result)[^;]*;)(\s*)(?=' . preg_quote($stmt_var, '/') . '\s*=\s*\$db->prepare|$)/',
                                '$1$2' . $stmt_var . '->close();$2',
                                $content
                            );
                            $file_modified = true;
                        }
                    }
                }
                
                // إصلاح 2: استبدال bind_result + fetch بـ get_result
                $content = preg_replace(
                    '/(\$\w+)->bind_result\(([^)]+)\);\s*\1->fetch\(\);/',
                    '$result = $1->get_result(); $row = $result->fetch_assoc(); extract($row);',
                    $content
                );
                
                // إصلاح 3: إضافة try-catch للمعاملات
                if (strpos($content, '$db->prepare') !== false && strpos($content, 'try {') === false) {
                    $content = preg_replace(
                        '/(require_once[^;]+;[\s\n]*redirectIfNotLoggedIn\(\);[\s\n]*)/',
                        '$1' . PHP_EOL . 'try {' . PHP_EOL,
                        $content
                    );
                    
                    $content = preg_replace(
                        '/(header\("Location:[^"]+"\);[\s\n]*exit\(\);[\s\n]*\?>)/',
                        '} catch (mysqli_sql_exception $e) {' . PHP_EOL . 
                        '    $_SESSION[\'error\'] = "خطأ في قاعدة البيانات: " . $e->getMessage();' . PHP_EOL .
                        '    error_log("Database error in ' . $file . ': " . $e->getMessage());' . PHP_EOL .
                        '} catch (Exception $e) {' . PHP_EOL .
                        '    $_SESSION[\'error\'] = "حدث خطأ غير متوقع: " . $e->getMessage();' . PHP_EOL .
                        '    error_log("General error in ' . $file . ': " . $e->getMessage());' . PHP_EOL .
                        '}' . PHP_EOL . PHP_EOL . '$1',
                        $content
                    );
                    $file_modified = true;
                }
                
                // إصلاح 4: إضافة فحص الاتصال
                if (strpos($content, '$db = getCurrentUserDB();') !== false && strpos($content, '$db->connect_error') === false) {
                    $content = str_replace(
                        '$db = getCurrentUserDB();',
                        '$db = getCurrentUserDB();' . PHP_EOL . 
                        'if ($db === null || $db->connect_error) {' . PHP_EOL .
                        '    $_SESSION[\'error\'] = "خطأ في الاتصال بقاعدة البيانات";' . PHP_EOL .
                        '    header("Location: index.php");' . PHP_EOL .
                        '    exit();' . PHP_EOL .
                        '}',
                        $content
                    );
                    $file_modified = true;
                }
                
                // حفظ الملف إذا تم تعديله
                if ($file_modified || $content !== $original_content) {
                    if (file_put_contents($filepath, $content)) {
                        $files_fixed++;
                    } else {
                        $errors[] = "فشل في حفظ ملف $file";
                    }
                }
            }
        }
        
        if ($files_fixed > 0) {
            $_SESSION['success'] = "تم إصلاح $files_fixed ملف من مشاكل MySQLi sync";
        } else {
            $_SESSION['info'] = "لا توجد ملفات تحتاج إصلاح";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء الإصلاح: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-sync-alt"></i>
                        إصلاح مشاكل "Commands out of sync" في MySQLi
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول المشكلة</h6>
                        <p>خطأ "Commands out of sync" يحدث عندما:</p>
                        <ul>
                            <li>لا يتم إغلاق prepared statements بعد الاستخدام</li>
                            <li>محاولة تنفيذ استعلام جديد قبل قراءة نتائج الاستعلام السابق</li>
                            <li>عدم استخدام <code>get_result()</code> بدلاً من <code>bind_result()</code></li>
                            <li>عدم معالجة الأخطاء بشكل صحيح</li>
                        </ul>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">المشاكل الشائعة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-times text-danger"></i> عدم إغلاق statements</li>
                                        <li><i class="fas fa-times text-danger"></i> استخدام bind_result + fetch</li>
                                        <li><i class="fas fa-times text-danger"></i> عدم معالجة الأخطاء</li>
                                        <li><i class="fas fa-times text-danger"></i> تداخل الاستعلامات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الإصلاحات المطبقة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> إضافة close() للـ statements</li>
                                        <li><i class="fas fa-check text-success"></i> استخدام get_result()</li>
                                        <li><i class="fas fa-check text-success"></i> إضافة try-catch</li>
                                        <li><i class="fas fa-check text-success"></i> فحص الاتصال</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                        <p>هذه الأداة ستقوم بتعديل ملفات PHP متعددة. يُنصح بعمل نسخة احتياطية قبل التطبيق.</p>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من إصلاح مشاكل MySQLi sync؟')">
                        <div class="text-center">
                            <button type="submit" name="fix_sync_errors" class="btn btn-warning btn-lg">
                                <i class="fas fa-sync-alt"></i> إصلاح مشاكل MySQLi Sync
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h6>أمثلة على الإصلاحات:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">قبل الإصلاح:</h6>
                                <pre class="bg-light p-3"><code>$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->bind_param("i", $id);
$stmt->execute();
$stmt->bind_result($name);
$stmt->fetch();

$stmt = $db->prepare("DELETE FROM users WHERE id = ?");</code></pre>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">بعد الإصلاح:</h6>
                                <pre class="bg-light p-3"><code>$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();
$stmt->close();

$stmt = $db->prepare("DELETE FROM users WHERE id = ?");</code></pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>الملفات التي سيتم إصلاحها:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <ul>
                                    <li>delete_customer.php</li>
                                    <li>delete_sale.php</li>
                                    <li>delete_purchase.php</li>
                                    <li>edit_customer.php</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul>
                                    <li>edit_sale.php</li>
                                    <li>edit_purchase.php</li>
                                    <li>add_customer.php</li>
                                    <li>add_sale.php</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul>
                                    <li>add_purchase.php</li>
                                    <li>customers.php</li>
                                    <li>sales.php</li>
                                    <li>purchases.php</li>
                                    <li>ajax_handler.php</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
