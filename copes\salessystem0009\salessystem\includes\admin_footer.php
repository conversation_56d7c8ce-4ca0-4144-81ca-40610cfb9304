    <!-- Footer -->
    <footer class="bg-white sticky-footer mt-5">
        <div class="container my-auto">
            <div class="copyright text-center my-auto">
                <span>حقوق الطبع والنشر &copy; نظام إدارة المبيعات <?php echo date('Y'); ?> - لوحة تحكم المدير</span>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Admin Scripts -->
    <script>
        // تفعيل القوائم المنسدلة
        document.addEventListener('DOMContentLoaded', function() {
            // تفعيل جميع القوائم المنسدلة
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
        });

        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تفعيل popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // إخفاء الرسائل تلقائياً بعد 5 ثوان
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // تأكيد العمليات الحساسة
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('confirm-action')) {
                e.preventDefault();
                var message = e.target.getAttribute('data-message') || 'هل أنت متأكد من هذا الإجراء؟';
                if (confirm(message)) {
                    window.location.href = e.target.href;
                }
            }
        });

        // تحديث الوقت الحالي
        function updateCurrentTime() {
            var now = new Date();
            var timeString = now.toLocaleTimeString('ar-SA');
            var dateString = now.toLocaleDateString('ar-SA');
            
            var timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString + ' - ' + dateString;
            }
        }

        // تحديث الوقت كل ثانية
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        // دالة لتصدير البيانات
        function exportData(format, url) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            
            var formatInput = document.createElement('input');
            formatInput.type = 'hidden';
            formatInput.name = 'export_format';
            formatInput.value = format;
            
            form.appendChild(formatInput);
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        // دالة لتحديث الإحصائيات
        function refreshStats() {
            fetch('admin_ajax.php?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث الإحصائيات في الصفحة
                        Object.keys(data.stats).forEach(key => {
                            var element = document.getElementById('stat-' + key);
                            if (element) {
                                element.textContent = data.stats[key];
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث الإحصائيات:', error);
                });
        }

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(refreshStats, 30000);

        // دالة للبحث السريع
        function quickSearch(query) {
            if (query.length < 2) {
                document.getElementById('search-results').innerHTML = '';
                return;
            }

            fetch('admin_ajax.php?action=quick_search&q=' + encodeURIComponent(query))
                .then(response => response.json())
                .then(data => {
                    var resultsHtml = '';
                    if (data.success && data.results.length > 0) {
                        data.results.forEach(result => {
                            resultsHtml += `
                                <div class="search-result-item">
                                    <a href="${result.url}" class="text-decoration-none">
                                        <i class="${result.icon} me-2"></i>
                                        ${result.title}
                                        <small class="text-muted d-block">${result.description}</small>
                                    </a>
                                </div>
                            `;
                        });
                    } else {
                        resultsHtml = '<div class="text-muted p-2">لا توجد نتائج</div>';
                    }
                    document.getElementById('search-results').innerHTML = resultsHtml;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                });
        }

        // إعداد البحث السريع
        var searchInput = document.getElementById('quick-search');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                quickSearch(this.value);
            });
        }
    </script>

    <style>
        .sticky-footer {
            position: sticky;
            bottom: 0;
            z-index: 1020;
        }

        .search-result-item {
            padding: 0.5rem;
            border-bottom: 1px solid #e3e6f0;
        }

        .search-result-item:hover {
            background-color: #f8f9fc;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .confirm-action {
            cursor: pointer;
        }

        .badge-counter {
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 0.7rem;
        }

        .dropdown-menu {
            max-height: 300px;
            overflow-y: auto;
        }

        .card-hover {
            transition: transform 0.2s ease-in-out;
        }

        .card-hover:hover {
            transform: translateY(-2px);
        }

        .text-xs {
            font-size: 0.7rem;
        }

        .font-weight-bold {
            font-weight: 700 !important;
        }

        .text-gray-800 {
            color: #5a5c69 !important;
        }

        .text-gray-300 {
            color: #dddfeb !important;
        }

        .no-gutters {
            margin-right: 0;
            margin-left: 0;
        }

        .no-gutters > .col,
        .no-gutters > [class*="col-"] {
            padding-right: 0;
            padding-left: 0;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -250px;
                width: 250px;
                height: calc(100vh - 56px);
                transition: left 0.3s ease;
                z-index: 1000;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0 !important;
            }
        }
    </style>

</body>
</html>
