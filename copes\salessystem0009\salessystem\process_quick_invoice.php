<?php
/**
 * معالجة الفواتير السريعة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "طريقة إرسال غير صحيحة";
    header("Location: index.php");
    exit();
}

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات";
    header("Location: index.php");
    exit();
}

try {
    // استلام البيانات
    $invoice_type = $_POST['invoice_type'] ?? '';
    $customer_id = $_POST['customer_id'] ?? '';
    $date = $_POST['date'] ?? date('Y-m-d');
    $notes = $_POST['notes'] ?? '';

    // استلام بيانات المنتجات
    $product_ids = $_POST['product_id'] ?? [];
    $quantities = $_POST['quantity'] ?? [];
    $prices = $_POST['price'] ?? [];
    $tax_rates = $_POST['tax_rate'] ?? [];

    // التحقق من البيانات الأساسية
    if (empty($invoice_type) || !in_array($invoice_type, ['sale', 'purchase'])) {
        throw new Exception("نوع الفاتورة غير صحيح");
    }

    if (empty($customer_id)) {
        throw new Exception("يجب اختيار العميل");
    }

    if (empty($product_ids) || !is_array($product_ids)) {
        throw new Exception("يجب إضافة عنصر واحد على الأقل");
    }

    // التحقق من صحة الأصناف
    $valid_items = [];
    $subtotal = 0;

    for ($i = 0; $i < count($product_ids); $i++) {
        $product_id = intval($product_ids[$i] ?? 0);
        $quantity = floatval($quantities[$i] ?? 0);
        $price = floatval($prices[$i] ?? 0);
        $tax_rate = floatval($tax_rates[$i] ?? 15);

        if ($product_id <= 0 || $quantity <= 0 || $price < 0) {
            continue; // تجاهل الأصناف غير الصحيحة
        }

        // جلب بيانات المنتج من قاعدة البيانات
        $product_stmt = $db->prepare("SELECT name, category FROM products WHERE id = ?");
        $product_stmt->bind_param("i", $product_id);
        $product_stmt->execute();
        $product_result = $product_stmt->get_result();

        if ($product_result->num_rows === 0) {
            continue; // تجاهل المنتجات غير الموجودة
        }

        $product_data = $product_result->fetch_assoc();
        $product_name = $product_data['name'];
        $product_category = $product_data['category'];
        $product_stmt->close();

        $item_total = $quantity * $price;
        $subtotal += $item_total;

        $valid_items[] = [
            'product_id' => $product_id,
            'product_name' => $product_name,
            'product_category' => $product_category,
            'quantity' => $quantity,
            'price' => $price,
            'tax_rate' => $tax_rate,
            'total' => $item_total
        ];
    }

    if (empty($valid_items)) {
        throw new Exception("لا توجد عناصر صحيحة");
    }

    // حساب الإجماليات (الضريبة محسوبة لكل عنصر على حدة)
    $tax_amount = 0;
    foreach ($valid_items as $item) {
        $item_subtotal = $item['quantity'] * $item['price'];
        $item_tax = $item_subtotal * ($item['tax_rate'] / 100);
        $tax_amount += $item_tax;
    }

    $total_amount = $subtotal + $tax_amount;

    // توليد رقم فاتورة إذا لم يكن موجود
    if (empty($invoice_number)) {
        $prefix = $invoice_type === 'sale' ? 'S' : 'P';
        $invoice_number = $prefix . date('YmdHis') . rand(100, 999);
    }

    // بدء المعاملة
    $db->begin_transaction();

    // تحديد الجدول المناسب
    $table = $invoice_type === 'sale' ? 'sales' : 'purchases';
    $items_table = $invoice_type === 'sale' ? 'sale_items' : 'purchase_items';

    // إدراج الفاتورة الرئيسية
    $stmt = $db->prepare("INSERT INTO $table (customer_id, invoice_number, date, subtotal, tax_amount, total_amount, notes, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");

    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام الفاتورة: " . $db->error);
    }

    $stmt->bind_param("issddds", $customer_id, $invoice_number, $date, $subtotal, $tax_amount, $total_amount, $notes);
    
    if (!$stmt->execute()) {
        throw new Exception("خطأ في حفظ الفاتورة: " . $stmt->error);
    }

    $invoice_id = $db->insert_id;
    $stmt->close();

    // إدراج أصناف الفاتورة
    if ($invoice_type === 'sale') {
        $item_stmt = $db->prepare("INSERT INTO $items_table (sale_id, product_id, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?)");
    } else {
        $item_stmt = $db->prepare("INSERT INTO $items_table (purchase_id, product_name, quantity, price, total) VALUES (?, ?, ?, ?, ?)");
    }

    if (!$item_stmt) {
        throw new Exception("خطأ في إعداد استعلام الأصناف: " . $db->error);
    }

    foreach ($valid_items as $item) {
        if ($invoice_type === 'sale') {
            $item_tax_amount = ($item['quantity'] * $item['price']) * ($item['tax_rate'] / 100);
            $item_total_price = ($item['quantity'] * $item['price']) + $item_tax_amount;

            $item_stmt->bind_param("iiidddd", $invoice_id, $item['product_id'], $item['quantity'], $item['price'], $item['tax_rate'], $item_tax_amount, $item_total_price);
        } else {
            $item_stmt->bind_param("isddd", $invoice_id, $item['product_name'], $item['quantity'], $item['price'], $item['total']);
        }

        if (!$item_stmt->execute()) {
            throw new Exception("خطأ في حفظ العنصر: " . $item_stmt->error);
        }
    }

    $item_stmt->close();

    // تأكيد المعاملة
    $db->commit();

    // رسالة نجاح
    $invoice_type_ar = $invoice_type === 'sale' ? 'المبيعات' : 'المشتريات';
    $_SESSION['success'] = "تم حفظ فاتورة $invoice_type_ar بنجاح. رقم الفاتورة: $invoice_number";

    // إعادة التوجيه لعرض الفاتورة
    $view_page = $invoice_type === 'sale' ? 'view_sale.php' : 'view_purchase.php';
    header("Location: $view_page?id=$invoice_id");
    exit();

} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if (isset($db) && $db->connect_error === null) {
        $db->rollback();
    }

    $_SESSION['error'] = "خطأ في حفظ الفاتورة: " . $e->getMessage();
    header("Location: index.php");
    exit();

} finally {
    // إغلاق الاتصال
    if (isset($db)) {
        $db->close();
    }
}
?>
