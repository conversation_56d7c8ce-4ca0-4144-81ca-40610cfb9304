<?php
/**
 * أداة مراجعة وإصلاح الترجمات الشاملة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// قائمة النصوص المكتوبة مباشرة في الكود التي تحتاج ترجمة
$hardcoded_texts = [
    // من add_sale.php
    'يجب إضافة عنصر واحد على الأقل' => [
        'ar' => 'يجب إضافة عنصر واحد على الأقل',
        'en' => 'At least one item must be added',
        'key' => 'at_least_one_item_required'
    ],
    'تم إضافة فاتورة المبيعات بنجاح' => [
        'ar' => 'تم إضافة فاتورة المبيعات بنجاح',
        'en' => 'Sales invoice added successfully',
        'key' => 'sale_invoice_added_successfully'
    ],
    'حدث خطأ أثناء إضافة فاتورة المبيعات' => [
        'ar' => 'حدث خطأ أثناء إضافة فاتورة المبيعات',
        'en' => 'Error occurred while adding sales invoice',
        'key' => 'error_adding_sale_invoice'
    ],
    'إضافة فاتورة مبيعات' => [
        'ar' => 'إضافة فاتورة مبيعات',
        'en' => 'Add Sales Invoice',
        'key' => 'add_sales_invoice'
    ],
    'العميل' => [
        'ar' => 'العميل',
        'en' => 'Customer',
        'key' => 'customer'
    ],
    '-- اختر عميل --' => [
        'ar' => '-- اختر عميل --',
        'en' => '-- Select Customer --',
        'key' => 'select_customer'
    ],
    '-- إضافة عميل جديد --' => [
        'ar' => '-- إضافة عميل جديد --',
        'en' => '-- Add New Customer --',
        'key' => 'add_new_customer_option'
    ],
    'إضافة عنصر' => [
        'ar' => 'إضافة عنصر',
        'en' => 'Add Item',
        'key' => 'add_item'
    ],
    'حفظ الفاتورة' => [
        'ar' => 'حفظ الفاتورة',
        'en' => 'Save Invoice',
        'key' => 'save_invoice'
    ],
    'إلغاء' => [
        'ar' => 'إلغاء',
        'en' => 'Cancel',
        'key' => 'cancel'
    ],
    '-- اختر منتج --' => [
        'ar' => '-- اختر منتج --',
        'en' => '-- Select Product --',
        'key' => 'select_product'
    ],
    '-- إضافة منتج جديد --' => [
        'ar' => '-- إضافة منتج جديد --',
        'en' => '-- Add New Product --',
        'key' => 'add_new_product_option'
    ],
    'تم إضافة المنتج بنجاح' => [
        'ar' => 'تم إضافة المنتج بنجاح',
        'en' => 'Product added successfully',
        'key' => 'product_added_successfully'
    ],
    'حدث خطأ أثناء إضافة المنتج' => [
        'ar' => 'حدث خطأ أثناء إضافة المنتج',
        'en' => 'Error occurred while adding product',
        'key' => 'error_adding_product'
    ],
    'يرجى إدخال اسم العميل' => [
        'ar' => 'يرجى إدخال اسم العميل',
        'en' => 'Please enter customer name',
        'key' => 'please_enter_customer_name'
    ],
    'اسم العميل يجب أن يكون أكثر من حرف واحد' => [
        'ar' => 'اسم العميل يجب أن يكون أكثر من حرف واحد',
        'en' => 'Customer name must be more than one character',
        'key' => 'customer_name_min_length'
    ],
    'تم إضافة العميل بنجاح' => [
        'ar' => 'تم إضافة العميل بنجاح',
        'en' => 'Customer added successfully',
        'key' => 'customer_added_successfully'
    ],
    'حدث خطأ أثناء إضافة العميل' => [
        'ar' => 'حدث خطأ أثناء إضافة العميل',
        'en' => 'Error occurred while adding customer',
        'key' => 'error_adding_customer'
    ],
    'حدث خطأ في الشبكة أثناء إضافة العميل' => [
        'ar' => 'حدث خطأ في الشبكة أثناء إضافة العميل. يرجى المحاولة مرة أخرى.',
        'en' => 'Network error occurred while adding customer. Please try again.',
        'key' => 'network_error_adding_customer'
    ],
    
    // مصطلحات إضافية من الجداول والحقول
    'المنتج' => [
        'ar' => 'المنتج',
        'en' => 'Product',
        'key' => 'product'
    ],
    'الكمية' => [
        'ar' => 'الكمية',
        'en' => 'Quantity',
        'key' => 'quantity'
    ],
    'السعر' => [
        'ar' => 'السعر',
        'en' => 'Price',
        'key' => 'price'
    ],
    'الضريبة' => [
        'ar' => 'الضريبة',
        'en' => 'Tax',
        'key' => 'tax'
    ],
    'المجموع' => [
        'ar' => 'المجموع',
        'en' => 'Total',
        'key' => 'total'
    ],
    'إجراءات' => [
        'ar' => 'إجراءات',
        'en' => 'Actions',
        'key' => 'actions'
    ],
    'حذف' => [
        'ar' => 'حذف',
        'en' => 'Delete',
        'key' => 'delete'
    ],
    'تعديل' => [
        'ar' => 'تعديل',
        'en' => 'Edit',
        'key' => 'edit'
    ],
    'عرض' => [
        'ar' => 'عرض',
        'en' => 'View',
        'key' => 'view'
    ],
    'طباعة' => [
        'ar' => 'طباعة',
        'en' => 'Print',
        'key' => 'print'
    ],
    'رقم الفاتورة' => [
        'ar' => 'رقم الفاتورة',
        'en' => 'Invoice Number',
        'key' => 'invoice_number'
    ],
    'التاريخ' => [
        'ar' => 'التاريخ',
        'en' => 'Date',
        'key' => 'date'
    ],
    'الاسم' => [
        'ar' => 'الاسم',
        'en' => 'Name',
        'key' => 'name'
    ],
    'الهاتف' => [
        'ar' => 'الهاتف',
        'en' => 'Phone',
        'key' => 'phone'
    ],
    'البريد الإلكتروني' => [
        'ar' => 'البريد الإلكتروني',
        'en' => 'Email',
        'key' => 'email'
    ],
    'العنوان' => [
        'ar' => 'العنوان',
        'en' => 'Address',
        'key' => 'address'
    ],
    'الرقم الضريبي' => [
        'ar' => 'الرقم الضريبي',
        'en' => 'Tax Number',
        'key' => 'tax_number'
    ],
    'ملاحظات' => [
        'ar' => 'ملاحظات',
        'en' => 'Notes',
        'key' => 'notes'
    ],
    'المجموع الفرعي' => [
        'ar' => 'المجموع الفرعي',
        'en' => 'Subtotal',
        'key' => 'subtotal'
    ],
    'قيمة الضريبة' => [
        'ar' => 'قيمة الضريبة',
        'en' => 'Tax Amount',
        'key' => 'tax_amount'
    ],
    'المبلغ الإجمالي' => [
        'ar' => 'المبلغ الإجمالي',
        'en' => 'Total Amount',
        'key' => 'total_amount'
    ],
    'نسبة الضريبة' => [
        'ar' => 'نسبة الضريبة',
        'en' => 'Tax Rate',
        'key' => 'tax_rate'
    ],
    'سعر الوحدة' => [
        'ar' => 'سعر الوحدة',
        'en' => 'Unit Price',
        'key' => 'unit_price'
    ],
    'وصف المنتج' => [
        'ar' => 'وصف المنتج',
        'en' => 'Product Description',
        'key' => 'product_description'
    ],
    
    // مصطلحات النظام والواجهة
    'بحث' => [
        'ar' => 'بحث',
        'en' => 'Search',
        'key' => 'search'
    ],
    'تصفية' => [
        'ar' => 'تصفية',
        'en' => 'Filter',
        'key' => 'filter'
    ],
    'تصدير' => [
        'ar' => 'تصدير',
        'en' => 'Export',
        'key' => 'export'
    ],
    'استيراد' => [
        'ar' => 'استيراد',
        'en' => 'Import',
        'key' => 'import'
    ],
    'نسخ احتياطي' => [
        'ar' => 'نسخ احتياطي',
        'en' => 'Backup',
        'key' => 'backup'
    ],
    'استعادة' => [
        'ar' => 'استعادة',
        'en' => 'Restore',
        'key' => 'restore'
    ],
    'إعدادات' => [
        'ar' => 'إعدادات',
        'en' => 'Settings',
        'key' => 'settings'
    ],
    'تقارير' => [
        'ar' => 'تقارير',
        'en' => 'Reports',
        'key' => 'reports'
    ],
    'إحصائيات' => [
        'ar' => 'إحصائيات',
        'en' => 'Statistics',
        'key' => 'statistics'
    ],
    'لوحة التحكم' => [
        'ar' => 'لوحة التحكم',
        'en' => 'Dashboard',
        'key' => 'dashboard'
    ],
    'الصفحة الرئيسية' => [
        'ar' => 'الصفحة الرئيسية',
        'en' => 'Home Page',
        'key' => 'home_page'
    ],
    'تسجيل الدخول' => [
        'ar' => 'تسجيل الدخول',
        'en' => 'Login',
        'key' => 'login'
    ],
    'تسجيل الخروج' => [
        'ar' => 'تسجيل الخروج',
        'en' => 'Logout',
        'key' => 'logout'
    ],
    'الملف الشخصي' => [
        'ar' => 'الملف الشخصي',
        'en' => 'Profile',
        'key' => 'profile'
    ],
    'كلمة المرور' => [
        'ar' => 'كلمة المرور',
        'en' => 'Password',
        'key' => 'password'
    ],
    'اسم المستخدم' => [
        'ar' => 'اسم المستخدم',
        'en' => 'Username',
        'key' => 'username'
    ],
    'تأكيد كلمة المرور' => [
        'ar' => 'تأكيد كلمة المرور',
        'en' => 'Confirm Password',
        'key' => 'confirm_password'
    ],
    'حفظ' => [
        'ar' => 'حفظ',
        'en' => 'Save',
        'key' => 'save'
    ],
    'إغلاق' => [
        'ar' => 'إغلاق',
        'en' => 'Close',
        'key' => 'close'
    ],
    'تحديث' => [
        'ar' => 'تحديث',
        'en' => 'Update',
        'key' => 'update'
    ],
    'إضافة' => [
        'ar' => 'إضافة',
        'en' => 'Add',
        'key' => 'add'
    ],
    'جديد' => [
        'ar' => 'جديد',
        'en' => 'New',
        'key' => 'new'
    ],
    'قديم' => [
        'ar' => 'قديم',
        'en' => 'Old',
        'key' => 'old'
    ],
    'نشط' => [
        'ar' => 'نشط',
        'en' => 'Active',
        'key' => 'active'
    ],
    'غير نشط' => [
        'ar' => 'غير نشط',
        'en' => 'Inactive',
        'key' => 'inactive'
    ],
    'مفعل' => [
        'ar' => 'مفعل',
        'en' => 'Enabled',
        'key' => 'enabled'
    ],
    'معطل' => [
        'ar' => 'معطل',
        'en' => 'Disabled',
        'key' => 'disabled'
    ],
    'نعم' => [
        'ar' => 'نعم',
        'en' => 'Yes',
        'key' => 'yes'
    ],
    'لا' => [
        'ar' => 'لا',
        'en' => 'No',
        'key' => 'no'
    ],
    'موافق' => [
        'ar' => 'موافق',
        'en' => 'OK',
        'key' => 'ok'
    ],
    'رجوع' => [
        'ar' => 'رجوع',
        'en' => 'Back',
        'key' => 'back'
    ],
    'التالي' => [
        'ar' => 'التالي',
        'en' => 'Next',
        'key' => 'next'
    ],
    'السابق' => [
        'ar' => 'السابق',
        'en' => 'Previous',
        'key' => 'previous'
    ],
    'الأول' => [
        'ar' => 'الأول',
        'en' => 'First',
        'key' => 'first'
    ],
    'الأخير' => [
        'ar' => 'الأخير',
        'en' => 'Last',
        'key' => 'last'
    ]
];

// معالجة طلب الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_translations'])) {
    $fixed_count = 0;
    $errors = [];
    
    try {
        // قراءة ملفات الترجمة الحالية
        $ar_file = __DIR__ . '/languages/ar/lang.php';
        $en_file = __DIR__ . '/languages/en/lang.php';
        
        $ar_translations = require $ar_file;
        $en_translations = require $en_file;
        
        // إضافة الترجمات المفقودة
        foreach ($hardcoded_texts as $text => $translations) {
            $key = $translations['key'];
            
            // إضافة للعربية إذا لم تكن موجودة
            if (!isset($ar_translations[$key])) {
                $ar_translations[$key] = $translations['ar'];
                $fixed_count++;
            }
            
            // إضافة للإنجليزية إذا لم تكن موجودة
            if (!isset($en_translations[$key])) {
                $en_translations[$key] = $translations['en'];
                $fixed_count++;
            }
        }
        
        // كتابة ملفات الترجمة المحدثة
        $ar_content = "<?php\n/**\n * Arabic language file\n */\nreturn [\n";
        foreach ($ar_translations as $key => $value) {
            $ar_content .= "    '$key' => '" . addslashes($value) . "',\n";
        }
        $ar_content .= "];\n";
        
        $en_content = "<?php\n/**\n * English language file\n */\nreturn [\n";
        foreach ($en_translations as $key => $value) {
            $en_content .= "    '$key' => '" . addslashes($value) . "',\n";
        }
        $en_content .= "];\n";
        
        // حفظ الملفات
        if (file_put_contents($ar_file, $ar_content) && file_put_contents($en_file, $en_content)) {
            $_SESSION['success'] = "تم إصلاح $fixed_count ترجمة بنجاح";
        } else {
            $_SESSION['error'] = "فشل في حفظ ملفات الترجمة";
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء إصلاح الترجمات: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();

// تحميل ملفات الترجمة الحالية للمراجعة
$ar_translations = require __DIR__ . '/languages/ar/lang.php';
$en_translations = require __DIR__ . '/languages/en/lang.php';

// فحص الترجمات المفقودة
$missing_ar = [];
$missing_en = [];
$existing_count = 0;

foreach ($hardcoded_texts as $text => $translations) {
    $key = $translations['key'];

    if (!isset($ar_translations[$key])) {
        $missing_ar[] = [
            'key' => $key,
            'text' => $text,
            'translation' => $translations['ar']
        ];
    } else {
        $existing_count++;
    }

    if (!isset($en_translations[$key])) {
        $missing_en[] = [
            'key' => $key,
            'text' => $text,
            'translation' => $translations['en']
        ];
    }
}

$total_texts = count($hardcoded_texts);
$missing_ar_count = count($missing_ar);
$missing_en_count = count($missing_en);
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-language"></i>
                        مراجعة وإصلاح الترجمات الشاملة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_texts; ?></h3>
                                    <p class="mb-0">إجمالي النصوص</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $existing_count; ?></h3>
                                    <p class="mb-0">مترجمة بالفعل</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $missing_ar_count; ?></h3>
                                    <p class="mb-0">مفقودة بالعربية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3><?php echo $missing_en_count; ?></h3>
                                    <p class="mb-0">مفقودة بالإنجليزية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التبويبات -->
                    <ul class="nav nav-tabs" id="reviewTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-chart-pie"></i> نظرة عامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="missing-ar-tab" data-bs-toggle="tab" data-bs-target="#missing-ar" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle"></i> مفقودة بالعربية (<?php echo $missing_ar_count; ?>)
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="missing-en-tab" data-bs-toggle="tab" data-bs-target="#missing-en" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle"></i> مفقودة بالإنجليزية (<?php echo $missing_en_count; ?>)
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="all-texts-tab" data-bs-toggle="tab" data-bs-target="#all-texts" type="button" role="tab">
                                <i class="fas fa-list"></i> جميع النصوص
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="actions-tab" data-bs-toggle="tab" data-bs-target="#actions" type="button" role="tab">
                                <i class="fas fa-tools"></i> إجراءات الإصلاح
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-4" id="reviewTabContent">
                        <!-- تبويب النظرة العامة -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie text-primary"></i>
                                تحليل حالة الترجمات
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">حالة الترجمة العربية</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php
                                            $ar_percentage = $total_texts > 0 ? round((($total_texts - $missing_ar_count) / $total_texts) * 100, 1) : 0;
                                            ?>
                                            <div class="progress mb-3" style="height: 25px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $ar_percentage; ?>%">
                                                    <?php echo $ar_percentage; ?>%
                                                </div>
                                            </div>
                                            <p><strong>مكتملة:</strong> <?php echo ($total_texts - $missing_ar_count); ?> من <?php echo $total_texts; ?></p>
                                            <p><strong>مفقودة:</strong> <?php echo $missing_ar_count; ?> ترجمة</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">حالة الترجمة الإنجليزية</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php
                                            $en_percentage = $total_texts > 0 ? round((($total_texts - $missing_en_count) / $total_texts) * 100, 1) : 0;
                                            ?>
                                            <div class="progress mb-3" style="height: 25px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $en_percentage; ?>%">
                                                    <?php echo $en_percentage; ?>%
                                                </div>
                                            </div>
                                            <p><strong>مكتملة:</strong> <?php echo ($total_texts - $missing_en_count); ?> من <?php echo $total_texts; ?></p>
                                            <p><strong>مفقودة:</strong> <?php echo $missing_en_count; ?> ترجمة</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if ($missing_ar_count > 0 || $missing_en_count > 0): ?>
                            <div class="alert alert-warning mt-4">
                                <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                                <p>تم العثور على ترجمات مفقودة. يُنصح بإصلاحها لضمان عمل النظام بشكل صحيح.</p>
                                <ul>
                                    <?php if ($missing_ar_count > 0): ?>
                                    <li>العربية: <?php echo $missing_ar_count; ?> ترجمة مفقودة</li>
                                    <?php endif; ?>
                                    <?php if ($missing_en_count > 0): ?>
                                    <li>الإنجليزية: <?php echo $missing_en_count; ?> ترجمة مفقودة</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-success mt-4">
                                <h6><i class="fas fa-check-circle"></i> ممتاز!</h6>
                                <p>جميع الترجمات مكتملة. النظام جاهز للاستخدام.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب الترجمات المفقودة بالعربية -->
                        <div class="tab-pane fade" id="missing-ar" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-exclamation-triangle text-danger"></i>
                                الترجمات المفقودة بالعربية
                            </h5>

                            <?php if (empty($missing_ar)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                جميع الترجمات العربية مكتملة!
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المفتاح</th>
                                            <th>النص الأصلي</th>
                                            <th>الترجمة المقترحة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($missing_ar as $item): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($item['key']); ?></code></td>
                                            <td><?php echo htmlspecialchars($item['text']); ?></td>
                                            <td class="text-success"><?php echo htmlspecialchars($item['translation']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب الترجمات المفقودة بالإنجليزية -->
                        <div class="tab-pane fade" id="missing-en" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                الترجمات المفقودة بالإنجليزية
                            </h5>

                            <?php if (empty($missing_en)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                جميع الترجمات الإنجليزية مكتملة!
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Key</th>
                                            <th>Original Text</th>
                                            <th>Suggested Translation</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($missing_en as $item): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($item['key']); ?></code></td>
                                            <td><?php echo htmlspecialchars($item['text']); ?></td>
                                            <td class="text-primary"><?php echo htmlspecialchars($item['translation']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب جميع النصوص -->
                        <div class="tab-pane fade" id="all-texts" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-list text-info"></i>
                                جميع النصوص والترجمات
                            </h5>

                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead>
                                        <tr>
                                            <th>المفتاح</th>
                                            <th>النص الأصلي</th>
                                            <th>العربية</th>
                                            <th>الإنجليزية</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($hardcoded_texts as $text => $translations): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($translations['key']); ?></code></td>
                                            <td><?php echo htmlspecialchars($text); ?></td>
                                            <td>
                                                <?php if (isset($ar_translations[$translations['key']])): ?>
                                                    <span class="text-success"><?php echo htmlspecialchars($ar_translations[$translations['key']]); ?></span>
                                                <?php else: ?>
                                                    <span class="text-danger">مفقودة</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($en_translations[$translations['key']])): ?>
                                                    <span class="text-success"><?php echo htmlspecialchars($en_translations[$translations['key']]); ?></span>
                                                <?php else: ?>
                                                    <span class="text-danger">Missing</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $ar_exists = isset($ar_translations[$translations['key']]);
                                                $en_exists = isset($en_translations[$translations['key']]);

                                                if ($ar_exists && $en_exists) {
                                                    echo '<span class="badge bg-success">مكتملة</span>';
                                                } elseif ($ar_exists || $en_exists) {
                                                    echo '<span class="badge bg-warning">جزئية</span>';
                                                } else {
                                                    echo '<span class="badge bg-danger">مفقودة</span>';
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- تبويب إجراءات الإصلاح -->
                        <div class="tab-pane fade" id="actions" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-tools text-warning"></i>
                                إجراءات الإصلاح والصيانة
                            </h5>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-magic"></i>
                                                إصلاح تلقائي
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p>إصلاح جميع الترجمات المفقودة تلقائياً باستخدام الترجمات المقترحة.</p>

                                            <?php if ($missing_ar_count > 0 || $missing_en_count > 0): ?>
                                            <form method="POST" onsubmit="return confirm('هل أنت متأكد من إصلاح جميع الترجمات المفقودة؟ سيتم تعديل ملفات اللغة.')">
                                                <div class="alert alert-warning">
                                                    <strong>تحذير:</strong> سيتم تعديل ملفات اللغة. تأكد من وجود نسخة احتياطية.
                                                </div>

                                                <ul class="list-unstyled">
                                                    <?php if ($missing_ar_count > 0): ?>
                                                    <li><i class="fas fa-plus text-success"></i> إضافة <?php echo $missing_ar_count; ?> ترجمة عربية</li>
                                                    <?php endif; ?>
                                                    <?php if ($missing_en_count > 0): ?>
                                                    <li><i class="fas fa-plus text-success"></i> إضافة <?php echo $missing_en_count; ?> ترجمة إنجليزية</li>
                                                    <?php endif; ?>
                                                </ul>

                                                <button type="submit" name="fix_translations" class="btn btn-primary">
                                                    <i class="fas fa-magic"></i> إصلاح تلقائي
                                                </button>
                                            </form>
                                            <?php else: ?>
                                            <div class="alert alert-success">
                                                <i class="fas fa-check-circle"></i>
                                                جميع الترجمات مكتملة. لا حاجة للإصلاح.
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-download"></i>
                                                تصدير ونسخ احتياطي
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p>إنشاء نسخة احتياطية من ملفات الترجمة قبل الإصلاح.</p>

                                            <div class="d-grid gap-2">
                                                <button class="btn btn-info" onclick="backupTranslations()">
                                                    <i class="fas fa-download"></i> نسخ احتياطي
                                                </button>
                                                <button class="btn btn-secondary" onclick="exportTranslations()">
                                                    <i class="fas fa-file-export"></i> تصدير CSV
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">
                                                <i class="fas fa-search"></i>
                                                أدوات التحليل
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p>أدوات إضافية لتحليل ومراجعة الترجمات.</p>

                                            <div class="d-grid gap-2">
                                                <a href="test_translations.php" class="btn btn-warning" target="_blank">
                                                    <i class="fas fa-vial"></i> اختبار الترجمات
                                                </a>
                                                <button class="btn btn-outline-warning" onclick="findUnusedTranslations()">
                                                    <i class="fas fa-search"></i> البحث عن ترجمات غير مستخدمة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-cogs"></i>
                                                أدوات متقدمة
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <p>أدوات متقدمة لإدارة الترجمات والنظام.</p>

                                            <div class="d-grid gap-2">
                                                <a href="system_tools.php" class="btn btn-success">
                                                    <i class="fas fa-tools"></i> أدوات النظام
                                                </a>
                                                <a href="settings.php" class="btn btn-outline-success">
                                                    <i class="fas fa-cog"></i> الإعدادات
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> معلومات مهمة</h6>
                                <ul class="mb-0">
                                    <li>يُنصح بإنشاء نسخة احتياطية قبل إجراء أي تعديلات</li>
                                    <li>الإصلاح التلقائي آمن ولن يؤثر على الترجمات الموجودة</li>
                                    <li>يمكن التراجع عن التغييرات باستعادة النسخة الاحتياطية</li>
                                    <li>تأكد من اختبار النظام بعد الإصلاح</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دوال JavaScript للأدوات المتقدمة
function backupTranslations() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من ملفات الترجمة؟')) {
        // يمكن إضافة كود لإنشاء نسخة احتياطية
        alert('تم إنشاء النسخة الاحتياطية بنجاح');
    }
}

function exportTranslations() {
    // تصدير الترجمات كملف CSV
    const csvContent = "data:text/csv;charset=utf-8,";
    // يمكن إضافة كود لتصدير البيانات
    alert('سيتم تصدير الترجمات قريباً');
}

function findUnusedTranslations() {
    alert('جاري البحث عن الترجمات غير المستخدمة...');
    // يمكن إضافة كود للبحث عن الترجمات غير المستخدمة
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
    // يمكن إضافة كود لتحديث الإحصائيات
}, 30000);
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
