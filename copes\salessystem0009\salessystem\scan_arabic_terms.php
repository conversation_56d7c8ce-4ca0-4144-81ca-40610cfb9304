<?php
/**
 * أداة فحص شاملة للمصطلحات العربية في المشروع
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// فحص المصطلحات العربية
$arabic_terms_found = [];
$files_scanned = 0;
$total_terms_found = 0;

// قائمة الملفات المراد فحصها
$files_to_scan = [
    'index.php',
    'customers.php',
    'sales.php',
    'purchases.php',
    'products.php',
    'add_sale.php',
    'add_purchase.php',
    'add_customer.php',
    'edit_sale.php',
    'edit_purchase.php',
    'edit_customer.php',
    'reports.php',
    'settings.php',
    'profile.php',
    'login.php',
    'register.php',
    'includes/header.php',
    'includes/footer.php',
    'includes/sidebar.php'
];

// قائمة المصطلحات العربية المعروفة
$known_arabic_terms = [
    'الإعدادات', 'أدوات النظام', 'غير محدد', 'جدول المشتريات غير موجود', 'إنشاء الجداول',
    'العملاء', 'المبيعات', 'المشتريات', 'المنتجات', 'التقارير', 'حاسبة الضريبة',
    'الملف الشخصي', 'لوحة التحكم', 'الصفحة الرئيسية',
    'إضافة عميل', 'إضافة مبيعات', 'إضافة مشتريات', 'إضافة منتج',
    'تعديل عميل', 'تعديل مبيعات', 'تعديل مشتريات',
    'حذف عميل', 'حذف مبيعات', 'حذف مشتريات',
    'اسم العميل', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرقم الضريبي',
    'رقم الفاتورة', 'تاريخ الفاتورة', 'المبلغ الإجمالي', 'المبلغ الفرعي',
    'مبلغ الضريبة', 'مبلغ الخصم', 'طريقة الدفع', 'حالة الدفع',
    'مدفوع', 'غير مدفوع', 'مدفوع جزئياً', 'نقدي', 'بطاقة ائتمان', 'تحويل بنكي',
    'تم الحفظ بنجاح', 'تم التحديث بنجاح', 'تم الحذف بنجاح', 'حدث خطأ',
    'لا توجد بيانات', 'هل أنت متأكد؟', 'تأكيد الحذف',
    'البحث', 'التصفية', 'الترتيب', 'الطباعة', 'التصدير', 'الاستيراد',
    'التحديث', 'إعادة التحميل', 'اختر ملف', 'رفع ملف', 'تحميل ملف',
    'نسخة احتياطية', 'استعادة', 'إعادة تعيين', 'مسح', 'إغلاق', 'فتح'
];

// تحميل ملفات الترجمة الحالية
$ar_file = __DIR__ . '/languages/ar/lang.php';
$en_file = __DIR__ . '/languages/en/lang.php';

$ar_translations = file_exists($ar_file) ? require $ar_file : [];
$en_translations = file_exists($en_file) ? require $en_file : [];

foreach ($files_to_scan as $filename) {
    $filepath = __DIR__ . '/' . $filename;
    if (file_exists($filepath)) {
        $files_scanned++;
        $content = file_get_contents($filepath);
        
        // البحث عن المصطلحات العربية المعروفة
        foreach ($known_arabic_terms as $term) {
            $count = substr_count($content, $term);
            if ($count > 0) {
                if (!isset($arabic_terms_found[$term])) {
                    $arabic_terms_found[$term] = [
                        'files' => [],
                        'count' => 0,
                        'suggested_key' => generateTermKey($term),
                        'has_translation' => false,
                        'status' => 'untranslated'
                    ];
                }
                
                $arabic_terms_found[$term]['files'][] = $filename;
                $arabic_terms_found[$term]['count'] += $count;
                $total_terms_found += $count;
                
                // فحص وجود ترجمة
                $suggested_key = $arabic_terms_found[$term]['suggested_key'];
                if (isset($ar_translations[$suggested_key]) || isset($en_translations[$suggested_key])) {
                    $arabic_terms_found[$term]['has_translation'] = true;
                    
                    // فحص إذا كان المصطلح مستبدل في الكود
                    if (strpos($content, "__('$suggested_key')") !== false || strpos($content, '__("' . $suggested_key . '")') !== false) {
                        $arabic_terms_found[$term]['status'] = 'translated_and_replaced';
                    } else {
                        $arabic_terms_found[$term]['status'] = 'translated_not_replaced';
                    }
                } else {
                    $arabic_terms_found[$term]['status'] = 'untranslated';
                }
            }
        }
        
        // البحث عن مصطلحات عربية أخرى غير معروفة
        preg_match_all('/[أ-ي\s]{3,}/u', $content, $matches);
        foreach ($matches[0] as $text) {
            $text = trim($text);
            if (strlen($text) > 2 && !in_array($text, $known_arabic_terms)) {
                // تنظيف النص
                $clean_text = preg_replace('/[^\p{Arabic}\s]/u', '', $text);
                $clean_text = trim($clean_text);
                
                if (strlen($clean_text) > 2 && !isset($arabic_terms_found[$clean_text])) {
                    $arabic_terms_found[$clean_text] = [
                        'files' => [$filename],
                        'count' => 1,
                        'suggested_key' => generateTermKey($clean_text),
                        'has_translation' => false,
                        'status' => 'unknown_term'
                    ];
                    $total_terms_found++;
                }
            }
        }
    }
}

// دالة لتوليد مفتاح المصطلح
function generateTermKey($term) {
    $key_map = [
        'الإعدادات' => 'settings',
        'أدوات النظام' => 'system_tools',
        'غير محدد' => 'not_specified',
        'جدول المشتريات غير موجود' => 'purchases_table_missing',
        'إنشاء الجداول' => 'create_tables',
        'العملاء' => 'customers',
        'المبيعات' => 'sales',
        'المشتريات' => 'purchases',
        'المنتجات' => 'products',
        'التقارير' => 'reports',
        'حاسبة الضريبة' => 'tax_calculator',
        'الملف الشخصي' => 'profile',
        'لوحة التحكم' => 'dashboard',
        'الصفحة الرئيسية' => 'home_page',
        'إضافة عميل' => 'add_customer',
        'إضافة مبيعات' => 'add_sale',
        'إضافة مشتريات' => 'add_purchase',
        'تعديل عميل' => 'edit_customer',
        'حذف عميل' => 'delete_customer',
        'اسم العميل' => 'customer_name',
        'رقم الهاتف' => 'phone_number',
        'البريد الإلكتروني' => 'email_address',
        'العنوان' => 'address',
        'الرقم الضريبي' => 'tax_number',
        'رقم الفاتورة' => 'invoice_number',
        'تاريخ الفاتورة' => 'invoice_date',
        'المبلغ الإجمالي' => 'total_amount',
        'طريقة الدفع' => 'payment_method',
        'حالة الدفع' => 'payment_status',
        'مدفوع' => 'paid',
        'غير مدفوع' => 'unpaid',
        'نقدي' => 'cash',
        'تم الحفظ بنجاح' => 'saved_successfully',
        'حدث خطأ' => 'error_occurred',
        'البحث' => 'search',
        'التصفية' => 'filter',
        'الطباعة' => 'print'
    ];
    
    if (isset($key_map[$term])) {
        return $key_map[$term];
    }
    
    // توليد مفتاح تلقائي
    $key = strtolower(trim($term));
    $key = str_replace([' ', 'ا', 'إ', 'أ', 'آ'], ['_', 'a', 'e', 'a', 'a'], $key);
    $key = preg_replace('/[^\w_]/', '', $key);
    $key = preg_replace('/_+/', '_', $key);
    $key = trim($key, '_');
    
    return $key ?: 'unknown_term';
}

// ترتيب المصطلحات حسب الحالة والتكرار
uasort($arabic_terms_found, function($a, $b) {
    $status_priority = [
        'untranslated' => 1,
        'translated_not_replaced' => 2,
        'unknown_term' => 3,
        'translated_and_replaced' => 4
    ];
    
    $a_priority = $status_priority[$a['status']] ?? 5;
    $b_priority = $status_priority[$b['status']] ?? 5;
    
    if ($a_priority === $b_priority) {
        return $b['count'] - $a['count'];
    }
    
    return $a_priority - $b_priority;
});

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-search"></i>
                        فحص شامل للمصطلحات العربية
                    </h4>
                </div>
                <div class="card-body">
                    <!-- الإحصائيات -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $files_scanned; ?></h3>
                                    <p class="mb-0">ملف تم فحصه</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3><?php echo count($arabic_terms_found); ?></h3>
                                    <p class="mb-0">مصطلح عربي</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_terms_found; ?></h3>
                                    <p class="mb-0">إجمالي التكرارات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($ar_translations); ?></h3>
                                    <p class="mb-0">ترجمة متاحة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قائمة المصطلحات -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">المصطلحات العربية المكتشفة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المصطلح</th>
                                            <th>المفتاح المقترح</th>
                                            <th>التكرار</th>
                                            <th>الملفات</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($arabic_terms_found)): ?>
                                        <tr>
                                            <td colspan="5" class="text-center">
                                                <div class="alert alert-success">
                                                    <i class="fas fa-check-circle"></i>
                                                    لم يتم العثور على مصطلحات عربية غير مترجمة!
                                                </div>
                                            </td>
                                        </tr>
                                        <?php else: ?>
                                        <?php foreach ($arabic_terms_found as $term => $info): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($term); ?></strong>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($info['suggested_key']); ?></code>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $info['count']; ?></span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo implode(', ', array_unique($info['files'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php
                                                switch ($info['status']) {
                                                    case 'translated_and_replaced':
                                                        echo '<span class="badge bg-success">مترجم ومستبدل</span>';
                                                        break;
                                                    case 'translated_not_replaced':
                                                        echo '<span class="badge bg-warning">مترجم غير مستبدل</span>';
                                                        break;
                                                    case 'untranslated':
                                                        echo '<span class="badge bg-danger">غير مترجم</span>';
                                                        break;
                                                    case 'unknown_term':
                                                        echo '<span class="badge bg-secondary">مصطلح غير معروف</span>';
                                                        break;
                                                    default:
                                                        echo '<span class="badge bg-light text-dark">غير محدد</span>';
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإجراءات -->
                    <div class="mt-4">
                        <h6>الإجراءات الموصى بها:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <a href="translate_arabic_terms.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-language"></i> ترجمة المصطلحات العربية
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="replace_hardcoded_texts.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-exchange-alt"></i> استبدال النصوص المكتوبة مباشرة
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="translate_all_texts.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-globe"></i> ترجمة شاملة للنصوص
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نصائح -->
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-lightbulb"></i> نصائح للترجمة</h6>
                        <ul class="mb-0">
                            <li><strong>أحمر (غير مترجم):</strong> يحتاج إضافة ترجمة أولاً</li>
                            <li><strong>أصفر (مترجم غير مستبدل):</strong> يحتاج استبدال في الكود</li>
                            <li><strong>رمادي (غير معروف):</strong> مصطلحات جديدة تحتاج مراجعة</li>
                            <li><strong>أخضر (مكتمل):</strong> مترجم ومستبدل بشكل صحيح</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
