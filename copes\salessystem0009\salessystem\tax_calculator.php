<?php
// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات: " . ($db ? $db->connect_error : "اتصال غير موجود");
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

// متغيرات الحساب
$amount = 0;
$tax_rate = 15; // نسبة الضريبة الافتراضية 15%
$tax_amount = 0;
$total_amount = 0;
$calculation_type = 'add'; // إضافة الضريبة (add) أو استخراج الضريبة (extract) أو حساب الضريبة للمشتريات والمبيعات معًا (combined)

// متغيرات إضافية للحساب المشترك
$purchase_amount = 0;
$sales_amount = 0;
$purchase_tax = 0;
$sales_tax = 0;
$net_tax = 0;

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tax_rate = floatval($_POST['tax_rate'] ?? 15);

    // تحديد نوع النموذج المرسل
    $form_type = $_POST['form_type'] ?? 'standard';

    if ($form_type === 'combined') {
        // حساب ضريبة المشتريات والمبيعات معًا (النموذج المستقل)
        $purchase_amount = floatval($_POST['purchase_amount'] ?? 0);
        $sales_amount = floatval($_POST['sales_amount'] ?? 0);

        // حساب ضريبة المشتريات (ضريبة المدخلات)
        $purchase_tax = $purchase_amount * ($tax_rate / 100);

        // حساب ضريبة المبيعات (ضريبة المخرجات)
        $sales_tax = $sales_amount * ($tax_rate / 100);

        // صافي الضريبة المستحقة (ضريبة المخرجات - ضريبة المدخلات)
        $net_tax = $sales_tax - $purchase_tax;

    } else {
        // حاسبة الضريبة العادية
        $amount = floatval($_POST['amount'] ?? 0);
        $calculation_type = $_POST['calculation_type'] ?? 'add';

        if ($calculation_type === 'add') {
            // إضافة الضريبة للمبلغ
            $tax_amount = $amount * ($tax_rate / 100);
            $total_amount = $amount + $tax_amount;
        } else {
            // استخراج الضريبة من المبلغ (المبلغ يتضمن الضريبة)
            $tax_amount = $amount - ($amount / (1 + ($tax_rate / 100)));
            $total_amount = $amount;
            $amount = $total_amount - $tax_amount; // المبلغ قبل الضريبة
        }
    }
}

displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<div class="container mt-4">
    <div class="row">
        <!-- حاسبة الضريبة العادية -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-calculator"></i> <?php echo __('tax_calculator_title'); ?></h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="form_type" value="standard">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="amount" class="form-label"><?php echo __('amount'); ?></label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" class="form-control" id="amount" name="amount" value="<?php echo $amount; ?>" required>
                                    <span class="input-group-text"><?php echo __('currency'); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="tax_rate" class="form-label"><?php echo __('tax_rate'); ?></label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" class="form-control" id="tax_rate" name="tax_rate" value="<?php echo $tax_rate; ?>" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label d-block"><?php echo __('calculation_type'); ?></label>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="calculation_type" id="add_tax" value="add" <?php echo $calculation_type === 'add' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="add_tax"><?php echo __('add_tax'); ?></label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="calculation_type" id="extract_tax" value="extract" <?php echo $calculation_type === 'extract' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="extract_tax"><?php echo __('extract_tax'); ?></label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary"><?php echo __('calculate'); ?></button>
                        </div>
                    </form>

                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['form_type']) && $_POST['form_type'] === 'standard'): ?>
                    <hr>
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="mb-3"><?php echo __('calculation_result'); ?></h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <th><?php echo __('amount_before_tax'); ?></th>
                                            <td><?php echo number_format($amount, 2) . ' ' . __('currency'); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo __('tax_rate'); ?></th>
                                            <td><?php echo number_format($tax_rate, 2); ?>%</td>
                                        </tr>
                                        <tr>
                                            <th><?php echo __('tax_amount'); ?></th>
                                            <td><?php echo number_format($tax_amount, 2) . ' ' . __('currency'); ?></td>
                                        </tr>
                                        <tr class="table-primary">
                                            <th><?php echo __('amount_including_tax'); ?></th>
                                            <td><strong><?php echo number_format($total_amount, 2) . ' ' . __('currency'); ?></strong></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- حاسبة ضريبة المشتريات والمبيعات -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-balance-scale"></i> حاسبة ضريبة المشتريات والمبيعات</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="form_type" value="combined">
                        <div class="row mb-3">
                            <div class="col-md-12 mb-3">
                                <label for="purchase_amount" class="form-label">إجمالي المشتريات</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" class="form-control" id="purchase_amount" name="purchase_amount" value="<?php echo $purchase_amount; ?>" required>
                                    <span class="input-group-text"><?php echo __('currency'); ?></span>
                                </div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="sales_amount" class="form-label">إجمالي المبيعات</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" class="form-control" id="sales_amount" name="sales_amount" value="<?php echo $sales_amount; ?>" required>
                                    <span class="input-group-text"><?php echo __('currency'); ?></span>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label for="combined_tax_rate" class="form-label">نسبة الضريبة</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" class="form-control" id="combined_tax_rate" name="tax_rate" value="<?php echo $tax_rate; ?>" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <button type="submit" class="btn btn-success"><?php echo __('calculate'); ?></button>
                        </div>
                    </form>

                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['form_type']) && $_POST['form_type'] === 'combined'): ?>
                    <hr>
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="mb-3">نتيجة الحساب</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <th colspan="2" class="table-secondary text-center">المشتريات (المدخلات)</th>
                                        </tr>
                                        <tr>
                                            <th>إجمالي المشتريات</th>
                                            <td><?php echo number_format($purchase_amount, 2) . ' ' . __('currency'); ?></td>
                                        </tr>
                                        <tr>
                                            <th>نسبة الضريبة</th>
                                            <td><?php echo number_format($tax_rate, 2); ?>%</td>
                                        </tr>
                                        <tr>
                                            <th>ضريبة المشتريات (المدخلات)</th>
                                            <td><?php echo number_format($purchase_tax, 2) . ' ' . __('currency'); ?></td>
                                        </tr>

                                        <tr>
                                            <th colspan="2" class="table-secondary text-center">المبيعات (المخرجات)</th>
                                        </tr>
                                        <tr>
                                            <th>إجمالي المبيعات</th>
                                            <td><?php echo number_format($sales_amount, 2) . ' ' . __('currency'); ?></td>
                                        </tr>
                                        <tr>
                                            <th>نسبة الضريبة</th>
                                            <td><?php echo number_format($tax_rate, 2); ?>%</td>
                                        </tr>
                                        <tr>
                                            <th>ضريبة المبيعات (المخرجات)</th>
                                            <td><?php echo number_format($sales_tax, 2) . ' ' . __('currency'); ?></td>
                                        </tr>

                                        <tr class="<?php echo $net_tax >= 0 ? 'table-success' : 'table-danger'; ?>">
                                            <th>صافي الضريبة المستحقة</th>
                                            <td><strong><?php echo number_format($net_tax, 2) . ' ' . __('currency'); ?></strong>
                                                <?php if ($net_tax >= 0): ?>
                                                    <span class="badge bg-success">مستحق الدفع للضرائب</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">مستحق لك من الضرائب</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<?php
require_once 'includes/footer.php';
// إغلاق اتصال قاعدة البيانات
if (isset($db) && $db) {
    $db->close();
}
?>
