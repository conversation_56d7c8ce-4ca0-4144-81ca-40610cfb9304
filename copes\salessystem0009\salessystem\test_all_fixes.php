<?php
/**
 * ملف لاختبار جميع الإصلاحات المطبقة على النظام
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$test_results = [];

// اختبار 1: فحص قاعدة البيانات والجداول
$test_results['database'] = [];
$required_tables = ['purchases', 'purchase_items', 'sales', 'sale_items', 'products', 'customers'];

foreach ($required_tables as $table) {
    $check_table = $db->query("SHOW TABLES LIKE '$table'");
    $test_results['database'][$table] = ($check_table && $check_table->num_rows > 0);
}

// اختبار 2: فحص الترجمات
$test_results['translations'] = [];
$used_keys = [
    'app_name', 'dashboard', 'welcome', 'sales', 'purchases', 'customers', 'products',
    'reports', 'add_sale', 'add_purchase', 'add_customer', 'currency', 'total_sales',
    'total_purchases', 'tax_calculator', 'quick_actions', 'recent_sales', 'recent_purchases'
];

$ar_translations = require __DIR__ . '/languages/ar/lang.php';
$en_translations = require __DIR__ . '/languages/en/lang.php';

$missing_ar = 0;
$missing_en = 0;

foreach ($used_keys as $key) {
    if (!isset($ar_translations[$key])) $missing_ar++;
    if (!isset($en_translations[$key])) $missing_en++;
}

$test_results['translations']['ar_total'] = count($ar_translations);
$test_results['translations']['en_total'] = count($en_translations);
$test_results['translations']['missing_ar'] = $missing_ar;
$test_results['translations']['missing_en'] = $missing_en;
$test_results['translations']['coverage'] = (count($used_keys) - $missing_ar - $missing_en) / (count($used_keys) * 2) * 100;

// اختبار 3: فحص الاستعلامات
$test_results['queries'] = [];

try {
    $today = date('Y-m-d');
    
    // اختبار استعلام المبيعات
    $sales_stmt = $db->prepare("SELECT SUM(total_amount) FROM sales WHERE date = ?");
    $sales_stmt->bind_param("s", $today);
    $sales_stmt->execute();
    $sales_stmt->bind_result($today_sales);
    $sales_stmt->fetch();
    $sales_stmt->close();
    $test_results['queries']['sales'] = ['success' => true, 'value' => $today_sales ?? 0];
    
    // اختبار استعلام المشتريات
    $db = resetDBConnection($db);
    $purchases_stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE date = ?");
    $purchases_stmt->bind_param("s", $today);
    $purchases_stmt->execute();
    $purchases_stmt->bind_result($today_purchases);
    $purchases_stmt->fetch();
    $purchases_stmt->close();
    $test_results['queries']['purchases'] = ['success' => true, 'value' => $today_purchases ?? 0];
    
    // اختبار عدد العملاء
    $db = resetDBConnection($db);
    $customers_count = $db->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'];
    $test_results['queries']['customers'] = ['success' => true, 'value' => $customers_count];
    
} catch (Exception $e) {
    $test_results['queries']['error'] = $e->getMessage();
}

// اختبار 4: فحص الملفات المهمة
$test_results['files'] = [];
$important_files = [
    'index.php' => 'الصفحة الرئيسية',
    'add_sale.php' => 'إضافة مبيعات',
    'add_purchase.php' => 'إضافة مشتريات',
    'sales.php' => 'قائمة المبيعات',
    'purchases.php' => 'قائمة المشتريات',
    'customers.php' => 'قائمة العملاء',
    'reports.php' => 'التقارير',
    'tax_calculator.php' => 'حاسبة الضريبة',
    'languages/ar/lang.php' => 'ملف اللغة العربية',
    'languages/en/lang.php' => 'ملف اللغة الإنجليزية'
];

foreach ($important_files as $file => $description) {
    $test_results['files'][$file] = [
        'exists' => file_exists(__DIR__ . '/' . $file),
        'description' => $description
    ];
}

// حساب النتيجة الإجمالية
$total_score = 0;
$max_score = 0;

// نقاط قاعدة البيانات (30 نقطة)
$db_score = array_sum($test_results['database']) * 5;
$total_score += $db_score;
$max_score += count($required_tables) * 5;

// نقاط الترجمات (25 نقطة)
$translation_score = ($test_results['translations']['coverage'] / 100) * 25;
$total_score += $translation_score;
$max_score += 25;

// نقاط الاستعلامات (25 نقطة)
$queries_working = isset($test_results['queries']['sales']) && isset($test_results['queries']['purchases']) && isset($test_results['queries']['customers']);
$queries_score = $queries_working ? 25 : 0;
$total_score += $queries_score;
$max_score += 25;

// نقاط الملفات (20 نقطة)
$files_existing = array_sum(array_column($test_results['files'], 'exists'));
$files_score = ($files_existing / count($important_files)) * 20;
$total_score += $files_score;
$max_score += 20;

$overall_percentage = ($total_score / $max_score) * 100;

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار شامل لجميع الإصلاحات</h2>
    
    <!-- النتيجة الإجمالية -->
    <div class="card mb-4">
        <div class="card-header <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-chart-pie"></i>
                النتيجة الإجمالية: <?php echo number_format($overall_percentage, 1); ?>%
            </h4>
        </div>
        <div class="card-body">
            <div class="progress mb-3" style="height: 30px;">
                <div class="progress-bar <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?>" 
                     style="width: <?php echo $overall_percentage; ?>%">
                    <?php echo number_format($overall_percentage, 1); ?>%
                </div>
            </div>
            <p class="mb-0">
                <?php if ($overall_percentage >= 90): ?>
                    <i class="fas fa-check-circle text-success"></i> ممتاز! جميع الإصلاحات تعمل بشكل مثالي
                <?php elseif ($overall_percentage >= 70): ?>
                    <i class="fas fa-exclamation-triangle text-warning"></i> جيد! معظم الإصلاحات تعمل مع بعض المشاكل البسيطة
                <?php else: ?>
                    <i class="fas fa-times-circle text-danger"></i> يحتاج إلى إصلاح! هناك مشاكل تحتاج إلى حل
                <?php endif; ?>
            </p>
        </div>
    </div>
    
    <div class="row">
        <!-- اختبار قاعدة البيانات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-database"></i>
                        قاعدة البيانات (<?php echo $db_score; ?>/<?php echo count($required_tables) * 5; ?> نقطة)
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['database'] as $table => $exists): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><?php echo $table; ?></span>
                        <span class="badge <?php echo $exists ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $exists ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الترجمات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-language"></i>
                        الترجمات (<?php echo number_format($translation_score, 1); ?>/25 نقطة)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h6><?php echo $test_results['translations']['ar_total']; ?></h6>
                            <small>ترجمات عربية</small>
                        </div>
                        <div class="col-6">
                            <h6><?php echo $test_results['translations']['en_total']; ?></h6>
                            <small>ترجمات إنجليزية</small>
                        </div>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span>التغطية:</span>
                        <span class="badge bg-<?php echo $test_results['translations']['coverage'] >= 90 ? 'success' : 'warning'; ?>">
                            <?php echo number_format($test_results['translations']['coverage'], 1); ?>%
                        </span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>مفقود (عربي):</span>
                        <span class="badge bg-<?php echo $test_results['translations']['missing_ar'] == 0 ? 'success' : 'danger'; ?>">
                            <?php echo $test_results['translations']['missing_ar']; ?>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>مفقود (إنجليزي):</span>
                        <span class="badge bg-<?php echo $test_results['translations']['missing_en'] == 0 ? 'success' : 'danger'; ?>">
                            <?php echo $test_results['translations']['missing_en']; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار الاستعلامات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i>
                        الاستعلامات (<?php echo $queries_score; ?>/25 نقطة)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($test_results['queries']['error'])): ?>
                        <div class="alert alert-danger">
                            خطأ: <?php echo $test_results['queries']['error']; ?>
                        </div>
                    <?php else: ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>مبيعات اليوم:</span>
                            <span class="badge bg-success">
                                <?php echo number_format($test_results['queries']['sales']['value'], 2); ?> ر.س
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>مشتريات اليوم:</span>
                            <span class="badge bg-info">
                                <?php echo number_format($test_results['queries']['purchases']['value'], 2); ?> ر.س
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>عدد العملاء:</span>
                            <span class="badge bg-warning">
                                <?php echo $test_results['queries']['customers']['value']; ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الملفات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-file-code"></i>
                        الملفات المهمة (<?php echo number_format($files_score, 1); ?>/20 نقطة)
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['files'] as $file => $info): ?>
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small title="<?php echo $file; ?>"><?php echo $info['description']; ?></small>
                        <span class="badge <?php echo $info['exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $info['exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الإجراءات -->
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">الإجراءات والأدوات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6>اختبارات فردية:</h6>
                    <div class="d-grid gap-2">
                        <a href="test_system.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-vial"></i> اختبار النظام
                        </a>
                        <a href="test_translations.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-language"></i> اختبار الترجمات
                        </a>
                        <a href="test_purchase_fix.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-bug"></i> اختبار إصلاح المشتريات
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>أدوات الإصلاح:</h6>
                    <div class="d-grid gap-2">
                        <a href="check_tables.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-database"></i> فحص الجداول
                        </a>
                        <a href="fix_translations.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-tools"></i> إصلاح الترجمات
                        </a>
                        <a href="add_sample_data.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-plus-circle"></i> بيانات تجريبية
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>التنقل:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_all_fixes.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-redo"></i> إعادة الاختبار الشامل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
