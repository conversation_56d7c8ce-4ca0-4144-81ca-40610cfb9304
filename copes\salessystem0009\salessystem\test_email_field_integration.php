<?php
/**
 * ملف لاختبار تكامل حقل البريد الإلكتروني في جميع أجزاء النظام
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$test_results = [];

// اختبار 1: فحص بنية جدول العملاء
$test_results['database'] = [];
try {
    $check_table = $db->query("SHOW TABLES LIKE 'customers'");
    $test_results['database']['table_exists'] = ($check_table && $check_table->num_rows > 0);
    
    if ($test_results['database']['table_exists']) {
        $structure = $db->query("DESCRIBE customers");
        $columns = [];
        while ($row = $structure->fetch_assoc()) {
            $columns[$row['Field']] = $row;
        }
        
        $required_columns = ['id', 'name', 'phone', 'email', 'tax_number', 'address'];
        foreach ($required_columns as $column) {
            $test_results['database'][$column] = isset($columns[$column]);
        }
        
        // فحص نوع بيانات عمود البريد الإلكتروني
        if (isset($columns['email'])) {
            $test_results['database']['email_type'] = $columns['email']['Type'];
            $test_results['database']['email_nullable'] = ($columns['email']['Null'] == 'YES');
        }
    }
} catch (Exception $e) {
    $test_results['database']['error'] = $e->getMessage();
}

// اختبار 2: فحص الملفات المحدثة
$files_to_check = [
    'add_customer.php' => 'صفحة إضافة العميل',
    'add_purchase.php' => 'صفحة إضافة المشتريات',
    'add_sale.php' => 'صفحة إضافة المبيعات',
    'ajax_handler.php' => 'معالج AJAX',
    'add_email_column.php' => 'أداة إضافة عمود البريد'
];

$test_results['files'] = [];
foreach ($files_to_check as $file => $description) {
    $file_path = __DIR__ . '/' . $file;
    $test_results['files'][$file] = [
        'exists' => file_exists($file_path),
        'description' => $description,
        'has_email_field' => false,
        'has_email_validation' => false
    ];
    
    if (file_exists($file_path)) {
        $content = file_get_contents($file_path);
        
        // فحص وجود حقل البريد الإلكتروني
        $test_results['files'][$file]['has_email_field'] = (
            strpos($content, 'email') !== false &&
            (strpos($content, 'new_customer_email') !== false || strpos($content, 'name="email"') !== false)
        );
        
        // فحص وجود التحقق من البريد الإلكتروني
        $test_results['files'][$file]['has_email_validation'] = (
            strpos($content, 'filter_var') !== false ||
            strpos($content, 'isValidEmail') !== false ||
            strpos($content, 'type="email"') !== false
        );
    }
}

// اختبار 3: اختبار إضافة عميل تجريبي
$test_results['functionality'] = [];
if ($test_results['database']['table_exists'] && $test_results['database']['email']) {
    try {
        // إضافة عميل تجريبي
        $test_name = 'عميل تجريبي ' . date('His');
        $test_phone = '0501234567';
        $test_email = 'test' . date('His') . '@example.com';
        $test_tax_number = '*********';
        $test_address = 'عنوان تجريبي';
        
        $stmt = $db->prepare("INSERT INTO customers (name, phone, email, tax_number, address) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("sssss", $test_name, $test_phone, $test_email, $test_tax_number, $test_address);
        
        if ($stmt->execute()) {
            $customer_id = $stmt->insert_id;
            $test_results['functionality']['insert_success'] = true;
            $test_results['functionality']['customer_id'] = $customer_id;
            
            // التحقق من البيانات المحفوظة
            $check_stmt = $db->prepare("SELECT name, phone, email, tax_number, address FROM customers WHERE id = ?");
            $check_stmt->bind_param("i", $customer_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            
            if ($row = $result->fetch_assoc()) {
                $test_results['functionality']['data_integrity'] = [
                    'name_match' => ($row['name'] == $test_name),
                    'phone_match' => ($row['phone'] == $test_phone),
                    'email_match' => ($row['email'] == $test_email),
                    'tax_number_match' => ($row['tax_number'] == $test_tax_number),
                    'address_match' => ($row['address'] == $test_address)
                ];
            }
            
            // حذف العميل التجريبي
            $delete_stmt = $db->prepare("DELETE FROM customers WHERE id = ?");
            $delete_stmt->bind_param("i", $customer_id);
            $delete_stmt->execute();
            
        } else {
            $test_results['functionality']['insert_success'] = false;
            $test_results['functionality']['error'] = $stmt->error;
        }
    } catch (Exception $e) {
        $test_results['functionality']['error'] = $e->getMessage();
    }
}

// حساب النتيجة الإجمالية
$total_score = 0;
$max_score = 0;

// نقاط قاعدة البيانات (40 نقطة)
$db_score = 0;
if ($test_results['database']['table_exists']) $db_score += 10;
if (isset($test_results['database']['email']) && $test_results['database']['email']) $db_score += 15;
if (isset($test_results['database']['email_type']) && strpos($test_results['database']['email_type'], 'varchar') !== false) $db_score += 10;
if (isset($test_results['database']['email_nullable']) && $test_results['database']['email_nullable']) $db_score += 5;
$total_score += $db_score;
$max_score += 40;

// نقاط الملفات (30 نقطة)
$files_score = 0;
foreach ($test_results['files'] as $file_data) {
    if ($file_data['exists']) $files_score += 2;
    if ($file_data['has_email_field']) $files_score += 3;
    if ($file_data['has_email_validation']) $files_score += 1;
}
$total_score += $files_score;
$max_score += count($files_to_check) * 6;

// نقاط الوظائف (30 نقطة)
$functionality_score = 0;
if (isset($test_results['functionality']['insert_success']) && $test_results['functionality']['insert_success']) {
    $functionality_score += 15;
    if (isset($test_results['functionality']['data_integrity'])) {
        $integrity_checks = array_sum($test_results['functionality']['data_integrity']);
        $functionality_score += ($integrity_checks / 5) * 15;
    }
}
$total_score += $functionality_score;
$max_score += 30;

$overall_percentage = ($total_score / $max_score) * 100;

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار تكامل حقل البريد الإلكتروني</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
        <p>هذا الاختبار يتحقق من تكامل حقل البريد الإلكتروني في جميع أجزاء النظام:</p>
        <ul>
            <li>بنية جدول قاعدة البيانات</li>
            <li>الملفات المحدثة</li>
            <li>وظائف الإدراج والاستعلام</li>
        </ul>
    </div>
    
    <!-- النتيجة الإجمالية -->
    <div class="card mb-4">
        <div class="card-header <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-chart-pie"></i>
                النتيجة الإجمالية: <?php echo number_format($overall_percentage, 1); ?>%
            </h4>
        </div>
        <div class="card-body">
            <div class="progress mb-3" style="height: 30px;">
                <div class="progress-bar <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?>" 
                     style="width: <?php echo $overall_percentage; ?>%">
                    <?php echo number_format($total_score, 1); ?>/<?php echo $max_score; ?> (<?php echo number_format($overall_percentage, 1); ?>%)
                </div>
            </div>
            
            <?php if ($overall_percentage >= 90): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ممتاز! حقل البريد الإلكتروني متكامل بشكل مثالي
                </div>
            <?php elseif ($overall_percentage >= 70): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> جيد! التكامل يعمل مع بعض المشاكل البسيطة
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i> يحتاج إلى إصلاح! هناك مشاكل في التكامل
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="row">
        <!-- اختبار قاعدة البيانات -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header <?php echo $db_score >= 35 ? 'bg-success' : ($db_score >= 25 ? 'bg-warning' : 'bg-danger'); ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-database"></i>
                        قاعدة البيانات (<?php echo $db_score; ?>/40)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>الجدول موجود:</span>
                        <span class="badge <?php echo $test_results['database']['table_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['database']['table_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <?php if (isset($test_results['database']['email'])): ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span>عمود البريد الإلكتروني:</span>
                        <span class="badge <?php echo $test_results['database']['email'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['database']['email'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($test_results['database']['email_type'])): ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span>نوع البيانات:</span>
                        <span class="badge bg-info"><?php echo $test_results['database']['email_type']; ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($test_results['database']['email_nullable'])): ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span>يقبل NULL:</span>
                        <span class="badge <?php echo $test_results['database']['email_nullable'] ? 'bg-success' : 'bg-warning'; ?>">
                            <?php echo $test_results['database']['email_nullable'] ? 'نعم' : 'لا'; ?>
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الملفات -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header <?php echo $files_score >= 25 ? 'bg-success' : ($files_score >= 15 ? 'bg-warning' : 'bg-danger'); ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-code"></i>
                        الملفات (<?php echo $files_score; ?>/<?php echo count($files_to_check) * 6; ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['files'] as $file => $data): ?>
                    <div class="mb-3">
                        <h6 class="mb-1"><?php echo $data['description']; ?></h6>
                        <div class="d-flex justify-content-between">
                            <small>موجود:</small>
                            <span class="badge <?php echo $data['exists'] ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $data['exists'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>حقل البريد:</small>
                            <span class="badge <?php echo $data['has_email_field'] ? 'bg-success' : 'bg-warning'; ?>">
                                <?php echo $data['has_email_field'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>التحقق:</small>
                            <span class="badge <?php echo $data['has_email_validation'] ? 'bg-success' : 'bg-warning'; ?>">
                                <?php echo $data['has_email_validation'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                    </div>
                    <hr>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الوظائف -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header <?php echo $functionality_score >= 25 ? 'bg-success' : ($functionality_score >= 15 ? 'bg-warning' : 'bg-danger'); ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i>
                        الوظائف (<?php echo number_format($functionality_score, 1); ?>/30)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($test_results['functionality']['insert_success'])): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>إدراج البيانات:</span>
                            <span class="badge <?php echo $test_results['functionality']['insert_success'] ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $test_results['functionality']['insert_success'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                        
                        <?php if (isset($test_results['functionality']['data_integrity'])): ?>
                            <h6 class="mt-3">سلامة البيانات:</h6>
                            <?php foreach ($test_results['functionality']['data_integrity'] as $field => $match): ?>
                            <div class="d-flex justify-content-between">
                                <small><?php echo str_replace('_match', '', $field); ?>:</small>
                                <span class="badge <?php echo $match ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $match ? '✓' : '✗'; ?>
                                </span>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="alert alert-warning">
                            لا يمكن اختبار الوظائف - تحقق من قاعدة البيانات
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($test_results['functionality']['error'])): ?>
                        <div class="alert alert-danger mt-2">
                            <small>خطأ: <?php echo htmlspecialchars($test_results['functionality']['error']); ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الإجراءات -->
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">الإجراءات والأدوات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <h6>إدارة قاعدة البيانات:</h6>
                    <div class="d-grid gap-2">
                        <a href="add_email_column.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus"></i> إضافة عمود البريد
                        </a>
                        <a href="fix_customer_table.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-tools"></i> إصلاح الجدول
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>اختبار الميزات:</h6>
                    <div class="d-grid gap-2">
                        <a href="test_customer_fields_match.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-check-double"></i> تطابق الحقول
                        </a>
                        <a href="test_ajax_customer.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-vial"></i> اختبار AJAX
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>اختبار الصفحات:</h6>
                    <div class="d-grid gap-2">
                        <a href="add_customer.php" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </a>
                        <a href="add_purchase.php" class="btn btn-outline-secondary btn-sm" target="_blank">
                            <i class="fas fa-shopping-cart"></i> إضافة مشتريات
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>العودة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_email_field_integration.php" class="btn btn-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة الاختبار
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
