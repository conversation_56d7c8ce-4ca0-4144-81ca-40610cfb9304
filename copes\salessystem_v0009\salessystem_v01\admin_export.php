<?php
/**
 * ملف تصدير البيانات للمديرين
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    http_response_code(403);
    exit('غير مصرح');
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    http_response_code(403);
    exit('ليس لديك صلاحية للوصول لهذه الصفحة');
}

$type = $_GET['type'] ?? '';
$format = $_GET['format'] ?? 'excel';

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    switch ($type) {
        case 'users':
            exportUsers($db);
            break;
        case 'sales':
            exportSales($db);
            break;
        case 'purchases':
            exportPurchases($db);
            break;
        default:
            throw new Exception("نوع التصدير غير مدعوم");
    }

} catch (Exception $e) {
    http_response_code(500);
    exit('خطأ في التصدير: ' . $e->getMessage());
}

function exportUsers($db) {
    $user_ids = $_GET['user_ids'] ?? '';
    
    // بناء الاستعلام
    $query = "SELECT id, username, full_name, email, phone, status, last_login, created_at FROM users";
    $params = [];
    
    if (!empty($user_ids)) {
        $ids = explode(',', $user_ids);
        $ids = array_map('intval', $ids);
        $ids = array_filter($ids);
        
        if (!empty($ids)) {
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $query .= " WHERE id IN ($placeholders)";
            $params = $ids;
        }
    }
    
    $query .= " ORDER BY created_at DESC";
    
    // تنفيذ الاستعلام
    if (!empty($params)) {
        $stmt = $db->prepare($query);
        $types = str_repeat('i', count($params));
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        $result = $db->query($query);
    }
    
    // إعداد headers للتنزيل
    $filename = 'users_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    
    // إنشاء ملف CSV
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة العناوين
    fputcsv($output, [
        'الرقم التعريفي',
        'اسم المستخدم',
        'الاسم الكامل',
        'البريد الإلكتروني',
        'رقم الهاتف',
        'الحالة',
        'آخر دخول',
        'تاريخ التسجيل'
    ]);
    
    // كتابة البيانات
    while ($row = $result->fetch_assoc()) {
        fputcsv($output, [
            $row['id'],
            $row['username'],
            $row['full_name'] ?? '',
            $row['email'] ?? '',
            $row['phone'] ?? '',
            $row['status'] === 'active' ? 'نشط' : 'غير نشط',
            $row['last_login'] ? date('Y-m-d H:i:s', strtotime($row['last_login'])) : 'لم يسجل دخول',
            date('Y-m-d H:i:s', strtotime($row['created_at']))
        ]);
    }
    
    fclose($output);
    exit();
}

function exportSales($db) {
    $query = "SELECT s.*, c.name as customer_name, u.full_name as user_name 
              FROM sales s 
              LEFT JOIN customers c ON s.customer_id = c.id 
              LEFT JOIN users u ON s.user_id = u.id 
              ORDER BY s.date DESC";
    
    $result = $db->query($query);
    
    $filename = 'sales_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    fputcsv($output, [
        'رقم الفاتورة',
        'التاريخ',
        'العميل',
        'المستخدم',
        'المبلغ الفرعي',
        'الضريبة',
        'الإجمالي'
    ]);
    
    while ($row = $result->fetch_assoc()) {
        fputcsv($output, [
            $row['invoice_number'],
            $row['date'],
            $row['customer_name'] ?? 'غير محدد',
            $row['user_name'] ?? 'غير محدد',
            $row['subtotal'],
            $row['tax_amount'],
            $row['total_amount']
        ]);
    }
    
    fclose($output);
    exit();
}

function exportPurchases($db) {
    $query = "SELECT p.*, u.full_name as user_name 
              FROM purchases p 
              LEFT JOIN users u ON p.user_id = u.id 
              ORDER BY p.date DESC";
    
    $result = $db->query($query);
    
    $filename = 'purchases_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    fputcsv($output, [
        'رقم الفاتورة',
        'التاريخ',
        'المورد',
        'المستخدم',
        'المبلغ الفرعي',
        'الضريبة',
        'الإجمالي'
    ]);
    
    while ($row = $result->fetch_assoc()) {
        fputcsv($output, [
            $row['invoice_number'],
            $row['date'],
            $row['supplier_name'] ?? 'غير محدد',
            $row['user_name'] ?? 'غير محدد',
            $row['subtotal'],
            $row['tax_amount'],
            $row['total_amount']
        ]);
    }
    
    fclose($output);
    exit();
}
?>
