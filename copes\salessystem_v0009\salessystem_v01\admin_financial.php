<?php
/**
 * صفحة التقارير المالية وتفاصيل الفواتير
 */
require_once __DIR__ . '/config/init.php';

// دوال التقارير المالية
function exportFinancialData($type, $data, $format = 'excel') {
    switch ($format) {
        case 'excel':
            exportToExcel($data, 'financial_report_' . date('Y-m-d'));
            break;
        case 'pdf':
            exportToPDF($data, 'financial_report_' . date('Y-m-d'));
            break;
        case 'csv':
            exportToCSV($data, 'financial_report_' . date('Y-m-d'));
            break;
    }
}

function generateFinancialChart($sales_data, $purchases_data) {
    // إنشاء بيانات الرسم البياني
    $chart_data = [
        'sales' => $sales_data,
        'purchases' => $purchases_data,
        'profit' => $sales_data - $purchases_data
    ];
    return $chart_data;
}

function calculateFinancialMetrics($sales, $purchases) {
    $profit = $sales - $purchases;
    $profit_margin = $sales > 0 ? ($profit / $sales) * 100 : 0;
    $roi = $purchases > 0 ? ($profit / $purchases) * 100 : 0;

    return [
        'profit' => $profit,
        'profit_margin' => $profit_margin,
        'roi' => $roi
    ];
}

function generateComparisonReport($params) {
    // إنشاء تقرير مقارنة
    logActivity('comparison_report_generated', 'financial', null, null, null, 'تم إنشاء تقرير مقارنة مالية');
    $_SESSION['success'] = 'تم إنشاء تقرير المقارنة بنجاح';
}

function scheduleFinancialReport($params) {
    // جدولة التقرير المالي
    global $db;

    $frequency = $params['frequency'] ?? 'monthly';
    $email = $params['email'] ?? '';
    $report_type = $params['report_type'] ?? 'summary';

    if (empty($email)) {
        $_SESSION['error'] = 'البريد الإلكتروني مطلوب';
        return;
    }

    logActivity('financial_report_scheduled', 'financial', null, null, null, "تم جدولة تقرير مالي $report_type");
    $_SESSION['success'] = 'تم جدولة التقرير المالي بنجاح';
}

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'export_financial':
                $export_format = $_POST['export_format'] ?? 'excel';
                $export_data = $_POST['export_data'] ?? '';
                exportFinancialData('financial', $export_data, $export_format);
                break;

            case 'generate_comparison':
                $comparison_params = $_POST['comparison_params'] ?? [];
                generateComparisonReport($comparison_params);
                break;

            case 'schedule_financial_report':
                $schedule_params = $_POST['schedule_params'] ?? [];
                scheduleFinancialReport($schedule_params);
                break;
        }
    } catch (Exception $e) {
        ErrorHandler::logError('ERROR', 'Financial report action error: ' . $e->getMessage(), __FILE__, __LINE__);
        $_SESSION['error'] = 'حدث خطأ في تنفيذ العملية: ' . $e->getMessage();
    }
}
// التحقق من الصلاحيات
if (!hasAdminPermission('view_reports')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}
// تضمين ملف قاعدة البيانات الموحدة
require_once __DIR__ . '/config/unified_db_config.php';
$main_db = getUnifiedDB();
// معاملات البحث
$user_filter = $_GET['user_id'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$report_type = $_GET['report_type'] ?? 'summary';
// التأكد من أن التواريخ صحيحة
if (empty($date_from)) $date_from = date('Y-m-01');
if (empty($date_to)) $date_to = date('Y-m-d');
// إضافة تشخيص
$debug_mode = isset($_GET['debug']) && $_GET['debug'] == '1';
// جلب قائمة المستخدمين من قاعدة البيانات الموحدة
$users_result = null;
if ($main_db) {
    $users_result = $main_db->query("SELECT id, username, full_name FROM users WHERE status = 'active' ORDER BY full_name");
    if (!$users_result) {
        // إذا فشل الاستعلام، إنشاء نتيجة فارغة
        $users_result = new class {
            public function fetch_assoc() {
 return null; }
        };
}
}
else {
    // إذا فشل الاتصال، إنشاء نتيجة فارغة
    $users_result = new class {
        public function fetch_assoc() {
 return null; }
    };
}
// جلب البيانات المالية
$financial_data = [];
$total_sales = 0;
$total_purchases = 0;
$total_profit = 0;
// استخدام قاعدة البيانات الموحدة
require_once __DIR__ . '/config/unified_db_config.php';
$user_db = getUnifiedDB();
// تهيئة المتغيرات
$sales_result = null;
$purchases_result = null;
$total_sales = 0;
$total_purchases = 0;
$total_profit = 0;
if ($user_db) {
    try {
        if ($debug_mode) {
            echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;'>";
            echo "<h4>معلومات التشخيص:</h4>";
            echo "المستخدم المحدد: " . ($user_filter ?: 'جميع المستخدمين') . "<br>";
            echo "من تاريخ: $date_from<br>";
            echo "إلى تاريخ: $date_to<br>";
            echo "نوع التقرير: $report_type<br>";
            echo "</div>";
}
        if ($user_filter) {
            // تقرير مستخدم محدد
            if ($debug_mode) echo "<p>جاري جلب بيانات المستخدم رقم: $user_filter</p>
";
            // جلب بيانات المبيعات للمستخدم المحدد
            $sales_query = "SELECT s.*, c.name as customer_name, u.full_name as user_name
                           FROM sales s
                           LEFT JOIN customers c ON s.customer_id = c.id
                           LEFT JOIN users u ON s.user_id = u.id
                           WHERE s.user_id = ? AND s.date BETWEEN ? AND ?
                           ORDER BY s.date DESC";
            $sales_stmt = $user_db->prepare($sales_query);
            if ($sales_stmt) {
                $sales_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $sales_stmt->execute();
                $sales_result = $sales_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المبيعات: " . $sales_result->num_rows . "</p>";

                // تحويل النتيجة إلى مصفوفة للاستخدام المتعدد
                $sales_data = [];
                while ($row = $sales_result->fetch_assoc()) {
                    $sales_data[] = $row;
                }
                $sales_result = $sales_data; // إعادة تعيين للاستخدام في الجدول
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المبيعات: " . $user_db->error . "</p>
";
}
            // جلب بيانات المشتريات للمستخدم المحدد
            $purchases_query = "SELECT p.*, p.supplier_name, u.full_name as user_name
                               FROM purchases p
                               LEFT JOIN users u ON p.user_id = u.id
                               WHERE p.user_id = ? AND p.date BETWEEN ? AND ?
                               ORDER BY p.date DESC";
            $purchases_stmt = $user_db->prepare($purchases_query);
            if ($purchases_stmt) {
                $purchases_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $purchases_stmt->execute();
                $purchases_result = $purchases_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المشتريات: " . $purchases_result->num_rows . "</p>";

                // تحويل النتيجة إلى مصفوفة للاستخدام المتعدد
                $purchases_data = [];
                while ($row = $purchases_result->fetch_assoc()) {
                    $purchases_data[] = $row;
                }
                $purchases_result = $purchases_data; // إعادة تعيين للاستخدام في الجدول
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المشتريات: " . $user_db->error . "</p>
";
}
            // حساب الإجماليات للمستخدم المحدد
            $sales_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM sales WHERE user_id = ? AND date BETWEEN ? AND ?");
            if ($sales_total_stmt) {
                $sales_total_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $sales_total_stmt->execute();
                $sales_total_result = $sales_total_stmt->get_result();
                $total_sales = $sales_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مبيعات المستخدم: " . number_format($total_sales, 2) . "</p>
";
                $sales_total_stmt->close();
}
            $purchases_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM purchases WHERE user_id = ? AND date BETWEEN ? AND ?");
            if ($purchases_total_stmt) {
                $purchases_total_stmt->bind_param("iss", $user_filter, $date_from, $date_to);
                $purchases_total_stmt->execute();
                $purchases_total_result = $purchases_total_stmt->get_result();
                $total_purchases = $purchases_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مشتريات المستخدم: " . number_format($total_purchases, 2) . "</p>
";
                $purchases_total_stmt->close();
}
            $total_profit = $total_sales - $total_purchases;
            // إغلاق الاستعلامات
            if ($sales_stmt) $sales_stmt->close();
            if ($purchases_stmt) $purchases_stmt->close();
}
else {
            // تقرير جميع المستخدمين
            if ($debug_mode) echo "<p>جاري جلب بيانات جميع المستخدمين</p>
";
            // جلب بيانات المبيعات لجميع المستخدمين
            $sales_query = "SELECT s.*, c.name as customer_name, u.full_name as user_name
                           FROM sales s
                           LEFT JOIN customers c ON s.customer_id = c.id
                           LEFT JOIN users u ON s.user_id = u.id
                           WHERE s.date BETWEEN ? AND ?
                           ORDER BY s.date DESC";
            $sales_stmt = $user_db->prepare($sales_query);
            if ($sales_stmt) {
                $sales_stmt->bind_param("ss", $date_from, $date_to);
                $sales_stmt->execute();
                $sales_result = $sales_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المبيعات (جميع المستخدمين): " . $sales_result->num_rows . "</p>";

                // تحويل النتيجة إلى مصفوفة للاستخدام المتعدد
                $sales_data = [];
                while ($row = $sales_result->fetch_assoc()) {
                    $sales_data[] = $row;
                }
                $sales_result = $sales_data; // إعادة تعيين للاستخدام في الجدول
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المبيعات: " . $user_db->error . "</p>
";
}
            // جلب بيانات المشتريات لجميع المستخدمين
            $purchases_query = "SELECT p.*, p.supplier_name, u.full_name as user_name
                               FROM purchases p
                               LEFT JOIN users u ON p.user_id = u.id
                               WHERE p.date BETWEEN ? AND ?
                               ORDER BY p.date DESC";
            $purchases_stmt = $user_db->prepare($purchases_query);
            if ($purchases_stmt) {
                $purchases_stmt->bind_param("ss", $date_from, $date_to);
                $purchases_stmt->execute();
                $purchases_result = $purchases_stmt->get_result();
                if ($debug_mode) echo "<p>عدد فواتير المشتريات (جميع المستخدمين): " . $purchases_result->num_rows . "</p>";

                // تحويل النتيجة إلى مصفوفة للاستخدام المتعدد
                $purchases_data = [];
                while ($row = $purchases_result->fetch_assoc()) {
                    $purchases_data[] = $row;
                }
                $purchases_result = $purchases_data; // إعادة تعيين للاستخدام في الجدول
}
else {
                if ($debug_mode) echo "<p>خطأ في استعلام المشتريات: " . $user_db->error . "</p>
";
}
            // حساب الإجماليات لجميع المستخدمين
            $sales_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM sales WHERE date BETWEEN ? AND ?");
            if ($sales_total_stmt) {
                $sales_total_stmt->bind_param("ss", $date_from, $date_to);
                $sales_total_stmt->execute();
                $sales_total_result = $sales_total_stmt->get_result();
                $total_sales = $sales_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مبيعات جميع المستخدمين: " . number_format($total_sales, 2) . "</p>
";
                $sales_total_stmt->close();
}
            $purchases_total_stmt = $user_db->prepare("SELECT SUM(total_amount) as total FROM purchases WHERE date BETWEEN ? AND ?");
            if ($purchases_total_stmt) {
                $purchases_total_stmt->bind_param("ss", $date_from, $date_to);
                $purchases_total_stmt->execute();
                $purchases_total_result = $purchases_total_stmt->get_result();
                $total_purchases = $purchases_total_result->fetch_assoc()['total'] ?? 0;
                if ($debug_mode) echo "<p>إجمالي مشتريات جميع المستخدمين: " . number_format($total_purchases, 2) . "</p>
";
                $purchases_total_stmt->close();
}
            $total_profit = $total_sales - $total_purchases;
            // إغلاق الاستعلامات
            if ($sales_stmt) $sales_stmt->close();
            if ($purchases_stmt) $purchases_stmt->close();
}
    }
catch (Exception $e) {
        // تسجيل الخطأ
        error_log("خطأ في التقارير المالية: " . $e->getMessage());
        $total_sales = 0;
        $total_purchases = 0;
        $total_profit = 0;
}
}
require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-file-invoice-dollar me-3"></i>
                        التقارير المالية
                    </h1>
                    <p class="text-muted mb-0">مراجعة التقارير المالية والإحصائيات</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-success" onclick="exportFinancialReport()">
                        <i class="fas fa-download"></i>
                        <span>تصدير التقرير</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-info" onclick="printFinancialReport()">
                        <i class="fas fa-print"></i>
                        <span>طباعة</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-warning" onclick="generateComparison()">
                        <i class="fas fa-chart-bar"></i>
                        <span>تقرير مقارنة</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-primary" onclick="scheduleReport()">
                        <i class="fas fa-clock"></i>
                        <span>جدولة التقرير</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير المالي
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form method="GET" class="row g-3" id="financialFilters">
                        <div class="col-md-2">
                            <label for="user_id" class="modern-form-label">المستخدم</label>
                            <select class="modern-form-control" id="user_id" name="user_id">
                                <option value="">جميع المستخدمين</option>
                                <?php
                                // إعادة تعيين مؤشر النتيجة
                                if ($users_result && method_exists($users_result, 'data_seek')) {
                                    $users_result->data_seek(0);
                                }
                                while ($user = $users_result->fetch_assoc()): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['full_name'] ?? $user['username']); ?>
                                </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="report_type" class="modern-form-label">نوع التقرير</label>
                            <select class="modern-form-control" id="report_type" name="report_type">
                                <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>ملخص</option>
                                <option value="detailed" <?php echo $report_type === 'detailed' ? 'selected' : ''; ?>>تفصيلي</option>
                                <option value="invoices" <?php echo $report_type === 'invoices' ? 'selected' : ''; ?>>الفواتير</option>
                                <option value="comparison" <?php echo $report_type === 'comparison' ? 'selected' : ''; ?>>مقارنة</option>
                                <option value="analytics" <?php echo $report_type === 'analytics' ? 'selected' : ''; ?>>تحليلي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="modern-form-label">من تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="modern-form-label">إلى تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="modern-btn modern-btn-danger me-2">
                                <i class="fas fa-search me-1"></i>إنشاء التقرير
                            </button>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="modern-btn modern-btn-outline" onclick="resetFinancialFilters()">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                        </div>
</form>
                </div>
            </div>
            <?php if (isset($total_sales)): ?>
            <!-- الملخص المالي -->
            <div class="row g-4 mb-5">
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">إجمالي المبيعات</div>
                                <div class="luxury-value"><?php echo number_format($total_sales, 2); ?> ريال</div>
                                <div class="luxury-change">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    إيرادات
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">إجمالي المشتريات</div>
                                <div class="luxury-value"><?php echo number_format($total_purchases, 2); ?> ريال</div>
                                <div class="luxury-change">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    مصروفات
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">صافي الربح</div>
                                <div class="luxury-value <?php echo $total_profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo number_format($total_profit, 2); ?> ريال
                                </div>
                                <div class="luxury-change">
                                    <i class="fas fa-chart-line me-1"></i>
                                    <?php echo $total_profit >= 0 ? 'ربح' : 'خسارة'; ?>
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">هامش الربح</div>
                                <div class="luxury-value">
                                    <?php
                                    $profit_margin = $total_sales > 0 ? (($total_profit / $total_sales) * 100) : 0;
                                    echo number_format($profit_margin, 1); ?>%
                                </div>
                                <div class="luxury-change">
                                    <i class="fas fa-percentage me-1"></i>
                                    نسبة الربح
                                </div>
                            </div>
                            <div class="luxury-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if (isset($sales_result) && is_array($sales_result) && !empty($sales_result)): ?>
            <!-- تفاصيل فواتير المبيعات -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-file-invoice me-2"></i>
                        فواتير المبيعات
                    </h5>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table class="modern-table table mb-0">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>العميل</th>
                                    <?php if (!$user_filter): ?>
                                    <th>المستخدم</th>
                                    <?php endif; ?>
                                    <th>المبلغ الفرعي</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>الإجراءات</th>
</tr>
                            </thead>
                            <tbody>
                                <?php if (is_array($sales_result)): ?>
                                    <?php foreach ($sales_result as $sale): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($sale['invoice_number']); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($sale['date'])); ?></td>
                                            <td><?php echo htmlspecialchars($sale['customer_name'] ?? 'غير محدد'); ?></td>
                                            <?php if (!$user_filter): ?>
                                                <td><?php echo htmlspecialchars($sale['user_name'] ?? 'غير محدد'); ?></td>
                                            <?php endif; ?>
                                            <td><?php echo number_format($sale['subtotal'], 2); ?></td>
                                            <td><?php echo number_format($sale['tax_amount'], 2); ?></td>
                                            <td><strong><?php echo number_format($sale['total_amount'], 2); ?></strong></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-info" onclick="viewInvoiceDetails(<?php echo $sale['id']; ?>, 'sale')" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" onclick="printInvoice(<?php echo $sale['id']; ?>, 'sale')" title="طباعة">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="exportInvoice(<?php echo $sale['id']; ?>, 'sale')" title="تصدير">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?php echo $user_filter ? '7' : '8'; ?>" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                                <h5>لا توجد فواتير مبيعات</h5>
                                                <p>لم يتم العثور على فواتير مبيعات في الفترة المحددة</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
</table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($purchases_result) && is_array($purchases_result) && !empty($purchases_result)): ?>
            <!-- تفاصيل فواتير المشتريات -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-file-invoice me-2"></i>
                        فواتير المشتريات
                    </h5>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table class="modern-table table mb-0">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <?php if (!$user_filter): ?>
                                    <th>المستخدم</th>
                                    <?php endif; ?>
                                    <th>المبلغ الفرعي</th>
                                    <th>الضريبة</th>
                                    <th>الإجمالي</th>
                                    <th>الإجراءات</th>
</tr>
                            </thead>
                            <tbody>
                                <?php if (is_array($purchases_result) && !empty($purchases_result)): ?>
                                    <?php foreach ($purchases_result as $purchase): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($purchase['invoice_number']); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($purchase['date'])); ?></td>
                                            <td><?php echo htmlspecialchars($purchase['supplier_name'] ?? 'غير محدد'); ?></td>
                                            <?php if (!$user_filter): ?>
                                                <td><?php echo htmlspecialchars($purchase['user_name'] ?? 'غير محدد'); ?></td>
                                            <?php endif; ?>
                                            <td><?php echo number_format($purchase['subtotal'], 2); ?></td>
                                            <td><?php echo number_format($purchase['tax_amount'], 2); ?></td>
                                            <td><strong><?php echo number_format($purchase['total_amount'], 2); ?></strong></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-info" onclick="viewInvoiceDetails(<?php echo $purchase['id']; ?>, 'purchase')" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" onclick="printInvoice(<?php echo $purchase['id']; ?>, 'purchase')" title="طباعة">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="exportInvoice(<?php echo $purchase['id']; ?>, 'purchase')" title="تصدير">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?php echo $user_filter ? '7' : '8'; ?>" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                                <h5>لا توجد فواتير مشتريات</h5>
                                                <p>لم يتم العثور على فواتير مشتريات في الفترة المحددة</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
</table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
<?php else: ?>
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>اضغط "إنشاء التقرير" لعرض البيانات المالية</h5>
                <p>يمكنك اختيار مستخدم محدد أو ترك الحقل فارغاً لعرض تقرير جميع المستخدمين، ثم اضغط "إنشاء التقرير".</p>
            </div>
            <?php endif; ?>
        </main></div>
<script>
// عرض تفاصيل الفاتورة
function viewInvoiceDetails(invoiceId, type) {
    Swal.fire({
        title: 'تفاصيل الفاتورة',
        html: `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الفاتورة...</p>
            </div>
        `,
        showConfirmButton: false,
        allowOutsideClick: false
    });

    // محاكاة تحميل البيانات
    setTimeout(() => {
        Swal.fire({
            title: `تفاصيل فاتورة ${type === 'sale' ? 'المبيعات' : 'المشتريات'}`,
            html: `
                <div class="text-start">
                    <div class="row">
                        <div class="col-6"><strong>رقم الفاتورة:</strong></div>
                        <div class="col-6">${invoiceId}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-6"><strong>النوع:</strong></div>
                        <div class="col-6">${type === 'sale' ? 'مبيعات' : 'مشتريات'}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-6"><strong>التاريخ:</strong></div>
                        <div class="col-6">${new Date().toLocaleDateString('ar-SA')}</div>
                    </div>
                    <hr>
                    <p class="text-muted">سيتم إضافة المزيد من التفاصيل قريباً</p>
                </div>
            `,
            width: '600px',
            confirmButtonText: 'إغلاق'
        });
    }, 1500);
}

// طباعة الفاتورة
function printInvoice(invoiceId, type) {
    Swal.fire({
        title: 'طباعة الفاتورة',
        text: `هل تريد طباعة فاتورة ${type === 'sale' ? 'المبيعات' : 'المشتريات'} رقم ${invoiceId}؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'طباعة',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // فتح نافذة طباعة
            const printWindow = window.open(`print_invoice.php?id=${invoiceId}&type=${type}`, '_blank');
            if (printWindow) {
                printWindow.onload = function() {
                    printWindow.print();
                };
            }
        }
    });
}

// تصدير الفاتورة
function exportInvoice(invoiceId, type) {
    Swal.fire({
        title: 'تصدير الفاتورة',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">تنسيق التصدير:</label>
                    <select id="exportFormat" class="form-select">
                        <option value="pdf">PDF</option>
                        <option value="excel">Excel</option>
                        <option value="word">Word</option>
                    </select>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تصدير',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            return document.getElementById('exportFormat').value;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const format = result.value;
            window.open(`export_invoice.php?id=${invoiceId}&type=${type}&format=${format}`, '_blank');
        }
    });
}

// تصدير التقرير المالي
function exportFinancialReport() {
    Swal.fire({
        title: 'تصدير التقرير المالي',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">تنسيق التصدير:</label>
                    <select id="reportFormat" class="form-select">
                        <option value="excel">Excel</option>
                        <option value="pdf">PDF</option>
                        <option value="csv">CSV</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">البيانات المطلوبة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeSummary" checked>
                        <label class="form-check-label" for="includeSummary">الملخص المالي</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeSales" checked>
                        <label class="form-check-label" for="includeSales">فواتير المبيعات</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includePurchases" checked>
                        <label class="form-check-label" for="includePurchases">فواتير المشتريات</label>
                    </div>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تصدير',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            return {
                format: document.getElementById('reportFormat').value,
                summary: document.getElementById('includeSummary').checked,
                sales: document.getElementById('includeSales').checked,
                purchases: document.getElementById('includePurchases').checked
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', result.value.format);
            params.set('include_summary', result.value.summary ? '1' : '0');
            params.set('include_sales', result.value.sales ? '1' : '0');
            params.set('include_purchases', result.value.purchases ? '1' : '0');

            window.open('admin_export.php?' + params.toString(), '_blank');
        }
    });
}

// طباعة التقرير المالي
function printFinancialReport() {
    window.print();
}

// إنشاء تقرير مقارنة
function generateComparison() {
    Swal.fire({
        title: 'تقرير المقارنة',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">نوع المقارنة:</label>
                    <select id="comparisonType" class="form-select">
                        <option value="monthly">مقارنة شهرية</option>
                        <option value="quarterly">مقارنة ربع سنوية</option>
                        <option value="yearly">مقارنة سنوية</option>
                        <option value="users">مقارنة بين المستخدمين</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">الفترة الأولى (من):</label>
                    <input type="date" id="period1From" class="form-control">
                </div>
                <div class="mb-3">
                    <label class="form-label">الفترة الأولى (إلى):</label>
                    <input type="date" id="period1To" class="form-control">
                </div>
                <div class="mb-3">
                    <label class="form-label">الفترة الثانية (من):</label>
                    <input type="date" id="period2From" class="form-control">
                </div>
                <div class="mb-3">
                    <label class="form-label">الفترة الثانية (إلى):</label>
                    <input type="date" id="period2To" class="form-control">
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'إنشاء المقارنة',
        cancelButtonText: 'إلغاء',
        width: '600px'
    }).then((result) => {
        if (result.isConfirmed) {
            const params = new URLSearchParams();
            params.set('report_type', 'comparison');
            params.set('comparison_type', document.getElementById('comparisonType').value);
            params.set('period1_from', document.getElementById('period1From').value);
            params.set('period1_to', document.getElementById('period1To').value);
            params.set('period2_from', document.getElementById('period2From').value);
            params.set('period2_to', document.getElementById('period2To').value);

            window.location.href = 'admin_financial.php?' + params.toString();
        }
    });
}

// جدولة التقرير
function scheduleReport() {
    Swal.fire({
        title: 'جدولة التقرير المالي',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label class="form-label">تكرار الإرسال:</label>
                    <select id="scheduleFrequency" class="form-select">
                        <option value="daily">يومي</option>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly">شهري</option>
                        <option value="quarterly">ربع سنوي</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">البريد الإلكتروني:</label>
                    <input type="email" id="scheduleEmail" class="form-control" placeholder="<EMAIL>">
                </div>
                <div class="mb-3">
                    <label class="form-label">وقت الإرسال:</label>
                    <input type="time" id="scheduleTime" class="form-control" value="09:00">
                </div>
                <div class="mb-3">
                    <label class="form-label">نوع التقرير:</label>
                    <select id="scheduleReportType" class="form-select">
                        <option value="summary">ملخص</option>
                        <option value="detailed">تفصيلي</option>
                        <option value="complete">شامل</option>
                    </select>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'جدولة',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const email = document.getElementById('scheduleEmail').value;
            if (!email) {
                Swal.showValidationMessage('يرجى إدخال البريد الإلكتروني');
                return false;
            }
            return {
                frequency: document.getElementById('scheduleFrequency').value,
                email: email,
                time: document.getElementById('scheduleTime').value,
                report_type: document.getElementById('scheduleReportType').value
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب جدولة التقرير
            fetch('admin_financial.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'schedule_financial_report',
                    'schedule_params[frequency]': result.value.frequency,
                    'schedule_params[email]': result.value.email,
                    'schedule_params[time]': result.value.time,
                    'schedule_params[report_type]': result.value.report_type
                })
            }).then(response => {
                if (response.ok) {
                    Swal.fire('تم!', 'تم جدولة التقرير المالي بنجاح', 'success');
                } else {
                    Swal.fire('خطأ!', 'فشل في جدولة التقرير', 'error');
                }
            });
        }
    });
}

// إعادة تعيين الفلاتر
function resetFinancialFilters() {
    document.getElementById('user_id').value = '';
    document.getElementById('report_type').value = 'summary';
    document.getElementById('date_from').value = '<?php echo date('Y-m-01'); ?>';
    document.getElementById('date_to').value = '<?php echo date('Y-m-d'); ?>';
    document.getElementById('financialFilters').submit();
}
</script>


<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>