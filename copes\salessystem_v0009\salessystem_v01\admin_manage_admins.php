<?php
/**
 * صفحة إدارة المديرين
 */
require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}
// التحقق من الصلاحيات
if (!hasAdminPermission('manage_admins')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}
// الحصول على اتصال قاعدة البيانات
$db = getUnifiedDB();
if (!$db) {
    error_log("Database connection failed in admin_manage_admins.php");
    die('خطأ في الاتصال بقاعدة البيانات');
}
// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    switch ($action) {
        case 'add_admin':
            $username = trim($_POST['username'] ?? '');
            $full_name = trim($_POST['full_name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $permissions = $_POST['permissions'] ?? [];
            if (empty($username) || empty($full_name) || empty($email) || empty($password)) {
                $_SESSION['error'] = 'جميع الحقول مطلوبة';
}
else {
                // التحقق من عدم وجود المدير
                $check_stmt = $db->prepare("SELECT id FROM admins WHERE username = ? OR email = ?");
                $check_stmt->bind_param("ss", $username, $email);
                $check_stmt->execute();
                if ($check_stmt->get_result()->num_rows > 0) {
                    $_SESSION['error'] = 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل';
}
else {
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $permissions_json = json_encode($permissions);
                    $stmt = $db->prepare("INSERT INTO admins (username, full_name, email, password, permissions, status) VALUES (?, ?, ?, ?, ?, 'active')");
                    $stmt->bind_param("sssss", $username, $full_name, $email, $hashed_password, $permissions_json);
                    if ($stmt->execute()) {
                        $new_admin_id = $stmt->insert_id;
                        logActivity('admin_created', 'admins', $new_admin_id, null, null, 'تم إنشاء مدير جديد: ' . $full_name);
                        $_SESSION['success'] = 'تم إضافة المدير بنجاح';
}
else {
                        $_SESSION['error'] = 'فشل في إضافة المدير';
}
                    $stmt->close();
}
                $check_stmt->close();
}
            break;
        case 'toggle_status':
            $admin_id = intval($_POST['admin_id'] ?? 0);
            if ($admin_id > 0 && canManageAdmin($admin_id)) {
                // التحقق من إمكانية التحكم في هذا المدير
                // جلب الحالة الحالية
                $current_status_stmt = $db->prepare("SELECT status FROM admins WHERE id = ?");
                $current_status_stmt->bind_param("i", $admin_id);
                $current_status_stmt->execute();
                $result = $current_status_stmt->get_result();
                $admin_data = $result->fetch_assoc();
                $current_status_stmt->close();
                if ($admin_data) {
                    $new_status = ($admin_data['status'] == 'active') ? 'inactive' : 'active';
                    $stmt = $db->prepare("UPDATE admins SET status = ? WHERE id = ?");
                    $stmt->bind_param("si", $new_status, $admin_id);
                    if ($stmt->execute()) {
                        logActivity('admin_status_changed', 'admins', $admin_id, null, null, 'تم تغيير حالة المدير إلى: ' . $new_status);
                        $_SESSION['success'] = 'تم تغيير حالة المدير';
}
else {
                        $_SESSION['error'] = 'فشل في تغيير حالة المدير';
}
                    $stmt->close();
}
            } else {
                $_SESSION['error'] = 'ليس لديك صلاحية للتحكم في هذا المدير';
            }
            break;
        case 'update_permissions':
            $admin_id = intval($_POST['admin_id'] ?? 0);
            $permissions = $_POST['permissions'] ?? [];
            if ($admin_id > 0 && canManageAdmin($admin_id)) {
                // التحقق من إمكانية التحكم في هذا المدير
                $permissions_json = json_encode($permissions);
                $stmt = $db->prepare("UPDATE admins SET permissions = ? WHERE id = ?");
                $stmt->bind_param("si", $permissions_json, $admin_id);
                if ($stmt->execute()) {
                    logActivity('admin_permissions_updated', 'admins', $admin_id, null, null, 'تم تحديث صلاحيات المدير');
                    $_SESSION['success'] = 'تم تحديث الصلاحيات بنجاح';
}
else {
                    $_SESSION['error'] = 'فشل في تحديث الصلاحيات';
}
                $stmt->close();
            } else {
                $_SESSION['error'] = 'ليس لديك صلاحية للتحكم في هذا المدير';
            }
            break;
        case 'delete_admin':
            $admin_id = intval($_POST['admin_id'] ?? 0);
            if ($admin_id > 0 && canManageAdmin($admin_id)) {
                // التحقق من إمكانية التحكم في هذا المدير
                // جلب بيانات المدير قبل الحذف للسجل
                $admin_info_stmt = $db->prepare("SELECT full_name, username FROM admins WHERE id = ?");
                $admin_info_stmt->bind_param("i", $admin_id);
                $admin_info_stmt->execute();
                $admin_info = $admin_info_stmt->get_result()->fetch_assoc();
                $admin_info_stmt->close();

                if ($admin_info) {
                    $stmt = $db->prepare("DELETE FROM admins WHERE id = ?");
                    $stmt->bind_param("i", $admin_id);
                    if ($stmt->execute()) {
                        logActivity('admin_deleted', 'admins', $admin_id, null, null, 'تم حذف المدير: ' . $admin_info['full_name'] . ' (' . $admin_info['username'] . ')');
                        $_SESSION['success'] = 'تم حذف المدير بنجاح';
                    } else {
                        $_SESSION['error'] = 'فشل في حذف المدير';
                    }
                    $stmt->close();
                } else {
                    $_SESSION['error'] = 'المدير غير موجود';
                }
            } else {
                $_SESSION['error'] = 'ليس لديك صلاحية للتحكم في هذا المدير';
            }
            break;
}
    header("Location: admin_manage_admins.php");
    exit();
}
// جلب قائمة المديرين
$admins_result = $db->query("SELECT * FROM admins ORDER BY created_at DESC");
// قائمة الصلاحيات المتاحة
$available_permissions = [
    'manage_users' => 'إدارة المستخدمين',
    'view_all_data' => 'عرض جميع البيانات',
    'manage_system' => 'إدارة النظام',
    'view_reports' => 'عرض التقارير',
    'manage_admins' => 'إدارة المديرين'
];
require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-user-shield me-3"></i>
                        إدارة المديرين
                    </h1>
                    <p class="text-muted mb-0">إدارة حسابات المديرين والصلاحيات</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>

            <?php displayMessages(); ?>
            <!-- جدول المديرين -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-user-shield me-2"></i>
                            قائمة المديرين
                        </h5>
                        <div class="d-flex align-items-center gap-3">
                            <span class="modern-badge modern-badge-primary">
                                إجمالي: <?php echo $admins_result->num_rows; ?>
                            </span>
                            <button type="button" class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مدير جديد
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table class="modern-table table mb-0">
                            <thead>
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الصلاحيات</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($admin = $admins_result->fetch_assoc()): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-initial bg-primary rounded-circle">
                                                        <?php echo strtoupper(substr($admin['full_name'], 0, 1)); ?>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($admin['full_name']); ?></div>
                                                    <?php if ($admin['id'] == $_SESSION['admin_id']): ?>
                                                        <small class="text-primary">أنت</small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($admin['username']); ?></td>
                                        <td><?php echo htmlspecialchars($admin['email']); ?></td>
                                        <td>
                                            <?php
                                            $permissions = json_decode($admin['permissions'], true) ?? [];
                                            if (!empty($permissions)):
                                                foreach ($permissions as $perm):
                                                    if (isset($available_permissions[$perm])):
                                            ?>
                                                <span class="badge bg-secondary me-1"><?php echo $available_permissions[$perm]; ?></span>
                                            <?php
                                                    endif;
                                                endforeach;
                                            else:
                                            ?>
                                                <span class="text-muted">لا توجد صلاحيات</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo ($admin['status'] == 'active') ? 'bg-success' : 'bg-danger'; ?>">
                                                <i class="fas <?php echo ($admin['status'] == 'active') ? 'fa-check' : 'fa-times'; ?> me-1"></i>
                                                <?php echo ($admin['status'] == 'active') ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo date('Y-m-d', strtotime($admin['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($admin['last_login']): ?>
                                                <div class="text-success">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <small><?php echo date('Y-m-d', strtotime($admin['last_login'])); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-minus-circle me-1"></i>
                                                    <small>لم يسجل دخول</small>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <?php if (canManageAdmin($admin['id'])): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-info"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#permissionsModal"
                                                            data-admin-id="<?php echo $admin['id']; ?>"
                                                            data-admin-name="<?php echo htmlspecialchars($admin['full_name']); ?>"
                                                            data-permissions='<?php echo htmlspecialchars($admin['permissions']); ?>'
                                                            title="تعديل الصلاحيات">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    <?php if ($admin['id'] != $_SESSION['admin_id']): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="toggle_status">
                                                            <input type="hidden" name="admin_id" value="<?php echo $admin['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-<?php echo ($admin['status'] == 'active') ? 'warning' : 'success'; ?>"
                                                                    title="<?php echo ($admin['status'] == 'active') ? 'إلغاء التفعيل' : 'تفعيل'; ?>"
                                                                    onclick="return confirm('هل تريد تغيير حالة هذا المدير؟')">
                                                                <i class="fas <?php echo ($admin['status'] == 'active') ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                            </button>
                                                        </form>
                                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#deleteAdminModal"
                                                                data-admin-id="<?php echo $admin['id']; ?>"
                                                                data-admin-name="<?php echo htmlspecialchars($admin['full_name']); ?>"
                                                                data-admin-username="<?php echo htmlspecialchars($admin['username']); ?>"
                                                                title="حذف المدير">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <?php if (isSuperAdmin($admin['id'])): ?>
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-crown me-1"></i>
                                                            مدير رئيسي
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">
                                                            <i class="fas fa-lock me-1"></i>
                                                            محمي
                                                        </span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main></div>
<!-- مودال إضافة مدير جديد -->
<div class="modal fade" id="addAdminModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مدير جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_admin">
                    <div class="mb-3">
                        <label for="username" class="modern-form-label">اسم المستخدم</label>
                        <input type="text" class="modern-form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="modern-form-label">الاسم الكامل</label>
                        <input type="text" class="modern-form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="modern-form-label">البريد الإلكتروني</label>
                        <input type="email" class="modern-form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="modern-form-label">كلمة المرور</label>
                        <input type="password" class="modern-form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="modern-form-label">الصلاحيات</label>
                        <?php foreach ($available_permissions as $perm => $label): ?>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="permissions[]" value="<?php echo $perm; ?>" id="perm_<?php echo $perm; ?>">
                            <label class="form-check-label" for="perm_<?php echo $perm; ?>">
                                <?php echo $label; ?>
                            </label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="modern-btn modern-btn-primary">إضافة المدير</button>
                </div>
</form>
        </div>
    </div>
</div>
<!-- مودال تعديل الصلاحيات -->
<div class="modal fade" id="permissionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل صلاحيات المدير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_permissions">
                    <input type="hidden" name="admin_id" id="permissions_admin_id">
                    <p>تعديل صلاحيات المدير: <strong id="permissions_admin_name"></strong></p>
                    <div class="mb-3">
                        <label class="modern-form-label">الصلاحيات</label>
                        <?php foreach ($available_permissions as $perm => $label): ?>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="permissions[]" value="<?php echo $perm; ?>" id="edit_perm_<?php echo $perm; ?>">
                            <label class="form-check-label" for="edit_perm_<?php echo $perm; ?>">
                                <?php echo $label; ?>
                            </label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="modern-btn modern-btn-primary">حفظ التغييرات</button>
                </div>
</form>
        </div>
    </div>
</div>

<!-- مودال تأكيد حذف المدير -->
<div class="modal fade" id="deleteAdminModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف المدير
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_admin">
                    <input type="hidden" name="admin_id" id="delete_admin_id">

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <p class="mb-3">هل أنت متأكد من حذف المدير التالي؟</p>

                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-3">
                                    <div class="avatar-initial bg-danger rounded-circle">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold" id="delete_admin_name"></div>
                                    <small class="text-muted">اسم المستخدم: <span id="delete_admin_username"></span></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <p class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم حذف جميع بيانات هذا المدير نهائياً من النظام.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="modern-btn modern-btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        حذف المدير
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تعديل الصلاحيات
document.getElementById('permissionsModal').addEventListener('show.bs.modal', function (event) {
    const button = event.relatedTarget;
    const adminId = button.getAttribute('data-admin-id');
    const adminName = button.getAttribute('data-admin-name');
    const permissions = JSON.parse(button.getAttribute('data-permissions') || '[]');

    document.getElementById('permissions_admin_id').value = adminId;
    document.getElementById('permissions_admin_name').textContent = adminName;

    // إعادة تعيين جميع الصلاحيات
    const checkboxes = document.querySelectorAll('#permissionsModal input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = permissions.includes(checkbox.value);
    });
});

// تأكيد حذف المدير
document.getElementById('deleteAdminModal').addEventListener('show.bs.modal', function (event) {
    const button = event.relatedTarget;
    const adminId = button.getAttribute('data-admin-id');
    const adminName = button.getAttribute('data-admin-name');
    const adminUsername = button.getAttribute('data-admin-username');

    document.getElementById('delete_admin_id').value = adminId;
    document.getElementById('delete_admin_name').textContent = adminName;
    document.getElementById('delete_admin_username').textContent = adminUsername;
});
</script>
<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>