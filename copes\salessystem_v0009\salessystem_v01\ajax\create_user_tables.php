<?php
require_once __DIR__.'/../config/init.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير مسموحة']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['username']) || empty($input['username'])) {
    echo json_encode(['success' => false, 'message' => 'اسم المستخدم مطلوب']);
    exit;
}

$username = trim($input['username']);

// التحقق من صحة اسم المستخدم
if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
    echo json_encode(['success' => false, 'message' => 'اسم المستخدم غير صحيح']);
    exit;
}

// التحقق من وجود المستخدم في قاعدة البيانات الرئيسية
global $main_db;
if (!$main_db || $main_db->connect_error) {
    echo json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات الرئيسية']);
    exit;
}

$stmt = $main_db->prepare("SELECT id FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
    exit;
}

$stmt->close();

// محاولة إنشاء جداول المستخدم
try {
    $result = createUserTables($username);
    
    if ($result) {
        // فحص الجداول المنشأة
        $tables = getUserTables($username);
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم إنشاء الجداول بنجاح',
            'tables' => $tables,
            'username' => $username
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في إنشاء الجداول']);
    }
} catch (Exception $e) {
    error_log("خطأ في إنشاء جداول المستخدم $username: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الجداول']);
}
?>
