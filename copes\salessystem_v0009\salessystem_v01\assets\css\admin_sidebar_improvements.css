/* تحسينات القائمة الجانبية للمدير */

/* تحسين تنظيم القائمة الجانبية */
.sidebar {
    background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
    border-right: 1px solid rgba(71, 85, 105, 0.3);
    min-height: calc(100vh - 76px);
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.15);
    position: sticky;
    top: 76px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(71, 85, 105, 0.5) transparent;
    width: 260px;
    flex-shrink: 0;
    padding: 0;
    z-index: 100;
}

/* تحسين عنوان القائمة الجانبية */
.sidebar .sidebar-brand {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(71, 85, 105, 0.4);
    margin-bottom: 0;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.sidebar .sidebar-brand h5 {
    color: #ffffff;
    font-weight: 700;
    font-size: 18px;
    margin: 0;
    text-align: center;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sidebar .sidebar-brand i {
    color: #60a5fa;
    margin-left: 8px;
    font-size: 20px;
}

/* تحسين أقسام القائمة */
.sidebar .nav-section {
    padding: 20px 20px 8px;
    margin-top: 0;
    border-top: none;
}

.sidebar .nav-section:first-child {
    padding-top: 16px;
}

.sidebar .nav-section-title {
    font-size: 11px;
    font-weight: 700;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 12px;
    padding: 0 4px;
    position: relative;
}

.sidebar .nav-section-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #60a5fa 0%, transparent 100%);
    border-radius: 1px;
}

/* تحسين عناصر القائمة */
.sidebar .nav-item {
    margin-bottom: 2px;
}

.sidebar .nav-link {
    color: #94a3b8 !important;
    padding: 12px 20px;
    border-radius: 10px;
    margin: 2px 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    text-decoration: none;
    border: 1px solid transparent;
    background: transparent;
    overflow: hidden;
}

/* تأثير الخلفية المتحركة */
.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.1), transparent);
    transition: all 0.5s ease;
    z-index: 0;
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

/* حالة التمرير */
.sidebar .nav-link:hover {
    color: #e2e8f0 !important;
    background: rgba(71, 85, 105, 0.4);
    text-decoration: none;
    border-color: rgba(96, 165, 250, 0.3);
    transform: translateX(6px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* الحالة النشطة */
.sidebar .nav-link.active {
    color: #ffffff !important;
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.3) 0%, rgba(59, 130, 246, 0.2) 100%);
    border-color: #60a5fa;
    font-weight: 600;
    transform: translateX(6px);
    box-shadow: 0 6px 16px rgba(96, 165, 250, 0.3);
    position: relative;
}

.sidebar .nav-link.active::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
}

/* تحسين الأيقونات */
.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    font-size: 16px;
    transition: all 0.3s ease;
    color: #64748b !important;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.sidebar .nav-link:hover i {
    color: #94a3b8 !important;
    transform: scale(1.1);
}

.sidebar .nav-link.active i {
    color: #ebf4ff !important;
    transform: scale(1.15);
}

/* تحسين النصوص */
.sidebar .nav-link span {
    font-weight: inherit;
    letter-spacing: 0.025em;
    line-height: 1.4;
    position: relative;
    z-index: 1;
}

/* تحسين شريط التمرير */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(96, 165, 250, 0.3);
    border-radius: 3px;
    transition: all 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(96, 165, 250, 0.5);
}

/* تأثيرات خاصة للتركيز */
.sidebar .nav-link:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
    border-color: #60a5fa;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 76px;
        left: -100%;
        width: 280px;
        height: calc(100vh - 76px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1000;
        box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
    }

    .sidebar.show {
        left: 0;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
}

/* تأثيرات الظهور */
.sidebar .nav-item {
    animation: slideInRight 0.3s ease-out;
    animation-fill-mode: both;
}

.sidebar .nav-item:nth-child(1) { animation-delay: 0.1s; }
.sidebar .nav-item:nth-child(2) { animation-delay: 0.15s; }
.sidebar .nav-item:nth-child(3) { animation-delay: 0.2s; }
.sidebar .nav-item:nth-child(4) { animation-delay: 0.25s; }
.sidebar .nav-item:nth-child(5) { animation-delay: 0.3s; }
.sidebar .nav-item:nth-child(6) { animation-delay: 0.35s; }
.sidebar .nav-item:nth-child(7) { animation-delay: 0.4s; }
.sidebar .nav-item:nth-child(8) { animation-delay: 0.45s; }

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسين الوضع الداكن */
[data-theme="dark"] .sidebar {
    background: linear-gradient(180deg, #020617 0%, #0f172a 100%);
    border-right-color: rgba(71, 85, 105, 0.4);
}

[data-theme="dark"] .sidebar .sidebar-brand {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    border-bottom-color: rgba(71, 85, 105, 0.4);
}

[data-theme="dark"] .sidebar .sidebar-brand h5 {
    color: #ffffff;
}

[data-theme="dark"] .sidebar .nav-link {
    color: #64748b !important;
}

[data-theme="dark"] .sidebar .nav-link:hover {
    color: #94a3b8 !important;
    background: rgba(71, 85, 105, 0.3);
}

[data-theme="dark"] .sidebar .nav-link.active {
    color: #ffffff !important;
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.4) 0%, rgba(59, 130, 246, 0.3) 100%);
}

[data-theme="dark"] .sidebar .nav-link i {
    color: #475569 !important;
}

[data-theme="dark"] .sidebar .nav-link:hover i {
    color: #64748b !important;
}

[data-theme="dark"] .sidebar .nav-link.active i {
    color: #60a5fa !important;
}

[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb {
    background: rgba(71, 85, 105, 0.5);
}

[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(71, 85, 105, 0.7);
}

/* تحسينات إضافية للأداء */
.sidebar .nav-link,
.sidebar .nav-section-title,
.sidebar .sidebar-brand {
    will-change: transform;
}

/* تحسين الوضع الفاتح */
[data-theme="light"] .sidebar,
.sidebar {
    background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
}

[data-theme="light"] .sidebar .nav-link,
.sidebar .nav-link {
    color: #94a3b8 !important;
}

[data-theme="light"] .sidebar .nav-link:hover,
.sidebar .nav-link:hover {
    color: #e2e8f0 !important;
    background: rgba(71, 85, 105, 0.4);
}

[data-theme="light"] .sidebar .nav-link.active,
.sidebar .nav-link.active {
    color: #ffffff !important;
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.3) 0%, rgba(59, 130, 246, 0.2) 100%);
}

[data-theme="light"] .sidebar .nav-link i,
.sidebar .nav-link i {
    color: #64748b !important;
}

[data-theme="light"] .sidebar .nav-link:hover i,
.sidebar .nav-link:hover i {
    color: #94a3b8 !important;
}

[data-theme="light"] .sidebar .nav-link.active i,
.sidebar .nav-link.active i {
    color: #60a5fa !important;
}

/* تأثير النبضة للعناصر النشطة */
@keyframes pulse {
    0% {
        box-shadow: 0 6px 16px rgba(96, 165, 250, 0.3);
    }
    50% {
        box-shadow: 0 6px 20px rgba(96, 165, 250, 0.4);
    }
    100% {
        box-shadow: 0 6px 16px rgba(96, 165, 250, 0.3);
    }
}

.sidebar .nav-link.active {
    animation: pulse 2s infinite;
}

/* تحسين التباعد */
.sidebar .nav {
    padding-bottom: 20px;
}

/* تحسين الخطوط */
.sidebar {
    font-family: 'Inter', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* تحسين الشفافية */
.sidebar .nav-link {
    backdrop-filter: blur(10px);
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    backdrop-filter: blur(15px);
}

/* تحسينات إضافية للوضع الداكن */
[data-theme="dark"] .sidebar .nav-section-title {
    color: #94a3b8;
    border-bottom-color: rgba(71, 85, 105, 0.4);
}

[data-theme="dark"] .sidebar .nav-link:focus {
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
    outline: none;
}

[data-theme="dark"] .sidebar .nav-link::before {
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.15), transparent);
}

/* تحسين الاستجابة للوضع الداكن */
@media (max-width: 768px) {
    [data-theme="dark"] .sidebar {
        background: linear-gradient(180deg, #020617 0%, #0f172a 100%);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }

    [data-theme="dark"] .sidebar-overlay {
        background: rgba(0, 0, 0, 0.7);
    }
}

/* تحسين التباعد */
.sidebar .nav-item {
    margin-bottom: 2px;
}

.sidebar .nav-section {
    margin-bottom: 1.5rem;
}

.sidebar .nav-section:last-child {
    margin-bottom: 0;
}

/* تأثير النبضة للعناصر النشطة */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(96, 165, 250, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(96, 165, 250, 0);
    }
}

.sidebar .nav-link.active {
    animation: pulse 2s infinite;
}
