/* تحسينات متقدمة للبطاقات في قسم المدير */

/* بطاقات متدرجة مع تأثيرات ثلاثية الأبعاد */
.premium-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    padding: 2rem;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 24px 24px 0 0;
}

.premium-card::after {
    content: '';
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    opacity: 0;
    transition: all 0.6s ease;
    pointer-events: none;
}

.premium-card:hover {
    box-shadow: 
        0 30px 60px rgba(0, 0, 0, 0.15),
        0 12px 24px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(102, 126, 234, 0.3);
}

.premium-card:hover::after {
    opacity: 1;
    animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* بطاقات إحصائيات فاخرة مصغرة */
.luxury-stats-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 1.5rem 1.25rem;
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.08),
        0 6px 12px rgba(0, 0, 0, 0.06),
        inset 0 1px 2px rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.6);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 140px;
}

.luxury-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    border-radius: 28px 28px 0 0;
}

.luxury-stats-card::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.08) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.5s ease;
    pointer-events: none;
}

.luxury-stats-card:hover {
    box-shadow: 
        0 35px 70px rgba(0, 0, 0, 0.18),
        0 15px 30px rgba(0, 0, 0, 0.12),
        inset 0 2px 4px rgba(255, 255, 255, 1);
    border-color: rgba(102, 126, 234, 0.5);
}

.luxury-stats-card:hover::after {
    opacity: 1;
    width: 300px;
    height: 300px;
}

/* أيقونات فاخرة مصغرة */
.luxury-icon {
    width: 50px;
    height: 50px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    margin-bottom: 1rem;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 8px rgba(102, 126, 234, 0.12),
        inset 0 1px 2px rgba(255, 255, 255, 0.8);
    float: right;
}

.luxury-icon::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    border-radius: 26px;
    z-index: -1;
    opacity: 0;
    transition: all 0.4s ease;
}

.luxury-stats-card:hover .luxury-icon {
    background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 
        0 15px 30px rgba(102, 126, 234, 0.4),
        inset 0 1px 2px rgba(255, 255, 255, 0.3);
}

.luxury-stats-card:hover .luxury-icon::before {
    opacity: 1;
}

/* قيم إحصائيات فاخرة مصغرة */
.luxury-value {
    font-size: 1.75rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.1;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.luxury-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.luxury-change {
    font-size: 0.7rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    display: inline-block;
    box-shadow: 0 1px 4px rgba(102, 126, 234, 0.12);
}

/* الوضع الداكن للبطاقات الفاخرة */
[data-theme="dark"] .premium-card,
[data-theme="dark"] .luxury-stats-card {
    background: linear-gradient(145deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.8),
        0 8px 18px rgba(0, 0, 0, 0.6),
        inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .premium-card:hover,
[data-theme="dark"] .luxury-stats-card:hover {
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.9),
        0 12px 25px rgba(0, 0, 0, 0.7),
        inset 0 1px 2px rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.6);
}

[data-theme="dark"] .luxury-icon {
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .luxury-stats-card:hover .luxury-icon {
    box-shadow: 
        0 15px 30px rgba(102, 126, 234, 0.5),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .luxury-label {
    color: #94a3b8;
}

[data-theme="dark"] .luxury-change {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    color: #a5b4fc;
}

/* تأثيرات إضافية */
.card-glow {
    position: relative;
}

.card-glow::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    filter: blur(10px);
    transition: all 0.4s ease;
}

.card-glow:hover::before {
    opacity: 0.3;
}

/* تخطيط مضغوط للبطاقات */
.luxury-stats-card .luxury-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    height: 100%;
    gap: 0.75rem;
}

.luxury-stats-card .luxury-info {
    flex: 1;
    min-width: 0;
}

.luxury-stats-card .luxury-icon {
    margin-bottom: 0;
    flex-shrink: 0;
    align-self: flex-start;
}

/* تحسين النصوص للتخطيط المضغوط */
.luxury-stats-card .luxury-label {
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.luxury-stats-card .luxury-value {
    margin-bottom: 0.5rem;
    line-height: 1.1;
}

.luxury-stats-card .luxury-change {
    margin-top: auto;
    font-size: 0.65rem;
    line-height: 1.2;
}

/* تحسينات الاستجابة */
@media (max-width: 1200px) {
    .luxury-stats-card {
        padding: 1.25rem 1rem;
        min-height: 120px;
    }

    .luxury-value {
        font-size: 1.5rem;
    }

    .luxury-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .luxury-stats-card {
        padding: 1rem;
        border-radius: 16px;
        min-height: 100px;
    }

    .luxury-value {
        font-size: 1.25rem;
    }

    .luxury-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .luxury-label {
        font-size: 0.7rem;
    }

    .luxury-change {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }
}

/* تحسينات إضافية للوضع الداكن */
[data-theme="dark"] .card {
    background: linear-gradient(145deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .card-header {
    background: rgba(45, 55, 72, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .card-body {
    background: rgba(26, 32, 44, 0.95);
    color: #f7fafc;
}

[data-theme="dark"] .card-title {
    color: #f7fafc;
}

[data-theme="dark"] .card-text {
    color: #e2e8f0;
}

[data-theme="dark"] .card:hover {
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.8),
        0 8px 18px rgba(0, 0, 0, 0.6),
        inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-card {
    background: linear-gradient(145deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .modern-card-header {
    background: rgba(45, 55, 72, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .modern-card-body {
    background: rgba(26, 32, 44, 0.95);
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card {
    background: linear-gradient(145deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card .card-header {
    background: rgba(45, 55, 72, 0.9);
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card .card-body h3 {
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card .bg-icon {
    opacity: 0.05;
}

[data-theme="dark"] .stats-card {
    background: linear-gradient(145deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .stats-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

[data-theme="dark"] .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات إضافية للعناوين في البطاقات */
[data-theme="dark"] .card h1,
[data-theme="dark"] .card h2,
[data-theme="dark"] .card h3,
[data-theme="dark"] .card h4,
[data-theme="dark"] .card h5,
[data-theme="dark"] .card h6 {
    color: #f7fafc !important;
}

[data-theme="dark"] .modern-card h1,
[data-theme="dark"] .modern-card h2,
[data-theme="dark"] .modern-card h3,
[data-theme="dark"] .modern-card h4,
[data-theme="dark"] .modern-card h5,
[data-theme="dark"] .modern-card h6 {
    color: #f7fafc !important;
}

[data-theme="dark"] .premium-card h1,
[data-theme="dark"] .premium-card h2,
[data-theme="dark"] .premium-card h3,
[data-theme="dark"] .premium-card h4,
[data-theme="dark"] .premium-card h5,
[data-theme="dark"] .premium-card h6 {
    color: #f7fafc !important;
}

[data-theme="dark"] .luxury-stats-card h1,
[data-theme="dark"] .luxury-stats-card h2,
[data-theme="dark"] .luxury-stats-card h3,
[data-theme="dark"] .luxury-stats-card h4,
[data-theme="dark"] .luxury-stats-card h5,
[data-theme="dark"] .luxury-stats-card h6 {
    color: #f7fafc !important;
}

[data-theme="dark"] .dashboard-card h1,
[data-theme="dark"] .dashboard-card h2,
[data-theme="dark"] .dashboard-card h3,
[data-theme="dark"] .dashboard-card h4,
[data-theme="dark"] .dashboard-card h5,
[data-theme="dark"] .dashboard-card h6 {
    color: #f7fafc !important;
}

/* تحسينات للنصوص في البطاقات */
[data-theme="dark"] .card-title {
    color: #f7fafc !important;
}

[data-theme="dark"] .card-subtitle {
    color: #e2e8f0 !important;
}

[data-theme="dark"] .card-text {
    color: #f7fafc !important;
}

[data-theme="dark"] .luxury-value {
    color: #f7fafc !important;
}

[data-theme="dark"] .luxury-label {
    color: #e2e8f0 !important;
}

[data-theme="dark"] .luxury-change {
    color: #4facfe !important;
}
