/**
 * تنسيقات خاصة بصفحة الإعدادات
 */

/* بطاقة الإعدادات الرئيسية */
.settings-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.settings-card .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-bottom: none;
    padding: 1.5rem;
}

.settings-card .card-body {
    padding: 2rem;
}

/* تنسيق التبويبات */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 10px 10px 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    margin-right: 0.25rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #ffffff;
    border-color: #e9ecef #e9ecef #ffffff;
    border-width: 2px 2px 0 2px;
    border-style: solid;
    font-weight: 600;
}

.nav-tabs .nav-link i {
    margin-right: 0.5rem;
}

/* محتوى التبويبات */
.tab-content {
    background: #ffffff;
    border-radius: 0 0 15px 15px;
    padding: 2rem;
    min-height: 400px;
}

.tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيق الحقول */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.form-control:disabled {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

/* تنسيق المفاتيح */
.form-check {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.form-check:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.form-check-input {
    margin-top: 0.25rem;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    cursor: pointer;
}

/* مفاتيح التبديل */
.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    border-radius: 3rem;
    background-color: #dee2e6;
    border: none;
    transition: all 0.3s ease;
}

.form-switch .form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* عناوين الأقسام */
h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

h5 i {
    margin-right: 0.5rem;
}

/* النصوص المساعدة */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* الأزرار */
.btn-lg {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 150px;
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
}

/* تحسينات للتصميم المتجاوب */
@media (max-width: 768px) {
    .settings-card .card-body {
        padding: 1rem;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    .nav-tabs .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .nav-tabs .nav-link i {
        display: none;
    }
    
    .btn-lg {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tabs .nav-link {
        flex: 1;
        text-align: center;
        margin-right: 0;
        margin-bottom: 0.25rem;
        border-radius: 10px;
    }
    
    .form-check {
        padding: 0.75rem;
    }
    
    h5 {
        font-size: 1.1rem;
    }
}

/* تأثيرات خاصة */
.settings-section {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-left: 4px solid #007bff;
}

.settings-section:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* تنسيق خاص للحقول المطلوبة */
.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* تنسيق رسائل التحقق */
.is-invalid {
    border-color: #dc3545;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.is-valid {
    border-color: #28a745;
}

/* تنسيق الأيقونات */
.text-primary { color: #007bff !important; }
.text-info { color: #17a2b8 !important; }
.text-warning { color: #ffc107 !important; }
.text-success { color: #28a745 !important; }
.text-secondary { color: #6c757d !important; }
.text-danger { color: #dc3545 !important; }

/* تحسينات إضافية */
.form-floating {
    position: relative;
}

.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}

/* تنسيق خاص للمعاينة */
.preview-box {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    color: #6c757d;
    margin-top: 1rem;
}

.preview-box.active {
    border-color: #007bff;
    background: #e7f3ff;
    color: #007bff;
}

/* تنسيق الإشعارات */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}
