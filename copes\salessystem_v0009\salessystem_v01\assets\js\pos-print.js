/**
 * مكتبة طباعة POS محسنة
 * تدعم الطابعات الحرارية والعادية
 */

// إعدادات الطباعة الافتراضية
const POSPrintConfig = {
    defaultWidth: '80', // عرض الورق الافتراضي (58 أو 80)
    autoPrint: false,   // طباعة تلقائية
    showPreview: true,  // عرض المعاينة
    paperSizes: {
        '58': { width: '58mm', name: 'ورق صغير (58mm)' },
        '80': { width: '80mm', name: 'ورق متوسط (80mm)' }
    }
};

/**
 * طباعة فاتورة بتنسيق POS
 * @param {number} invoiceId - معرف الفاتورة
 * @param {string} type - نوع الفاتورة (sale/purchase)
 * @param {object} options - خيارات الطباعة
 */
function printPOSInvoice(invoiceId, type = 'sale', options = {}) {
    // دمج الخيارات مع الإعدادات الافتراضية
    const config = { ...POSPrintConfig, ...options };
    
    // التحقق من صحة المعاملات
    if (!invoiceId || invoiceId <= 0) {
        showPrintError('معرف الفاتورة غير صحيح');
        return;
    }

    // إنشاء URL للطباعة
    const printUrl = buildPOSPrintUrl(invoiceId, type, config);
    
    // عرض خيارات الطباعة
    if (config.showPreview) {
        showPOSPrintDialog(printUrl, config);
    } else {
        // طباعة مباشرة
        openPOSPrintWindow(printUrl, config);
    }
}

/**
 * إنشاء URL للطباعة
 */
function buildPOSPrintUrl(invoiceId, type, config) {
    const params = new URLSearchParams({
        id: invoiceId,
        type: type,
        width: config.defaultWidth
    });
    
    if (config.autoPrint) {
        params.set('auto_print', '1');
    }
    
    return `print_pos_invoice.php?${params.toString()}`;
}

/**
 * عرض نافذة خيارات الطباعة
 */
function showPOSPrintDialog(printUrl, config) {
    // إنشاء محتوى النافذة
    const dialogContent = `
        <div class="pos-print-dialog">
            <div class="dialog-header">
                <h4><i class="fas fa-print me-2"></i>طباعة فاتورة POS</h4>
            </div>
            <div class="dialog-body">
                <div class="print-options">
                    <div class="option-group">
                        <label class="option-label">
                            <i class="fas fa-ruler me-2"></i>عرض الورق:
                        </label>
                        <select id="posPaperWidth" class="form-select">
                            ${Object.entries(config.paperSizes).map(([value, info]) => 
                                `<option value="${value}" ${value === config.defaultWidth ? 'selected' : ''}>
                                    ${info.name}
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                    
                    <div class="option-group">
                        <label class="option-label">
                            <i class="fas fa-cog me-2"></i>خيارات إضافية:
                        </label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="posAutoPrint" ${config.autoPrint ? 'checked' : ''}>
                            <label class="form-check-label" for="posAutoPrint">
                                طباعة تلقائية عند الفتح
                            </label>
                        </div>
                    </div>
                    
                    <div class="preview-info">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>نصائح للطباعة:</strong>
                            <ul class="mb-0 mt-2">
                                <li>تأكد من ضبط الطابعة على "بدون هوامش"</li>
                                <li>اختر العرض المناسب لنوع طابعتك</li>
                                <li>تأكد من وجود ورق كافي في الطابعة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dialog-footer">
                <button type="button" class="btn btn-success" onclick="executePOSPrint()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <button type="button" class="btn btn-info" onclick="previewPOSPrint()">
                    <i class="fas fa-eye me-2"></i>معاينة
                </button>
                <button type="button" class="btn btn-secondary" onclick="closePOSPrintDialog()">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
            </div>
        </div>
    `;

    // عرض النافذة باستخدام SweetAlert2
    Swal.fire({
        html: dialogContent,
        width: '500px',
        showConfirmButton: false,
        showCancelButton: false,
        customClass: {
            popup: 'pos-print-popup'
        }
    });

    // حفظ URL للاستخدام في الدوال الأخرى
    window.currentPOSPrintUrl = printUrl;
}

/**
 * تنفيذ الطباعة
 */
function executePOSPrint() {
    const width = document.getElementById('posPaperWidth').value;
    const autoPrint = document.getElementById('posAutoPrint').checked;
    
    // تحديث URL
    const url = updatePOSPrintUrl(window.currentPOSPrintUrl, { width, autoPrint });
    
    // فتح نافذة الطباعة
    openPOSPrintWindow(url, { autoPrint });
    
    // إغلاق النافذة
    Swal.close();
}

/**
 * معاينة الطباعة
 */
function previewPOSPrint() {
    const width = document.getElementById('posPaperWidth').value;
    
    // تحديث URL (بدون طباعة تلقائية للمعاينة)
    const url = updatePOSPrintUrl(window.currentPOSPrintUrl, { width, autoPrint: false });
    
    // فتح نافذة المعاينة
    openPOSPrintWindow(url, { autoPrint: false });
}

/**
 * إغلاق نافذة الطباعة
 */
function closePOSPrintDialog() {
    Swal.close();
}

/**
 * تحديث URL الطباعة
 */
function updatePOSPrintUrl(baseUrl, options) {
    const url = new URL(baseUrl, window.location.origin);
    
    if (options.width) {
        url.searchParams.set('width', options.width);
    }
    
    if (options.autoPrint) {
        url.searchParams.set('auto_print', '1');
    } else {
        url.searchParams.delete('auto_print');
    }
    
    return url.toString();
}

/**
 * فتح نافذة الطباعة
 */
function openPOSPrintWindow(url, config) {
    const windowFeatures = [
        'width=400',
        'height=600',
        'scrollbars=yes',
        'resizable=yes',
        'toolbar=no',
        'menubar=no',
        'location=no',
        'status=no'
    ].join(',');
    
    const printWindow = window.open(url, 'POSPrint', windowFeatures);
    
    if (!printWindow) {
        showPrintError('فشل في فتح نافذة الطباعة. تأكد من السماح للنوافذ المنبثقة.');
        return;
    }
    
    // التركيز على النافذة الجديدة
    printWindow.focus();
    
    // طباعة تلقائية إذا كانت مفعلة
    if (config.autoPrint) {
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
            }, 1000);
        };
    }
}

/**
 * عرض رسالة خطأ
 */
function showPrintError(message) {
    Swal.fire({
        icon: 'error',
        title: 'خطأ في الطباعة',
        text: message,
        confirmButtonText: 'موافق'
    });
}

/**
 * طباعة سريعة بالإعدادات الافتراضية
 */
function quickPOSPrint(invoiceId, type = 'sale') {
    printPOSInvoice(invoiceId, type, {
        showPreview: false,
        autoPrint: true
    });
}

/**
 * طباعة متعددة للفواتير
 */
function bulkPOSPrint(invoices, type = 'sale') {
    if (!invoices || invoices.length === 0) {
        showPrintError('لا توجد فواتير للطباعة');
        return;
    }
    
    Swal.fire({
        title: 'طباعة متعددة',
        text: `هل تريد طباعة ${invoices.length} فاتورة؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'طباعة الكل',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // طباعة كل فاتورة مع تأخير بسيط
            invoices.forEach((invoiceId, index) => {
                setTimeout(() => {
                    quickPOSPrint(invoiceId, type);
                }, index * 1000); // تأخير ثانية واحدة بين كل طباعة
            });
        }
    });
}

/**
 * إعداد أزرار الطباعة في الصفحة
 */
function setupPOSPrintButtons() {
    // البحث عن أزرار الطباعة وإضافة الوظائف
    document.querySelectorAll('[data-pos-print]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const invoiceId = this.dataset.invoiceId || this.dataset.posPrint;
            const type = this.dataset.invoiceType || 'sale';
            
            printPOSInvoice(invoiceId, type);
        });
    });
    
    // أزرار الطباعة السريعة
    document.querySelectorAll('[data-pos-quick-print]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const invoiceId = this.dataset.invoiceId || this.dataset.posQuickPrint;
            const type = this.dataset.invoiceType || 'sale';
            
            quickPOSPrint(invoiceId, type);
        });
    });
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setupPOSPrintButtons();
});

// CSS للنوافذ المنبثقة
const posDialogCSS = `
<style>
.pos-print-dialog {
    text-align: right;
    direction: rtl;
}

.dialog-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.dialog-header h4 {
    margin: 0;
    color: #495057;
}

.option-group {
    margin-bottom: 20px;
}

.option-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-check {
    margin-top: 10px;
}

.preview-info {
    margin-top: 20px;
}

.dialog-footer {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    margin-top: 20px;
    text-align: center;
}

.dialog-footer .btn {
    margin: 0 5px;
}

.pos-print-popup {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
}
</style>
`;

// إضافة CSS للصفحة
if (!document.getElementById('pos-dialog-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'pos-dialog-styles';
    styleElement.innerHTML = posDialogCSS;
    document.head.appendChild(styleElement);
}
