/**
 * ملف JavaScript محسن للطباعة
 * يحتوي على دوال لتحسين تجربة الطباعة في جميع أنحاء المشروع
 */

/**
 * طباعة عنصر محدد مع تحسينات
 * @param {string} elementId - معرف العنصر المراد طباعته
 * @param {object} options - خيارات الطباعة
 */
function printElement(elementId, options = {}) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error('العنصر غير موجود:', elementId);
        return;
    }

    // الخيارات الافتراضية
    const defaultOptions = {
        title: document.title,
        styles: true,
        removeAfterPrint: true,
        showPrintDialog: true,
        orientation: 'portrait', // portrait أو landscape
        paperSize: 'A4'
    };

    const config = { ...defaultOptions, ...options };

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    
    // بناء محتوى الطباعة
    const printContent = buildPrintContent(element, config);
    
    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم الطباعة
    printWindow.onload = function() {
        if (config.showPrintDialog) {
            printWindow.focus();
            printWindow.print();
        }
        
        if (config.removeAfterPrint) {
            printWindow.onafterprint = function() {
                printWindow.close();
            };
        }
    };
}

/**
 * بناء محتوى الطباعة
 * @param {HTMLElement} element - العنصر المراد طباعته
 * @param {object} config - إعدادات الطباعة
 * @returns {string} محتوى HTML للطباعة
 */
function buildPrintContent(element, config) {
    const styles = config.styles ? getPrintStyles(config) : '';
    const content = element.innerHTML;
    
    return `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${config.title}</title>
        ${styles}
    </head>
    <body>
        <div class="print-container">
            ${content}
        </div>
        <div class="print-info">
            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} ${new Date().toLocaleTimeString('ar-SA')}</p>
        </div>
    </body>
    </html>`;
}

/**
 * الحصول على أنماط الطباعة
 * @param {object} config - إعدادات الطباعة
 * @returns {string} أنماط CSS للطباعة
 */
function getPrintStyles(config) {
    const orientation = config.orientation === 'landscape' ? 'landscape' : 'portrait';
    const paperSize = config.paperSize || 'A4';
    
    return `
    <style>
        @page {
            size: ${paperSize} ${orientation};
            margin: 1cm 1.5cm;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
        }
        
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        html, body {
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
            color: #000 !important;
            font-family: 'Arial', 'Tahoma', sans-serif !important;
            font-size: 11pt !important;
            line-height: 1.4 !important;
            direction: rtl !important;
            text-align: right !important;
        }
        
        .print-container {
            width: 100%;
            margin: 0;
            padding: 0;
        }
        
        .no-print {
            display: none !important;
        }
        
        .table {
            width: 100% !important;
            border-collapse: collapse !important;
            margin-bottom: 15px !important;
            font-size: 10pt !important;
        }
        
        .table th {
            background: #f0f0f0 !important;
            color: #000 !important;
            border: 1px solid #000 !important;
            padding: 8px 6px !important;
            text-align: center !important;
            font-weight: bold !important;
            page-break-inside: avoid;
        }
        
        .table td {
            border: 1px solid #000 !important;
            padding: 6px 4px !important;
            text-align: center !important;
            font-size: 9pt !important;
            page-break-inside: avoid;
        }
        
        .table tr {
            page-break-inside: avoid;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
            margin-bottom: 15px !important;
            page-break-inside: avoid;
        }
        
        .card-header {
            background: white !important;
            color: #000 !important;
            border-bottom: 2px solid #000 !important;
            padding: 10px 0 !important;
            text-align: center !important;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: #000 !important;
            margin: 10px 0 5px 0 !important;
            page-break-after: avoid;
            font-weight: bold !important;
        }
        
        .badge {
            background: white !important;
            color: #000 !important;
            border: 1px solid #000 !important;
            padding: 2px 4px !important;
            font-size: 8pt !important;
        }
        
        .print-info {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 8pt;
            padding: 5px;
            border-top: 1px solid #000;
            background: white;
        }
        
        .page-break-avoid {
            page-break-inside: avoid !important;
        }
        
        .number {
            text-align: left !important;
            direction: ltr !important;
        }
    </style>`;
}

/**
 * طباعة الفاتورة مع تحسينات خاصة
 * @param {string} invoiceId - معرف الفاتورة
 * @param {string} invoiceType - نوع الفاتورة (sale/purchase)
 */
function printInvoice(invoiceId, invoiceType = 'sale') {
    const options = {
        title: `فاتورة ${invoiceType === 'sale' ? 'مبيعات' : 'مشتريات'} - ${invoiceId}`,
        orientation: 'portrait',
        paperSize: 'A4',
        styles: true
    };
    
    printElement('invoice-content', options);
}

/**
 * طباعة التقرير مع تحسينات خاصة
 * @param {string} reportType - نوع التقرير
 */
function printReport(reportType = 'general') {
    const options = {
        title: `تقرير ${getReportTitle(reportType)}`,
        orientation: reportType === 'account_statement' ? 'landscape' : 'portrait',
        paperSize: 'A4',
        styles: true
    };

    // استخدام العنصر المحدد للطباعة إذا كان متوفراً
    const printableContent = document.getElementById('printableContent');
    const targetElement = (printableContent && printableContent.innerHTML.trim() !== '') ? 'printableContent' : 'reportContent';

    printElement(targetElement, options);
}

/**
 * الحصول على عنوان التقرير
 * @param {string} reportType - نوع التقرير
 * @returns {string} عنوان التقرير
 */
function getReportTitle(reportType) {
    const titles = {
        'summary': 'ملخص',
        'account_statement': 'كشف حساب شامل',
        'sales_details': 'تفاصيل المبيعات',
        'purchases_details': 'تفاصيل المشتريات',
        'top_products': 'أفضل المنتجات',
        'top_customers': 'أفضل العملاء',
        'general': 'عام'
    };
    
    return titles[reportType] || 'عام';
}

/**
 * طباعة كشف الحساب مع تحسينات خاصة
 */
function printAccountStatement() {
    const options = {
        title: 'كشف الحساب الشامل',
        orientation: 'landscape',
        paperSize: 'A4',
        styles: true
    };

    // استخدام العنصر المحدد للطباعة إذا كان متوفراً
    const printableContent = document.getElementById('printableContent');
    const targetElement = (printableContent && printableContent.innerHTML.trim() !== '') ? 'printableContent' : 'reportContent';

    printElement(targetElement, options);
}

/**
 * طباعة سريعة للصفحة الحالية
 */
function quickPrint() {
    // إخفاء العناصر غير المطلوبة مؤقتاً
    const elementsToHide = document.querySelectorAll('.no-print, .navbar, .sidebar, .btn-toolbar, .pagination, .alert');
    const originalDisplay = [];
    
    elementsToHide.forEach((element, index) => {
        originalDisplay[index] = element.style.display;
        element.style.display = 'none';
    });
    
    // طباعة الصفحة
    window.print();
    
    // إعادة إظهار العناصر
    elementsToHide.forEach((element, index) => {
        element.style.display = originalDisplay[index];
    });
}

/**
 * تحضير الصفحة للطباعة
 */
function preparePrintPage() {
    // إضافة معلومات الطباعة
    const printInfo = document.createElement('div');
    printInfo.className = 'print-info';
    printInfo.innerHTML = `
        <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} ${new Date().toLocaleTimeString('ar-SA')} | 
        الصفحة: ${document.title}</p>
    `;
    
    // إضافة معلومات الطباعة في نهاية الصفحة
    document.body.appendChild(printInfo);
    
    // إعداد عنوان الصفحة للطباعة
    const originalTitle = document.title;
    
    window.addEventListener('beforeprint', function() {
        document.title = originalTitle + ' - ' + new Date().toLocaleDateString('ar-SA');
    });
    
    window.addEventListener('afterprint', function() {
        document.title = originalTitle;
        // إزالة معلومات الطباعة بعد الطباعة
        if (printInfo.parentNode) {
            printInfo.parentNode.removeChild(printInfo);
        }
    });
}

// تشغيل تحضير الطباعة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    preparePrintPage();
});

// تصدير الدوال للاستخدام العام
window.printElement = printElement;
window.printInvoice = printInvoice;
window.printReport = printReport;
window.printAccountStatement = printAccountStatement;
window.quickPrint = quickPrint;
