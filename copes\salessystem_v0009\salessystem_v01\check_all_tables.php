<?php
/**
 * فحص شامل لجميع جداول قاعدة البيانات
 */

require_once __DIR__ . '/config/init.php';

// بدء الجلسة
session_start();

$db = getUnifiedDB();
if (!$db) {
    die("فشل في الاتصال بقاعدة البيانات");
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص جداول قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .table-exists { color: #28a745; }
        .table-missing { color: #dc3545; }
        .table-structure { font-size: 12px; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4><i class="fas fa-database me-2"></i>فحص شامل لجداول قاعدة البيانات</h4>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        // قائمة الجداول المطلوبة مع أوصافها
                        $required_tables = [
                            'users' => 'جدول المستخدمين',
                            'admins' => 'جدول المديرين',
                            'activity_log' => 'جدول سجل الأنشطة',
                            'system_settings' => 'جدول إعدادات النظام',
                            'product_categories' => 'جدول فئات المنتجات',
                            'customers' => 'جدول العملاء',
                            'suppliers' => 'جدول الموردين',
                            'products' => 'جدول المنتجات',
                            'sales' => 'جدول المبيعات',
                            'purchases' => 'جدول المشتريات',
                            'sale_items' => 'جدول أصناف المبيعات',
                            'purchase_items' => 'جدول أصناف المشتريات',
                            'payments' => 'جدول المدفوعات',
                            'budgets' => 'جدول الميزانيات'
                        ];

                        // جلب قائمة الجداول الموجودة
                        $existing_tables = [];
                        $result = $db->query("SHOW TABLES");
                        if ($result) {
                            while ($row = $result->fetch_array()) {
                                $existing_tables[] = $row[0];
                            }
                        }

                        echo "<h5><i class='fas fa-list me-2'></i>حالة الجداول المطلوبة:</h5>";
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-striped table-bordered'>";
                        echo "<thead class='table-dark'>";
                        echo "<tr><th>اسم الجدول</th><th>الوصف</th><th>الحالة</th><th>عدد السجلات</th><th>الإجراءات</th></tr>";
                        echo "</thead><tbody>";

                        $missing_count = 0;
                        $existing_count = 0;

                        foreach ($required_tables as $table => $description) {
                            echo "<tr>";
                            echo "<td><code>$table</code></td>";
                            echo "<td>$description</td>";
                            
                            if (in_array($table, $existing_tables)) {
                                echo "<td><span class='table-exists'><i class='fas fa-check-circle me-1'></i>موجود</span></td>";
                                
                                // عدد السجلات
                                $count_result = $db->query("SELECT COUNT(*) as count FROM `$table`");
                                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                                echo "<td><span class='badge bg-primary'>$count</span></td>";
                                
                                echo "<td>";
                                echo "<button class='btn btn-sm btn-outline-info me-1' onclick='showTableStructure(\"$table\")'>هيكل الجدول</button>";
                                if ($count > 0) {
                                    echo "<button class='btn btn-sm btn-outline-success' onclick='showTableData(\"$table\")'>عرض البيانات</button>";
                                }
                                echo "</td>";
                                
                                $existing_count++;
                            } else {
                                echo "<td><span class='table-missing'><i class='fas fa-times-circle me-1'></i>غير موجود</span></td>";
                                echo "<td>-</td>";
                                echo "<td><button class='btn btn-sm btn-danger' onclick='createTable(\"$table\")'>إنشاء الجدول</button></td>";
                                $missing_count++;
                            }
                            
                            echo "</tr>";
                        }

                        echo "</tbody></table>";
                        echo "</div>";

                        // إحصائيات
                        echo "<div class='row mt-4'>";
                        echo "<div class='col-md-4'>";
                        echo "<div class='card bg-success text-white'>";
                        echo "<div class='card-body text-center'>";
                        echo "<h3>$existing_count</h3>";
                        echo "<p>جداول موجودة</p>";
                        echo "</div></div></div>";

                        echo "<div class='col-md-4'>";
                        echo "<div class='card bg-danger text-white'>";
                        echo "<div class='card-body text-center'>";
                        echo "<h3>$missing_count</h3>";
                        echo "<p>جداول مفقودة</p>";
                        echo "</div></div></div>";

                        echo "<div class='col-md-4'>";
                        echo "<div class='card bg-info text-white'>";
                        echo "<div class='card-body text-center'>";
                        $total = count($required_tables);
                        echo "<h3>$total</h3>";
                        echo "<p>إجمالي الجداول</p>";
                        echo "</div></div></div>";
                        echo "</div>";

                        // الجداول الإضافية الموجودة
                        $extra_tables = array_diff($existing_tables, array_keys($required_tables));
                        if (!empty($extra_tables)) {
                            echo "<h5 class='mt-4'><i class='fas fa-plus-circle me-2'></i>جداول إضافية موجودة:</h5>";
                            echo "<div class='row'>";
                            foreach ($extra_tables as $table) {
                                $count_result = $db->query("SELECT COUNT(*) as count FROM `$table`");
                                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                                echo "<div class='col-md-3 mb-2'>";
                                echo "<div class='card'>";
                                echo "<div class='card-body text-center'>";
                                echo "<h6><code>$table</code></h6>";
                                echo "<span class='badge bg-secondary'>$count سجل</span>";
                                echo "</div></div></div>";
                            }
                            echo "</div>";
                        }

                        // اختبار الدوال
                        echo "<h5 class='mt-4'><i class='fas fa-cogs me-2'></i>اختبار دوال النظام:</h5>";
                        echo "<div class='row'>";
                        
                        // اختبار getInvoiceSetting
                        echo "<div class='col-md-6'>";
                        echo "<div class='card'>";
                        echo "<div class='card-header'>دالة getInvoiceSetting</div>";
                        echo "<div class='card-body'>";
                        try {
                            if (function_exists('getInvoiceSetting')) {
                                $company_name = getInvoiceSetting('company_name', 'اسم افتراضي');
                                echo "<div class='alert alert-success'>";
                                echo "<i class='fas fa-check me-2'></i>تعمل بنجاح<br>";
                                echo "<small>اسم الشركة: $company_name</small>";
                                echo "</div>";
                            } else {
                                echo "<div class='alert alert-warning'>";
                                echo "<i class='fas fa-exclamation-triangle me-2'></i>الدالة غير موجودة";
                                echo "</div>";
                            }
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<i class='fas fa-times me-2'></i>خطأ: " . $e->getMessage();
                            echo "</div>";
                        }
                        echo "</div></div></div>";

                        // اختبار getUnifiedDB
                        echo "<div class='col-md-6'>";
                        echo "<div class='card'>";
                        echo "<div class='card-header'>دالة getUnifiedDB</div>";
                        echo "<div class='card-body'>";
                        try {
                            $test_db = getUnifiedDB();
                            if ($test_db) {
                                echo "<div class='alert alert-success'>";
                                echo "<i class='fas fa-check me-2'></i>تعمل بنجاح<br>";
                                echo "<small>نوع قاعدة البيانات: " . get_class($test_db) . "</small>";
                                echo "</div>";
                            } else {
                                echo "<div class='alert alert-danger'>";
                                echo "<i class='fas fa-times me-2'></i>فشل في الاتصال";
                                echo "</div>";
                            }
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<i class='fas fa-times me-2'></i>خطأ: " . $e->getMessage();
                            echo "</div>";
                        }
                        echo "</div></div></div>";
                        echo "</div>";
                        ?>

                        <div class="mt-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <a href="setup_database_tables.php" class="btn btn-primary w-100">
                                        <i class="fas fa-tools me-2"></i>
                                        إعداد قاعدة البيانات
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <button onclick="location.reload()" class="btn btn-secondary w-100">
                                        <i class="fas fa-refresh me-2"></i>
                                        إعادة الفحص
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <a href="index.php" class="btn btn-success w-100">
                                        <i class="fas fa-home me-2"></i>
                                        الصفحة الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لعرض هيكل الجدول -->
    <div class="modal fade" id="tableStructureModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">هيكل الجدول</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="tableStructureContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showTableStructure(tableName) {
            // هنا يمكن إضافة AJAX لجلب هيكل الجدول
            document.getElementById('tableStructureContent').innerHTML = 
                '<div class="alert alert-info">جاري تحميل هيكل جدول ' + tableName + '...</div>';
            
            const modal = new bootstrap.Modal(document.getElementById('tableStructureModal'));
            modal.show();
        }

        function showTableData(tableName) {
            // فتح صفحة جديدة لعرض بيانات الجدول
            window.open('view_table_data.php?table=' + tableName, '_blank');
        }

        function createTable(tableName) {
            if (confirm('هل تريد إنشاء جدول ' + tableName + '؟')) {
                window.location.href = 'setup_database_tables.php';
            }
        }
    </script>
</body>
</html>
