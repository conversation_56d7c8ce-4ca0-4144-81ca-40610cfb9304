<?php
/**
 * إصلاح شامل لجميع مشاكل ملفات PHP
 */

// قائمة الملفات التي تحتاج إصلاح شامل
$files_to_fix = [
    'admin_users.php',
    'admin_reports.php',
    'admin_financial.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$fixed_files = [];
$errors = [];

echo "<h2>🔧 إصلاح شامل لجميع مشاكل ملفات PHP</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>🎯 المشاكل المُصلحة:</h4>";
echo "<ul>";
echo "<li>✅ إصلاح أخطاء $$ في المتغيرات</li>";
echo "<li>✅ إصلاح المصفوفات المكسورة</li>";
echo "<li>✅ إصلاح الأقواس غير المتطابقة</li>";
echo "<li>✅ إصلاح تنسيق PHP</li>";
echo "<li>✅ إصلاح JavaScript المكسور</li>";
echo "<li>✅ تنظيف الكود</li>";
echo "</ul>";
echo "</div>";

foreach ($files_to_fix as $file) {
    $file_path = __DIR__ . '/' . $file;
    
    echo "<h3>🔧 إصلاح شامل: $file</h3>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    if (!file_exists($file_path)) {
        echo "<span style='color: orange;'>⚠️ الملف غير موجود</span><br>";
        echo "</div>";
        continue;
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $file";
        echo "<span style='color: red;'>❌ فشل في قراءة الملف</span><br>";
        echo "</div>";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. إصلاح أخطاء $$ الأساسية
    $double_dollar_count = substr_count($content, '$$');
    if ($double_dollar_count > 0) {
        echo "<span style='color: orange;'>⚠️ إصلاح $double_dollar_count خطأ $$</span><br>";
        
        // إصلاح بسيط وآمن
        $content = str_replace('$$_SERVER', '$_SERVER', $content);
        $content = str_replace('$$_GET', '$_GET', $content);
        $content = str_replace('$$_POST', '$_POST', $content);
        $content = str_replace('$$_SESSION', '$_SESSION', $content);
        $content = str_replace('$$user', '$user', $content);
        $content = str_replace('$$users_stats', '$users_stats', $content);
        $content = str_replace('$$query_params', '$query_params', $content);
        $content = str_replace('$$activity', '$activity', $content);
        $content = str_replace('$$stats', '$stats', $content);
        
        $changes_made = true;
        echo "<span style='color: green;'>✅ تم إصلاح أخطاء $$</span><br>";
    }
    
    // 2. إصلاح المصفوفات المكسورة
    $array_fixes = [
        // إصلاح المصفوفات المكسورة عبر أسطر متعددة
        '/\$([a-zA-Z_][a-zA-Z0-9_]*)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => '$$$1[\'$2\']',
        '/\$_([A-Z]+)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => '$_$1[\'$2\']',
        
        // إصلاح echo المكسورة
        '/echo\s+([^;]+)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\];/' => 'echo $1[\'$2\'];',
        
        // إصلاح htmlspecialchars المكسورة
        '/htmlspecialchars\(([^)]+)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => 'htmlspecialchars($1[\'$2\']',
    ];
    
    foreach ($array_fixes as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $content = $new_content;
            $changes_made = true;
        }
    }
    
    // 3. إصلاح مشاكل PHP العامة
    $php_fixes = [
        // إصلاح الفواصل المنقوطة المفقودة
        '/\?>\s*<\?php/' => "?>\n<?php",
        
        // إصلاح المسافات الزائدة
        '/\s+\n/' => "\n",
        '/\n{3,}/' => "\n\n",
        
        // إصلاح if statements
        '/if\s*\(\s*\n/' => 'if (',
        '/\)\s*\n\s*{/' => ') {',
        
        // إصلاح الأقواس
        '/\(\s*\n\s*\'/' => "(\'",
        '/\'\s*\n\s*\)/' => "\')",
        
        // إصلاح الفاصلة المنقوطة
        '/\]\s*\n\s*;/' => '];',
    ];
    
    foreach ($php_fixes as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $content = $new_content;
            $changes_made = true;
        }
    }
    
    // 4. إصلاح JavaScript المكسور
    if (strpos($content, '<script>') !== false) {
        // إصلاح دوال JavaScript المكسورة
        $js_fixes = [
            '/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(\s*\)\s*\{\s*\n/' => 'function $1() {' . "\n",
            '/\}\s*\n\s*function/' => "}\n\nfunction",
            '/;\s*\n\s*}/' => ";\n}",
        ];
        
        foreach ($js_fixes as $pattern => $replacement) {
            $new_content = preg_replace($pattern, $replacement, $content);
            if ($new_content !== $content) {
                $content = $new_content;
                $changes_made = true;
            }
        }
        echo "<span style='color: green;'>✅ تم إصلاح JavaScript</span><br>";
    }
    
    // 5. تنظيف نهائي
    $content = trim($content);
    
    // التحقق من التحسينات
    $new_double_dollar_count = substr_count($content, '$$');
    if ($new_double_dollar_count < $double_dollar_count) {
        echo "<span style='color: green;'>✅ تم تقليل أخطاء $$ من $double_dollar_count إلى $new_double_dollar_count</span><br>";
    }
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.comprehensive_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $fixed_files[] = $file;
            echo "<span style='color: green; font-weight: bold;'>✅ تم حفظ جميع الإصلاحات</span><br>";
            echo "<span style='color: blue;'>📁 نسخة احتياطية: " . basename($backup_path) . "</span><br>";
            
            // إحصائيات
            $old_lines = substr_count($original_content, "\n");
            $new_lines = substr_count($content, "\n");
            echo "<span style='color: gray;'>📊 الأسطر: $old_lines → $new_lines</span><br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $file";
            echo "<span style='color: red;'>❌ فشل في حفظ الملف</span><br>";
        }
    } else {
        echo "<span style='color: blue;'>ℹ️ لا توجد تغييرات مطلوبة</span><br>";
    }
    
    echo "</div>";
}

// عرض الملخص
echo "<h3>📋 ملخص الإصلاح الشامل</h3>";

if (!empty($fixed_files)) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>✅ الملفات المُصلحة:</h4>";
    foreach ($fixed_files as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #ffebee; color: #d32f2f; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

// رسالة النتيجة
if (count($fixed_files) > 0) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>🎉 تم إصلاح " . count($fixed_files) . " ملف بنجاح!</h3>";
    echo "<p>تم تطبيق إصلاحات شاملة على جميع الملفات.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3e0; color: #f57c00; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>⚠️ لم يتم إصلاح أي ملفات</h3>";
    echo "<p>قد تكون الملفات سليمة أو تحتاج إصلاح يدوي.</p>";
    echo "</div>";
}

// اختبار الملفات
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار الملفات:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;'>";

$all_files = [
    'admin_dashboard.php' => 'لوحة التحكم',
    'admin_users.php' => 'إدارة المستخدمين',
    'admin_activity.php' => 'سجل العمليات',
    'admin_error_logs.php' => 'سجل الأخطاء',
    'admin_reports.php' => 'التقارير',
    'admin_financial.php' => 'التقارير المالية',
    'admin_system.php' => 'إعدادات النظام'
];

foreach ($all_files as $file => $title) {
    $color = in_array($file, $fixed_files) ? '#4caf50' : '#2196f3';
    echo "<a href='$file' target='_blank' style='background: $color; color: white; padding: 10px 15px; text-decoration: none; border-radius: 8px; font-size: 14px; margin: 5px;'>$title</a>";
}
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='comprehensive_error_scanner.php' style='background: #ff9800; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🔍 فحص شامل نهائي</a>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🏠 لوحة التحكم</a>";
echo "</div>";
?>
