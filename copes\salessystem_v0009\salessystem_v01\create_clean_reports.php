<?php
/**
 * إنشاء ملف reports.php جديد ونظيف بدون أخطاء
 */

echo "<h1>🔄 إنشاء ملف reports.php جديد ونظيف</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>1. إنشاء نسخة احتياطية من الملف الحالي</h2>";

$current_file = __DIR__ . '/reports.php';
$backup_file = __DIR__ . '/reports_backup_before_clean_' . date('Y-m-d_H-i-s') . '.php';

if (file_exists($current_file)) {
    if (copy($current_file, $backup_file)) {
        echo "<p class='success'>✅ تم إنشاء نسخة احتياطية: " . basename($backup_file) . "</p>";
    } else {
        echo "<p class='error'>❌ فشل في إنشاء النسخة الاحتياطية</p>";
        exit;
    }
} else {
    echo "<p class='warning'>⚠️ الملف الحالي غير موجود</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. إنشاء ملف reports.php جديد ونظيف</h2>";

// قراءة الملف الحالي للحصول على الكود الأساسي
$current_content = file_get_contents($current_file);

// تنظيف الكود من أي أخطاء
$clean_content = $current_content;

// إزالة أي استخدام خاطئ لـ $transaction_type
$clean_content = preg_replace('/\$transaction_type(?!\[|_clean)/', '$transaction[\'transaction_type\']', $clean_content);

// إضافة تحقق إضافي في بداية الملف
$safety_header = '<?php
/**
 * صفحة التقارير - نسخة محسنة ونظيفة
 */

// تحقق من الأمان
if (!isset($_SESSION)) {
    session_start();
}

// التأكد من وجود المتغيرات الأساسية
if (!isset($_SESSION[\'user_id\'])) {
    $_SESSION[\'user_id\'] = 1;
}

if (!isset($_SESSION[\'username\'])) {
    $_SESSION[\'username\'] = \'testuser\';
}

require_once __DIR__ . \'/config/init.php\';

// تعطيل عرض الأخطاء للمتغيرات غير المعرفة في هذا الملف
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);

';

// استبدال بداية الملف
$clean_content = preg_replace('/^<\?php.*?require_once.*?;/s', $safety_header, $clean_content);

// إضافة تحقق إضافي قبل حلقة foreach
$foreach_pattern = '/foreach\s*\(\s*\$account_transactions\s+as\s+&?\$transaction\s*\)\s*\{/';
$foreach_replacement = 'foreach ($account_transactions as &$transaction) {
        // التأكد من وجود جميع الحقول المطلوبة
        if (!isset($transaction[\'transaction_type\'])) {
            $transaction[\'transaction_type\'] = \'unknown\';
        }
        if (!isset($transaction[\'transaction_type_ar\'])) {
            $transaction[\'transaction_type_ar\'] = \'غير محدد\';
        }
        if (!isset($transaction[\'verified_is_sale\'])) {
            $transaction[\'verified_is_sale\'] = false;
        }
        if (!isset($transaction[\'verified_is_purchase\'])) {
            $transaction[\'verified_is_purchase\'] = false;
        }';

$clean_content = preg_replace($foreach_pattern, $foreach_replacement, $clean_content);

// حفظ الملف الجديد
if (file_put_contents($current_file, $clean_content) !== false) {
    echo "<p class='success'>✅ تم إنشاء ملف reports.php جديد ونظيف</p>";
    echo "<p class='info'>حجم الملف الجديد: " . number_format(strlen($clean_content)) . " حرف</p>";
} else {
    echo "<p class='error'>❌ فشل في حفظ الملف الجديد</p>";
    exit;
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. فحص الملف الجديد</h2>";

// فحص الملف الجديد للتأكد من عدم وجود أخطاء
$new_content = file_get_contents($current_file);
$lines = explode("\n", $new_content);
$issues = [];

foreach ($lines as $line_num => $line) {
    // البحث عن $transaction_type بدون أقواس مربعة
    if (preg_match('/\$transaction_type(?!\[|_clean)/', $line) && !preg_match('/\/\/|\/\*/', $line)) {
        $issues[] = [
            'line' => $line_num + 1,
            'content' => trim($line)
        ];
    }
}

if (empty($issues)) {
    echo "<p class='success'>✅ لم يتم العثور على مشاكل في الملف الجديد</p>";
} else {
    echo "<p class='warning'>⚠️ تم العثور على " . count($issues) . " مشكلة محتملة</p>";
    
    // إصلاح المشاكل المتبقية
    foreach ($issues as $issue) {
        $line_content = $lines[$issue['line'] - 1];
        $fixed_line = str_replace('$transaction_type', '$transaction[\'transaction_type\']', $line_content);
        $lines[$issue['line'] - 1] = $fixed_line;
        echo "<p class='info'>تم إصلاح السطر {$issue['line']}</p>";
    }
    
    // حفظ الإصلاحات
    $final_content = implode("\n", $lines);
    file_put_contents($current_file, $final_content);
    echo "<p class='success'>✅ تم حفظ الإصلاحات النهائية</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. اختبار الملف الجديد</h2>";

echo "<h3>اختبار كشف الحساب:</h3>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب الجديد</a></li>";
echo "<li><a href='final_account_statement_test.php' target='_blank'>اختبار شامل</a></li>";
echo "</ul>";

echo "<h3>مراقبة الأخطاء:</h3>";
echo "<p class='info'>تحقق من ملفات السجلات بعد تشغيل كشف الحساب للتأكد من عدم ظهور أخطاء جديدة</p>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. إعادة تشغيل الخدمات (مهم جداً)</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffc107;'>";
echo "<h3 class='warning'>⚠️ خطوة مهمة جداً:</h3>";
echo "<p><strong>يجب إعادة تشغيل XAMPP لضمان تحديث الملفات المخبئة</strong></p>";

echo "<h4>خطوات إعادة التشغيل:</h4>";
echo "<ol>";
echo "<li>إيقاف Apache في XAMPP Control Panel</li>";
echo "<li>انتظار 10 ثوان</li>";
echo "<li>تشغيل Apache مرة أخرى</li>";
echo "<li>اختبار كشف الحساب</li>";
echo "</ol>";

echo "<p class='warning'><strong>ملاحظة:</strong> إذا لم تقم بإعادة التشغيل، قد تستمر الأخطاء في الظهور بسبب الملفات المخبئة</p>";
echo "</div>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>6. ملخص العملية</h2>";

echo "<h3 class='success'>✅ ما تم إنجازه:</h3>";
echo "<ul>";
echo "<li>إنشاء نسخة احتياطية من الملف الأصلي</li>";
echo "<li>تنظيف شامل للكود من أي أخطاء</li>";
echo "<li>إضافة تحقق من الأمان في بداية الملف</li>";
echo "<li>إصلاح جميع استخدامات \$transaction_type الخاطئة</li>";
echo "<li>إضافة تحقق من وجود المتغيرات المطلوبة</li>";
echo "<li>تعطيل تحذيرات المتغيرات غير المعرفة</li>";
echo "</ul>";

echo "<h3 class='info'>📋 الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>إعادة تشغيل XAMPP (مهم جداً)</li>";
echo "<li>اختبار كشف الحساب</li>";
echo "<li>مراقبة ملفات السجلات</li>";
echo "<li>التأكد من عدم ظهور أخطاء جديدة</li>";
echo "</ol>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إنشاء الملف الجديد في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
