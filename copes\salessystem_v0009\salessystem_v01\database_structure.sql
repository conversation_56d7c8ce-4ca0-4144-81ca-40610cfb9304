-- ===================================================================
-- هيكل قواعد البيانات الكاملة لمشروع salessystem_v2
-- تاريخ الإنشاء: 2024-12-19
-- ===================================================================

-- ===================================================================
-- قاعدة البيانات الرئيسية: u193708811_system_main
-- تحتوي على: المستخدمين، المدراء، سجل النشاطات
-- ===================================================================

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `email` varchar(100) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `full_name` varchar(100) DEFAULT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `status` enum('active','inactive','suspended') DEFAULT 'active',
    `last_login` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_email` (`email`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المدراء
CREATE TABLE IF NOT EXISTS `admins` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `email` varchar(100) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `full_name` varchar(100) DEFAULT NULL,
    `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
    `permissions` text DEFAULT NULL,
    `status` enum('active','inactive') DEFAULT 'active',
    `last_login` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_email` (`email`),
    KEY `idx_role` (`role`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول سجل النشاطات
CREATE TABLE IF NOT EXISTS `activity_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `user_type` enum('user','admin') DEFAULT 'user',
    `action` varchar(100) NOT NULL,
    `table_name` varchar(50) DEFAULT NULL,
    `record_id` int(11) DEFAULT NULL,
    `old_data` text DEFAULT NULL,
    `new_data` text DEFAULT NULL,
    `description` text DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_user_type` (`user_type`),
    KEY `idx_action` (`action`),
    KEY `idx_table_name` (`table_name`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ===================================================================
-- قاعدة البيانات التشغيلية: u193708811_operations
-- تحتوي على: جداول المستخدمين مع البادئة (username_tablename)
-- ===================================================================

-- جدول العملاء (مع البادئة: {username}_customers)
-- مثال: testuser_customers
CREATE TABLE IF NOT EXISTS `{username}_customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `name` varchar(255) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tax_number` varchar(50) DEFAULT NULL,
    `address` text DEFAULT NULL,
    `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_name` (`name`),
    KEY `idx_email` (`email`),
    KEY `idx_phone` (`phone`),
    KEY `idx_customer_type` (`customer_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المنتجات (مع البادئة: {username}_products)
-- مثال: testuser_products
CREATE TABLE IF NOT EXISTS `{username}_products` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `name` varchar(255) NOT NULL,
    `description` text DEFAULT NULL,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
    `category` varchar(100) DEFAULT NULL,
    `stock_quantity` decimal(10,2) DEFAULT 0.00,
    `unit` varchar(50) DEFAULT 'قطعة',
    `barcode` varchar(100) DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_name` (`name`),
    KEY `idx_category` (`category`),
    KEY `idx_barcode` (`barcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المبيعات (مع البادئة: {username}_sales)
-- مثال: testuser_sales
CREATE TABLE IF NOT EXISTS `{username}_sales` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `invoice_number` varchar(50) NOT NULL,
    `date` date NOT NULL,
    `customer_id` int(11) DEFAULT NULL,
    `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
    `notes` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    UNIQUE KEY `idx_invoice_number` (`invoice_number`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_date` (`date`),
    KEY `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المشتريات (مع البادئة: {username}_purchases)
-- مثال: testuser_purchases
CREATE TABLE IF NOT EXISTS `{username}_purchases` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `invoice_number` varchar(50) NOT NULL,
    `date` date NOT NULL,
    `customer_id` int(11) DEFAULT NULL,
    `supplier_name` varchar(255) DEFAULT NULL,
    `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
    `notes` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    UNIQUE KEY `idx_invoice_number` (`invoice_number`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_date` (`date`),
    KEY `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول عناصر المبيعات (مع البادئة: {username}_sale_items)
-- مثال: testuser_sale_items
CREATE TABLE IF NOT EXISTS `{username}_sale_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `sale_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `product_name` varchar(255) NOT NULL DEFAULT '',
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_sale_id` (`sale_id`),
    KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول عناصر المشتريات (مع البادئة: {username}_purchase_items)
-- مثال: testuser_purchase_items
CREATE TABLE IF NOT EXISTS `{username}_purchase_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `purchase_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `product_name` varchar(255) NOT NULL DEFAULT '',
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_purchase_id` (`purchase_id`),
    KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ===================================================================
-- بيانات تجريبية للاختبار
-- ===================================================================

-- إدراج مدير افتراضي
INSERT INTO `admins` (`username`, `email`, `password`, `full_name`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'super_admin', 'active');

-- إدراج مستخدم تجريبي
INSERT INTO `users` (`username`, `email`, `password`, `full_name`, `phone`, `status`) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مستخدم تجريبي', '0500000000', 'active');

-- ===================================================================
-- ملاحظات مهمة
-- ===================================================================

/*
1. قواعد البيانات المستخدمة:
   - u193708811_system_main: للمستخدمين والمدراء وسجل النشاطات
   - u193708811_operations: لجداول العمليات مع البادئة

2. نظام البادئة:
   - كل مستخدم له جداول منفصلة مع بادئة اسم المستخدم
   - مثال: testuser_customers, testuser_products, etc.

3. الأمان:
   - جميع الجداول تحتوي على user_id للعزل
   - استخدام فهارس للأداء الأمثل
   - تشفير كلمات المرور باستخدام bcrypt

4. الاستخدام:
   - استبدل {username} باسم المستخدم الفعلي
   - تأكد من إنشاء قواعد البيانات قبل تشغيل الاستعلامات
   - استخدم المعرفات الصحيحة للمستخدمين

5. كلمة المرور الافتراضية:
   - للمدير والمستخدم التجريبي: "password"
   - يُنصح بتغييرها فور الاستخدام

6. أوامر إنشاء قواعد البيانات:
   CREATE DATABASE IF NOT EXISTS `u193708811_system_main` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
   CREATE DATABASE IF NOT EXISTS `u193708811_operations` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

7. معلومات الاتصال:
   - المضيف: localhost
   - المنفذ: 3306
   - المستخدم الرئيسي: sales01 (كلمة المرور: dNz35nd5@)
   - المستخدم التشغيلي: sales02 (كلمة المرور: dNz35nd5@)
*/
