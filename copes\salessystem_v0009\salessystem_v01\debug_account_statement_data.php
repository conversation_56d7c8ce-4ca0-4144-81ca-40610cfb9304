<?php
/**
 * فحص دقيق لبيانات كشف الحساب لحل مشكلة العدد الخاطئ
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>🔍 فحص دقيق لبيانات كشف الحساب</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f8f9fa; }
    .sale-row { background-color: #d4edda; }
    .purchase-row { background-color: #fff3cd; }
</style>";

try {
    $db = getCurrentUserDB();
    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }
    
    $username = $_SESSION['username'] ?? 'testuser';
    $user_id = $_SESSION['user_id'] ?? 1;
    
    echo "<div class='section'>";
    echo "<h2>1. معلومات المستخدم الحالي</h2>";
    echo "<p class='info'>اسم المستخدم: $username</p>";
    echo "<p class='info'>معرف المستخدم: $user_id</p>";
    echo "</div>";
    
    // الحصول على أسماء الجداول
    $sales_table = getUserTableName('sales', $username);
    $purchases_table = getUserTableName('purchases', $username);
    $customers_table = getUserTableName('customers', $username);
    
    echo "<div class='section'>";
    echo "<h2>2. أسماء الجداول</h2>";
    echo "<p class='info'>جدول المبيعات: $sales_table</p>";
    echo "<p class='info'>جدول المشتريات: $purchases_table</p>";
    echo "<p class='info'>جدول العملاء: $customers_table</p>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3. فحص البيانات الفعلية</h2>";
    
    // فحص المبيعات
    echo "<h3>أ. جدول المبيعات:</h3>";
    $sales_count_query = "SELECT COUNT(*) as total_count, COUNT(CASE WHEN user_id = $user_id THEN 1 END) as user_count FROM `$sales_table`";
    $sales_count_result = $db->query($sales_count_query);
    
    if ($sales_count_result) {
        $sales_counts = $sales_count_result->fetch_assoc();
        echo "<p class='info'>إجمالي المبيعات في الجدول: {$sales_counts['total_count']}</p>";
        echo "<p class='info'>مبيعات المستخدم الحالي: {$sales_counts['user_count']}</p>";
    }
    
    // عرض تفاصيل المبيعات
    $sales_details = $db->query("SELECT id, invoice_number, date, total_amount, user_id, customer_id FROM `$sales_table` ORDER BY date DESC");
    if ($sales_details && $sales_details->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>معرف المستخدم</th><th>معرف العميل</th><th>للمستخدم الحالي؟</th></tr>";
        while ($row = $sales_details->fetch_assoc()) {
            $is_current_user = ($row['user_id'] == $user_id) ? 'نعم' : 'لا';
            $row_class = ($row['user_id'] == $user_id) ? 'sale-row' : '';
            echo "<tr class='$row_class'>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['invoice_number']}</td>";
            echo "<td>{$row['date']}</td>";
            echo "<td>{$row['total_amount']}</td>";
            echo "<td>{$row['user_id']}</td>";
            echo "<td>{$row['customer_id']}</td>";
            echo "<td>$is_current_user</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>لا توجد مبيعات في الجدول</p>";
    }
    
    // فحص المشتريات
    echo "<h3>ب. جدول المشتريات:</h3>";
    $purchases_count_query = "SELECT COUNT(*) as total_count, COUNT(CASE WHEN user_id = $user_id THEN 1 END) as user_count FROM `$purchases_table`";
    $purchases_count_result = $db->query($purchases_count_query);
    
    if ($purchases_count_result) {
        $purchases_counts = $purchases_count_result->fetch_assoc();
        echo "<p class='info'>إجمالي المشتريات في الجدول: {$purchases_counts['total_count']}</p>";
        echo "<p class='info'>مشتريات المستخدم الحالي: {$purchases_counts['user_count']}</p>";
    }
    
    // عرض تفاصيل المشتريات
    $purchases_details = $db->query("SELECT id, invoice_number, date, total_amount, user_id, customer_id FROM `$purchases_table` ORDER BY date DESC");
    if ($purchases_details && $purchases_details->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>معرف المستخدم</th><th>معرف العميل</th><th>للمستخدم الحالي؟</th></tr>";
        while ($row = $purchases_details->fetch_assoc()) {
            $is_current_user = ($row['user_id'] == $user_id) ? 'نعم' : 'لا';
            $row_class = ($row['user_id'] == $user_id) ? 'purchase-row' : '';
            echo "<tr class='$row_class'>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['invoice_number']}</td>";
            echo "<td>{$row['date']}</td>";
            echo "<td>{$row['total_amount']}</td>";
            echo "<td>{$row['user_id']}</td>";
            echo "<td>{$row['customer_id']}</td>";
            echo "<td>$is_current_user</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>لا توجد مشتريات في الجدول</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4. اختبار استعلامات كشف الحساب</h2>";
    
    $start_date = date('Y-m-01'); // أول يوم في الشهر
    $end_date = date('Y-m-d'); // اليوم الحالي
    
    echo "<p class='info'>الفترة المختبرة: $start_date إلى $end_date</p>";
    
    // اختبار استعلام المبيعات الحالي
    echo "<h3>أ. اختبار استعلام المبيعات:</h3>";
    $sales_query = "SELECT
                    s.id,
                    s.invoice_number,
                    s.date,
                    s.total_amount,
                    s.user_id,
                    s.customer_id,
                    COALESCE(s.subtotal, s.total_amount) as subtotal,
                    COALESCE(s.tax_amount, 0) as tax_amount,
                    s.payment_status,
                    COALESCE(c.name, 'عميل غير محدد') as customer_name,
                    'sale' as transaction_type,
                    'مبيعات' as transaction_type_ar,
                    'sales' as source_table,
                    'SALE' as type_verification
                    FROM `$sales_table` s
                    LEFT JOIN `$customers_table` c ON s.customer_id = c.id AND c.user_id = s.user_id
                    WHERE s.date BETWEEN '$start_date' AND '$end_date' AND s.user_id = $user_id
                    ORDER BY s.date ASC, s.id ASC";
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    echo "<strong>الاستعلام:</strong><br>";
    echo htmlspecialchars($sales_query);
    echo "</div>";
    
    $sales_result = $db->query($sales_query);
    if ($sales_result) {
        echo "<p class='success'>✅ نجح الاستعلام - عدد النتائج: " . $sales_result->num_rows . "</p>";
        
        if ($sales_result->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>معرف المستخدم</th><th>العميل</th></tr>";
            while ($row = $sales_result->fetch_assoc()) {
                echo "<tr class='sale-row'>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['invoice_number']}</td>";
                echo "<td>{$row['date']}</td>";
                echo "<td>{$row['total_amount']}</td>";
                echo "<td>{$row['user_id']}</td>";
                echo "<td>{$row['customer_name']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ فشل الاستعلام: " . $db->error . "</p>";
    }
    
    // اختبار استعلام المشتريات الحالي
    echo "<h3>ب. اختبار استعلام المشتريات:</h3>";
    $purchases_query = "SELECT
                       p.id,
                       p.invoice_number,
                       p.date,
                       p.total_amount,
                       p.user_id,
                       p.customer_id,
                       COALESCE(p.subtotal, p.total_amount) as subtotal,
                       COALESCE(p.tax_amount, 0) as tax_amount,
                       p.payment_status,
                       COALESCE(c.name, p.supplier_name, 'مورد غير محدد') as customer_name,
                       'purchase' as transaction_type,
                       'مشتريات' as transaction_type_ar,
                       'purchases' as source_table,
                       'PURCHASE' as type_verification
                       FROM `$purchases_table` p
                       LEFT JOIN `$customers_table` c ON p.customer_id = c.id AND c.user_id = p.user_id
                       WHERE p.date BETWEEN '$start_date' AND '$end_date' AND p.user_id = $user_id
                       ORDER BY p.date ASC, p.id ASC";
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    echo "<strong>الاستعلام:</strong><br>";
    echo htmlspecialchars($purchases_query);
    echo "</div>";
    
    $purchases_result = $db->query($purchases_query);
    if ($purchases_result) {
        echo "<p class='success'>✅ نجح الاستعلام - عدد النتائج: " . $purchases_result->num_rows . "</p>";
        
        if ($purchases_result->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>معرف المستخدم</th><th>العميل/المورد</th></tr>";
            while ($row = $purchases_result->fetch_assoc()) {
                echo "<tr class='purchase-row'>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['invoice_number']}</td>";
                echo "<td>{$row['date']}</td>";
                echo "<td>{$row['total_amount']}</td>";
                echo "<td>{$row['user_id']}</td>";
                echo "<td>{$row['customer_name']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ فشل الاستعلام: " . $db->error . "</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5. تحليل المشكلة</h2>";
    
    $actual_sales = $sales_counts['user_count'] ?? 0;
    $actual_purchases = $purchases_counts['user_count'] ?? 0;
    $query_sales = $sales_result ? $sales_result->num_rows : 0;
    $query_purchases = $purchases_result ? $purchases_result->num_rows : 0;
    
    echo "<table>";
    echo "<tr><th>النوع</th><th>العدد الفعلي في قاعدة البيانات</th><th>العدد من استعلام كشف الحساب</th><th>الحالة</th></tr>";
    
    $sales_status = ($actual_sales == $query_sales) ? '✅ صحيح' : '❌ خطأ';
    $purchases_status = ($actual_purchases == $query_purchases) ? '✅ صحيح' : '❌ خطأ';
    
    echo "<tr>";
    echo "<td>المبيعات</td>";
    echo "<td>$actual_sales</td>";
    echo "<td>$query_sales</td>";
    echo "<td>$sales_status</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>المشتريات</td>";
    echo "<td>$actual_purchases</td>";
    echo "<td>$query_purchases</td>";
    echo "<td>$purchases_status</td>";
    echo "</tr>";
    
    echo "</table>";
    
    if ($actual_sales != $query_sales || $actual_purchases != $query_purchases) {
        echo "<h3 class='error'>❌ تم اكتشاف مشكلة!</h3>";
        
        if ($actual_sales != $query_sales) {
            echo "<p class='error'>مشكلة في المبيعات: العدد الفعلي ($actual_sales) لا يطابق نتيجة الاستعلام ($query_sales)</p>";
        }
        
        if ($actual_purchases != $query_purchases) {
            echo "<p class='error'>مشكلة في المشتريات: العدد الفعلي ($actual_purchases) لا يطابق نتيجة الاستعلام ($query_purchases)</p>";
        }
        
        echo "<h4>الأسباب المحتملة:</h4>";
        echo "<ul>";
        echo "<li>مشكلة في شروط التاريخ (WHERE date BETWEEN)</li>";
        echo "<li>مشكلة في فلترة معرف المستخدم (WHERE user_id = $user_id)</li>";
        echo "<li>مشكلة في JOIN مع جدول العملاء</li>";
        echo "<li>بيانات خارج النطاق الزمني المحدد</li>";
        echo "</ul>";
        
    } else {
        echo "<h3 class='success'>✅ لا توجد مشكلة في الاستعلامات</h3>";
        echo "<p class='info'>المشكلة قد تكون في مكان آخر في كود كشف الحساب</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>6. الخطوات التالية</h2>";
echo "<ul>";
echo "<li><a href='fix_account_statement_queries.php'>إصلاح استعلامات كشف الحساب</a></li>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب الحالي</a></li>";
echo "<li><a href='test_account_statement.php'>اختبار شامل</a></li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الفحص في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
