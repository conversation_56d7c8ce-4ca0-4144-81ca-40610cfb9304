<?php
/**
 * تقرير حالة النظام النهائي - مبسط وواضح
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>📋 تقرير حالة النظام النهائي</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; background: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .status-card { border: 2px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .status-good { border-color: #28a745; background: #f8fff9; }
    .status-warning { border-color: #ffc107; background: #fffdf5; }
    .status-error { border-color: #dc3545; background: #fff5f5; }
    .summary-box { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 20px 0; text-align: center; }
    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
    th { background-color: #f8f9fa; font-weight: bold; }
    .metric { display: inline-block; margin: 10px 20px; text-align: center; }
    .metric-number { font-size: 2em; font-weight: bold; display: block; }
    .metric-label { font-size: 0.9em; opacity: 0.9; }
</style>";

echo "<div class='container'>";

$total_checks = 0;
$passed_checks = 0;
$failed_checks = 0;
$warnings = 0;

// 1. فحص قاعدة البيانات
echo "<div class='status-card status-good'>";
echo "<h2>🗄️ حالة قاعدة البيانات</h2>";

try {
    $db = getUnifiedDB();
    if ($db && !$db->connect_error) {
        echo "<p class='success'>✅ الاتصال بقاعدة البيانات: نجح</p>";
        $passed_checks++;
        
        // فحص الجداول الأساسية
        $tables = ['users', 'admins', 'customers', 'products', 'system_settings'];
        $table_status = [];
        
        foreach ($tables as $table) {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                $table_status[$table] = $count;
                $passed_checks++;
            } else {
                $table_status[$table] = 'مفقود';
                $failed_checks++;
            }
            $total_checks++;
        }
        
        echo "<table>";
        echo "<tr><th>الجدول</th><th>الحالة</th><th>عدد السجلات</th></tr>";
        foreach ($table_status as $table => $status) {
            $class = is_numeric($status) ? 'success' : 'error';
            $status_text = is_numeric($status) ? "موجود" : $status;
            $count_text = is_numeric($status) ? $status : '-';
            echo "<tr><td>$table</td><td class='$class'>$status_text</td><td>$count_text</td></tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p class='error'>❌ الاتصال بقاعدة البيانات: فشل</p>";
        $failed_checks++;
    }
    $total_checks++;
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    $failed_checks++;
    $total_checks++;
}

echo "</div>";

// 2. فحص الدوال الأساسية
echo "<div class='status-card status-good'>";
echo "<h2>⚙️ حالة الدوال الأساسية</h2>";

$functions = [
    'getUnifiedDB' => 'الاتصال بقاعدة البيانات',
    'isAdminLoggedIn' => 'فحص تسجيل دخول المدير',
    'hasAdminPermission' => 'فحص صلاحيات المدير',
    'getSystemSettings' => 'جلب إعدادات النظام',
    'generateInvoiceNumber' => 'إنشاء رقم فاتورة',
    'displayMessages' => 'عرض الرسائل'
];

$function_status = [];
foreach ($functions as $func => $desc) {
    if (function_exists($func)) {
        $function_status[$desc] = 'موجودة';
        $passed_checks++;
    } else {
        $function_status[$desc] = 'مفقودة';
        $failed_checks++;
    }
    $total_checks++;
}

echo "<table>";
echo "<tr><th>الدالة</th><th>الحالة</th></tr>";
foreach ($function_status as $desc => $status) {
    $class = $status == 'موجودة' ? 'success' : 'error';
    echo "<tr><td>$desc</td><td class='$class'>$status</td></tr>";
}
echo "</table>";

echo "</div>";

// 3. فحص الصفحات الرئيسية
echo "<div class='status-card status-good'>";
echo "<h2>📄 حالة الصفحات الرئيسية</h2>";

$pages = [
    'index.php' => 'الصفحة الرئيسية',
    'admin_login.php' => 'تسجيل دخول المدير',
    'admin_dashboard.php' => 'لوحة تحكم المدير',
    'admin_system.php' => 'إعدادات النظام',
    'admin_manage_admins.php' => 'إدارة المديرين'
];

$page_status = [];
foreach ($pages as $page => $desc) {
    if (file_exists($page) && filesize($page) > 0) {
        $page_status[$desc] = 'موجودة';
        $passed_checks++;
    } else {
        $page_status[$desc] = 'مفقودة';
        $failed_checks++;
    }
    $total_checks++;
}

echo "<table>";
echo "<tr><th>الصفحة</th><th>الحالة</th></tr>";
foreach ($page_status as $desc => $status) {
    $class = $status == 'موجودة' ? 'success' : 'error';
    echo "<tr><td>$desc</td><td class='$class'>$status</td></tr>";
}
echo "</table>";

echo "</div>";

// 4. ملخص شامل
$success_rate = $total_checks > 0 ? round(($passed_checks / $total_checks) * 100, 1) : 0;

echo "<div class='summary-box'>";
echo "<h2>📊 ملخص حالة النظام</h2>";

echo "<div class='metric'>";
echo "<span class='metric-number'>$success_rate%</span>";
echo "<span class='metric-label'>معدل النجاح</span>";
echo "</div>";

echo "<div class='metric'>";
echo "<span class='metric-number'>$passed_checks</span>";
echo "<span class='metric-label'>فحوصات نجحت</span>";
echo "</div>";

echo "<div class='metric'>";
echo "<span class='metric-number'>$failed_checks</span>";
echo "<span class='metric-label'>فحوصات فشلت</span>";
echo "</div>";

echo "<div class='metric'>";
echo "<span class='metric-number'>$total_checks</span>";
echo "<span class='metric-label'>إجمالي الفحوصات</span>";
echo "</div>";

echo "</div>";

// 5. تقييم الحالة النهائية
echo "<div class='status-card'>";
if ($success_rate >= 95) {
    echo "<div class='status-good'>";
    echo "<h2 class='success'>🎉 النظام يعمل بشكل ممتاز!</h2>";
    echo "<p class='success'>جميع المكونات الأساسية تعمل بشكل صحيح. النظام جاهز للاستخدام الإنتاجي.</p>";
} elseif ($success_rate >= 80) {
    echo "<div class='status-warning'>";
    echo "<h2 class='warning'>✅ النظام يعمل بشكل جيد</h2>";
    echo "<p class='warning'>معظم المكونات تعمل بشكل صحيح مع وجود بعض المشاكل البسيطة.</p>";
} else {
    echo "<div class='status-error'>";
    echo "<h2 class='error'>🚨 النظام يحتاج إلى إصلاحات</h2>";
    echo "<p class='error'>توجد مشاكل متعددة تحتاج إلى إصلاح فوري.</p>";
}
echo "</div>";
echo "</div>";

// 6. بيانات الدخول والروابط المفيدة
echo "<div class='status-card status-good'>";
echo "<h2>🔐 بيانات الدخول والروابط</h2>";

echo "<h3>بيانات تسجيل الدخول:</h3>";
echo "<table>";
echo "<tr><th>النوع</th><th>اسم المستخدم</th><th>كلمة المرور</th></tr>";
echo "<tr><td>المدير الرئيسي</td><td>superadmin</td><td>Admin@123456</td></tr>";
echo "<tr><td>مستخدم تجريبي</td><td>testuser</td><td>123456</td></tr>";
echo "</table>";

echo "<h3>الروابط المهمة:</h3>";
echo "<ul>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='admin_login.php'>تسجيل دخول المدير</a></li>";
echo "<li><a href='admin_dashboard.php'>لوحة تحكم المدير</a></li>";
echo "<li><a href='admin_system.php'>إعدادات النظام</a></li>";
echo "<li><a href='admin_manage_admins.php'>إدارة المديرين</a></li>";
echo "</ul>";

echo "<h3>أدوات الصيانة:</h3>";
echo "<ul>";
echo "<li><a href='improved_system_check.php'>الفحص المحسن</a></li>";
echo "<li><a href='auto_fix_all_issues.php'>الإصلاح التلقائي</a></li>";
echo "<li><a href='test_functions.php'>اختبار الدوال</a></li>";
echo "</ul>";

echo "</div>";

echo "</div>"; // إغلاق container

echo "<hr>";
echo "<p style='text-align: center; color: #666; margin-top: 30px;'>";
echo "<strong>تقرير حالة النظام النهائي</strong><br>";
echo "تم إجراء الفحص في: " . date('Y-m-d H:i:s') . "<br>";
echo "معدل النجاح: $success_rate% ($passed_checks من $total_checks)";
echo "</p>";

?>
