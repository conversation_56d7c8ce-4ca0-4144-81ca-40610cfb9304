<?php
/**
 * إصلاح شامل لمشكلة استعلامات كشف الحساب
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>🔧 إصلاح شامل لاستعلامات كشف الحساب</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }
</style>";

echo "<div class='section'>";
echo "<h2>1. تحليل المشكلة</h2>";

echo "<h3>المشاكل المكتشفة:</h3>";
echo "<ul>";
echo "<li><strong>مشكلة الفترة الزمنية:</strong> النظام يستخدم الشهر الحالي فقط كافتراضي</li>";
echo "<li><strong>مشكلة في شروط الاستعلام:</strong> قد تكون هناك مشاكل في WHERE clauses</li>";
echo "<li><strong>مشكلة في JOIN:</strong> قد يؤثر LEFT JOIN على النتائج</li>";
echo "<li><strong>مشكلة في فلترة المستخدم:</strong> قد تكون هناك مشاكل في user_id filtering</li>";
echo "</ul>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. تطبيق الإصلاحات</h2>";

$fixes_applied = 0;
$errors = [];

try {
    // قراءة الملف الحالي
    $file_path = __DIR__ . '/reports.php';
    if (!file_exists($file_path)) {
        throw new Exception('ملف reports.php غير موجود');
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        throw new Exception('فشل في قراءة ملف reports.php');
    }
    
    echo "<p class='info'>تم قراءة الملف بنجاح</p>";
    
    // إنشاء نسخة احتياطية
    $backup_path = __DIR__ . '/reports_backup_fix_' . date('Y-m-d_H-i-s') . '.php';
    if (file_put_contents($backup_path, $content) === false) {
        throw new Exception('فشل في إنشاء النسخة الاحتياطية');
    }
    
    echo "<p class='success'>✅ تم إنشاء نسخة احتياطية: " . basename($backup_path) . "</p>";
    
    // الإصلاح 1: تحسين الفترة الزمنية الافتراضية
    $old_date_range = '/\$start_date = isset\(\$_GET\[\'start_date\'\]\) \? \$_GET\[\'start_date\'\] : date\(\'Y-m-01\'\); \/\/ أول يوم في الشهر الحالي\s*\$end_date = isset\(\$_GET\[\'end_date\'\]\) \? \$_GET\[\'end_date\'\] : date\(\'Y-m-d\'\); \/\/ اليوم الحالي/';
    
    $new_date_range = '// تحسين الفترة الزمنية الافتراضية لتشمل جميع البيانات
$start_date = isset($_GET[\'start_date\']) ? $_GET[\'start_date\'] : date(\'Y-01-01\'); // أول يوم في السنة الحالية
$end_date = isset($_GET[\'end_date\']) ? $_GET[\'end_date\'] : date(\'Y-m-d\'); // اليوم الحالي';
    
    if (preg_match($old_date_range, $content)) {
        $content = preg_replace($old_date_range, $new_date_range, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم تحسين الفترة الزمنية الافتراضية</p>";
    } else {
        // محاولة إصلاح بديلة
        $old_start_date = '/\$start_date = isset\(\$_GET\[\'start_date\'\]\) \? \$_GET\[\'start_date\'\] : date\(\'Y-m-01\'\);/';
        $new_start_date = '$start_date = isset($_GET[\'start_date\']) ? $_GET[\'start_date\'] : date(\'Y-01-01\'); // تحسين: أول يوم في السنة';
        
        if (preg_match($old_start_date, $content)) {
            $content = preg_replace($old_start_date, $new_start_date, $content);
            $fixes_applied++;
            echo "<p class='success'>✅ تم تحسين تاريخ البداية الافتراضي</p>";
        }
    }
    
    // الإصلاح 2: تحسين استعلام المبيعات لإزالة تأثير JOIN
    $old_sales_query = '/LEFT JOIN `\$customers_table` c ON s\.customer_id = c\.id AND c\.user_id = s\.user_id/';
    $new_sales_query = 'LEFT JOIN `$customers_table` c ON s.customer_id = c.id';
    
    if (preg_match($old_sales_query, $content)) {
        $content = preg_replace($old_sales_query, $new_sales_query, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم تحسين JOIN في استعلام المبيعات</p>";
    }
    
    // الإصلاح 3: تحسين استعلام المشتريات لإزالة تأثير JOIN
    $old_purchases_query = '/LEFT JOIN `\$customers_table` c ON p\.customer_id = c\.id AND c\.user_id = p\.user_id/';
    $new_purchases_query = 'LEFT JOIN `$customers_table` c ON p.customer_id = c.id';
    
    if (preg_match($old_purchases_query, $content)) {
        $content = preg_replace($old_purchases_query, $new_purchases_query, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم تحسين JOIN في استعلام المشتريات</p>";
    }
    
    // الإصلاح 4: إضافة تسجيل مفصل للاستعلامات
    $debug_code = '
    // تسجيل مفصل للاستعلامات (للتشخيص)
    error_log("Account Statement Debug - Start Date: $start_date, End Date: $end_date, User ID: {$_SESSION[\'user_id\']}");
    error_log("Sales Query: " . $sales_query);
    ';
    
    $sales_query_position = strpos($content, '$sales_result = $db->query($sales_query);');
    if ($sales_query_position !== false) {
        $content = substr_replace($content, $debug_code . "\n    ", $sales_query_position, 0);
        $fixes_applied++;
        echo "<p class='success'>✅ تم إضافة تسجيل مفصل للاستعلامات</p>";
    }
    
    // حفظ الملف المحدث
    if (file_put_contents($file_path, $content) === false) {
        throw new Exception('فشل في حفظ الملف المحدث');
    }
    
    echo "<p class='success'>✅ تم حفظ الملف المحدث بنجاح</p>";
    echo "<p class='info'>إجمالي الإصلاحات المطبقة: $fixes_applied</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. إصلاح إضافي مباشر</h2>";

// إصلاح مباشر للفترة الزمنية
try {
    $reports_content = file_get_contents(__DIR__ . '/reports.php');
    
    // البحث عن السطر الحالي وتغييره
    $old_line = "date('Y-m-01')"; // أول يوم في الشهر الحالي
    $new_line = "date('Y-01-01')"; // أول يوم في السنة الحالية
    
    if (strpos($reports_content, $old_line) !== false) {
        $reports_content = str_replace($old_line, $new_line, $reports_content);
        file_put_contents(__DIR__ . '/reports.php', $reports_content);
        echo "<p class='success'>✅ تم تحديث الفترة الزمنية الافتراضية من الشهر الحالي إلى السنة الحالية</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في الإصلاح المباشر: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. اختبار الإصلاحات</h2>";

try {
    $db = getCurrentUserDB();
    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }
    
    $username = $_SESSION['username'] ?? 'testuser';
    $user_id = $_SESSION['user_id'] ?? 1;
    
    $sales_table = getUserTableName('sales', $username);
    $purchases_table = getUserTableName('purchases', $username);
    $customers_table = getUserTableName('customers', $username);
    
    // اختبار مع الفترة الزمنية الجديدة
    $start_date = date('Y-01-01'); // أول يوم في السنة
    $end_date = date('Y-m-d'); // اليوم الحالي
    
    echo "<p class='info'>اختبار مع الفترة الجديدة: $start_date إلى $end_date</p>";
    
    // اختبار المبيعات
    $sales_test_query = "SELECT COUNT(*) as count FROM `$sales_table` s WHERE s.date BETWEEN '$start_date' AND '$end_date' AND s.user_id = $user_id";
    $sales_test_result = $db->query($sales_test_query);
    $sales_count = $sales_test_result ? $sales_test_result->fetch_assoc()['count'] : 0;
    
    // اختبار المشتريات
    $purchases_test_query = "SELECT COUNT(*) as count FROM `$purchases_table` p WHERE p.date BETWEEN '$start_date' AND '$end_date' AND p.user_id = $user_id";
    $purchases_test_result = $db->query($purchases_test_query);
    $purchases_count = $purchases_test_result ? $purchases_test_result->fetch_assoc()['count'] : 0;
    
    echo "<p class='success'>✅ نتائج الاختبار مع الفترة الجديدة:</p>";
    echo "<ul>";
    echo "<li><strong>المبيعات:</strong> $sales_count</li>";
    echo "<li><strong>المشتريات:</strong> $purchases_count</li>";
    echo "</ul>";
    
    // مقارنة مع الفترة القديمة
    $old_start_date = date('Y-m-01'); // أول يوم في الشهر
    $old_sales_test_query = "SELECT COUNT(*) as count FROM `$sales_table` s WHERE s.date BETWEEN '$old_start_date' AND '$end_date' AND s.user_id = $user_id";
    $old_sales_test_result = $db->query($old_sales_test_query);
    $old_sales_count = $old_sales_test_result ? $old_sales_test_result->fetch_assoc()['count'] : 0;
    
    $old_purchases_test_query = "SELECT COUNT(*) as count FROM `$purchases_table` p WHERE p.date BETWEEN '$old_start_date' AND '$end_date' AND p.user_id = $user_id";
    $old_purchases_test_result = $db->query($old_purchases_test_query);
    $old_purchases_count = $old_purchases_test_result ? $old_purchases_test_result->fetch_assoc()['count'] : 0;
    
    echo "<p class='info'>مقارنة مع الفترة القديمة (الشهر الحالي فقط):</p>";
    echo "<ul>";
    echo "<li><strong>المبيعات:</strong> $old_sales_count</li>";
    echo "<li><strong>المشتريات:</strong> $old_purchases_count</li>";
    echo "</ul>";
    
    if ($sales_count != $old_sales_count || $purchases_count != $old_purchases_count) {
        echo "<p class='warning'>⚠️ هناك فرق في النتائج! هذا يؤكد أن المشكلة كانت في الفترة الزمنية</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. الخطوات التالية</h2>";

if (empty($errors)) {
    echo "<p class='success'>✅ تم تطبيق جميع الإصلاحات بنجاح!</p>";
    
    echo "<h3>اختبار النتائج:</h3>";
    echo "<ul>";
    echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب المُصلح</a></li>";
    echo "<li><a href='reports.php?report_type=account_statement&start_date=" . date('Y-01-01') . "&end_date=" . date('Y-m-d') . "' target='_blank'>كشف الحساب للسنة الكاملة</a></li>";
    echo "<li><a href='debug_account_statement_data.php' target='_blank'>فحص البيانات مرة أخرى</a></li>";
    echo "</ul>";
    
    echo "<h3>ما تم إصلاحه:</h3>";
    echo "<ul>";
    echo "<li><strong>الفترة الزمنية الافتراضية:</strong> من الشهر الحالي إلى السنة الكاملة</li>";
    echo "<li><strong>تحسين JOIN:</strong> إزالة الشروط المعقدة التي قد تؤثر على النتائج</li>";
    echo "<li><strong>إضافة تسجيل مفصل:</strong> لتتبع الاستعلامات والتشخيص</li>";
    echo "<li><strong>تحسين الاستعلامات:</strong> لضمان جلب جميع البيانات الصحيحة</li>";
    echo "</ul>";
    
} else {
    echo "<p class='error'>❌ حدثت بعض الأخطاء:</p>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li class='error'>$error</li>";
    }
    echo "</ul>";
}

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم تطبيق الإصلاحات في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
