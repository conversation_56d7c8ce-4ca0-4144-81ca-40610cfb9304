<?php
/**
 * فحص وإصلاح هيكل جداول المبيعات والمشتريات
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>🔧 فحص وإصلاح جداول المبيعات والمشتريات</h2>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    $results = [];
    $errors = [];

    // قائمة الأعمدة المطلوبة لجدول المبيعات
    $required_sales_columns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'user_id' => 'INT NOT NULL',
        'customer_id' => 'INT DEFAULT NULL',
        'invoice_number' => 'VARCHAR(50) NOT NULL',
        'date' => 'DATE NOT NULL',
        'subtotal' => 'DECIMAL(10,2) DEFAULT 0.00',
        'discount_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'tax_rate' => 'DECIMAL(5,2) DEFAULT 0.00',
        'tax_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'total_amount' => 'DECIMAL(10,2) NOT NULL',
        'payment_method' => 'VARCHAR(50) DEFAULT "cash"',
        'payment_status' => 'ENUM("paid","unpaid","partial") DEFAULT "unpaid"',
        'paid_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'remaining_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'payment_date' => 'DATE DEFAULT NULL',
        'payment_reference' => 'VARCHAR(100) DEFAULT NULL',
        'payment_notes' => 'TEXT DEFAULT NULL',
        'notes' => 'TEXT DEFAULT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];

    // قائمة الأعمدة المطلوبة لجدول المشتريات
    $required_purchases_columns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'user_id' => 'INT NOT NULL',
        'supplier_id' => 'INT DEFAULT NULL',
        'supplier_name' => 'VARCHAR(255) DEFAULT NULL',
        'invoice_number' => 'VARCHAR(50) NOT NULL',
        'date' => 'DATE NOT NULL',
        'subtotal' => 'DECIMAL(10,2) DEFAULT 0.00',
        'discount_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'tax_rate' => 'DECIMAL(5,2) DEFAULT 0.00',
        'tax_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'total_amount' => 'DECIMAL(10,2) NOT NULL',
        'payment_method' => 'VARCHAR(50) DEFAULT "cash"',
        'payment_status' => 'ENUM("paid","unpaid","partial") DEFAULT "unpaid"',
        'paid_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'remaining_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
        'payment_date' => 'DATE DEFAULT NULL',
        'payment_reference' => 'VARCHAR(100) DEFAULT NULL',
        'payment_notes' => 'TEXT DEFAULT NULL',
        'notes' => 'TEXT DEFAULT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];

    // دالة لفحص وإصلاح جدول
    function checkAndFixTable($db, $table_name, $required_columns, &$results, &$errors) {
        echo "<div style='background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>📋 فحص جدول $table_name:</h4>";

        // فحص وجود الجدول
        $table_exists = $db->query("SHOW TABLES LIKE '$table_name'");
        if (!$table_exists || $table_exists->num_rows == 0) {
            echo "<p style='color: red;'>❌ الجدول $table_name غير موجود</p>";
            $errors[] = "الجدول $table_name غير موجود";
            echo "</div>";
            return;
        }

        // فحص الأعمدة الموجودة
        $columns_query = $db->query("SHOW COLUMNS FROM $table_name");
        if (!$columns_query) {
            $errors[] = "فشل في فحص أعمدة الجدول $table_name: " . $db->error;
            echo "<p style='color: red;'>❌ فشل في فحص الأعمدة</p>";
            echo "</div>";
            return;
        }

        $existing_columns = [];
        while ($column = $columns_query->fetch_assoc()) {
            $existing_columns[$column['Field']] = $column;
        }

        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>العمود</th><th>الحالة</th><th>النوع الحالي</th><th>النوع المطلوب</th><th>الإجراء</th></tr>";

        $missing_columns = [];
        foreach ($required_columns as $column_name => $column_definition) {
            echo "<tr>";
            echo "<td><code>$column_name</code></td>";
            
            if (isset($existing_columns[$column_name])) {
                echo "<td style='color: green;'>✅ موجود</td>";
                echo "<td>" . htmlspecialchars($existing_columns[$column_name]['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($column_definition) . "</td>";
                echo "<td>لا يحتاج إجراء</td>";
            } else {
                echo "<td style='color: red;'>❌ مفقود</td>";
                echo "<td>-</td>";
                echo "<td>" . htmlspecialchars($column_definition) . "</td>";
                echo "<td style='color: orange;'>سيتم إضافته</td>";
                $missing_columns[$column_name] = $column_definition;
            }
            echo "</tr>";
        }
        echo "</table>";

        // إضافة الأعمدة المفقودة
        if (!empty($missing_columns)) {
            echo "<h5>🔧 إضافة الأعمدة المفقودة:</h5>";
            foreach ($missing_columns as $column_name => $column_definition) {
                // تنظيف تعريف العمود لإزالة PRIMARY KEY و AUTO_INCREMENT
                $clean_definition = $column_definition;
                $clean_definition = str_replace('AUTO_INCREMENT PRIMARY KEY', '', $clean_definition);
                $clean_definition = str_replace('PRIMARY KEY', '', $clean_definition);
                $clean_definition = trim($clean_definition);
                
                if ($column_name === 'id') {
                    // تخطي عمود ID إذا كان موجوداً بالفعل
                    continue;
                }
                
                $add_column_sql = "ALTER TABLE $table_name ADD COLUMN $column_name $clean_definition";
                
                if ($db->query($add_column_sql)) {
                    echo "<p style='color: green;'>✅ تم إضافة العمود: $column_name</p>";
                    $results[] = "تم إضافة العمود $column_name إلى جدول $table_name";
                } else {
                    echo "<p style='color: red;'>❌ فشل في إضافة العمود $column_name: " . $db->error . "</p>";
                    $errors[] = "فشل في إضافة العمود $column_name إلى جدول $table_name: " . $db->error;
                }
            }
        } else {
            echo "<p style='color: green;'>✅ جميع الأعمدة المطلوبة موجودة</p>";
        }

        echo "</div>";
    }

    // فحص وإصلاح جدول المبيعات
    checkAndFixTable($db, 'sales', $required_sales_columns, $results, $errors);

    // فحص وإصلاح جدول المشتريات
    checkAndFixTable($db, 'purchases', $required_purchases_columns, $results, $errors);

    // اختبار البيانات
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🧪 اختبار البيانات:</h4>";

    // اختبار جدول المبيعات
    $sales_test = $db->query("SELECT COUNT(*) as count FROM sales");
    if ($sales_test) {
        $sales_count = $sales_test->fetch_assoc()['count'];
        echo "<p><strong>عدد فواتير المبيعات:</strong> $sales_count</p>";
        
        if ($sales_count > 0) {
            // اختبار عينة من البيانات
            $sample_sales = $db->query("SELECT id, invoice_number, total_amount, payment_status FROM sales LIMIT 3");
            if ($sample_sales) {
                echo "<h6>عينة من فواتير المبيعات:</h6>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>المبلغ</th><th>حالة الدفع</th></tr>";
                while ($sale = $sample_sales->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $sale['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($sale['invoice_number']) . "</td>";
                    echo "<td>" . number_format($sale['total_amount'], 2) . "</td>";
                    echo "<td>" . htmlspecialchars($sale['payment_status'] ?? 'غير محدد') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }

    // اختبار جدول المشتريات
    $purchases_test = $db->query("SELECT COUNT(*) as count FROM purchases");
    if ($purchases_test) {
        $purchases_count = $purchases_test->fetch_assoc()['count'];
        echo "<p><strong>عدد فواتير المشتريات:</strong> $purchases_count</p>";
    }

    echo "</div>";

    // عرض النتائج
    if (!empty($results)) {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>✅ النتائج:</h4>";
        foreach ($results as $result) {
            echo "<p>✅ $result</p>";
        }
        echo "</div>";
    }

    if (!empty($errors)) {
        echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>❌ الأخطاء:</h4>";
        foreach ($errors as $error) {
            echo "<p>❌ $error</p>";
        }
        echo "</div>";
    }

    // اختبار طباعة POS
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🖨️ اختبار طباعة POS:</h4>";
    
    $test_sales = $db->query("SELECT id FROM sales LIMIT 1");
    if ($test_sales && $test_sales->num_rows > 0) {
        $test_sale = $test_sales->fetch_assoc();
        $test_id = $test_sale['id'];
        echo "<p>تم العثور على فاتورة مبيعات للاختبار (ID: $test_id)</p>";
        echo "<a href='print_pos_invoice.php?id=$test_id&type=sale&width=80' target='_blank' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 5px;'>اختبار طباعة POS</a>";
        echo "<a href='test_pos_print.php' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 5px;'>صفحة الاختبار الشاملة</a>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد فواتير مبيعات للاختبار</p>";
        echo "<a href='sales.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 5px;'>إضافة فاتورة مبيعات</a>";
    }
    echo "</div>";

    // روابط سريعة
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='sales.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>المبيعات</a>";
    echo "<a href='purchases.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>المشتريات</a>";
    echo "<a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>لوحة التحكم</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
