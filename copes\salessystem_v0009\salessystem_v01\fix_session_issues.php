<?php
/**
 * إصلاح مشاكل الجلسات في النظام
 */

echo "<h1>🔧 إصلاح مشاكل الجلسات</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }
</style>";

echo "<div class='section'>";
echo "<h2>1. فحص حالة الجلسة الحالية</h2>";

echo "<p class='info'>حالة الجلسة: ";
switch (session_status()) {
    case PHP_SESSION_DISABLED:
        echo "<span class='error'>معطلة</span>";
        break;
    case PHP_SESSION_NONE:
        echo "<span class='warning'>غير نشطة</span>";
        break;
    case PHP_SESSION_ACTIVE:
        echo "<span class='success'>نشطة</span>";
        break;
}
echo "</p>";

if (session_status() == PHP_SESSION_ACTIVE) {
    echo "<p class='info'>معرف الجلسة: " . session_id() . "</p>";
    echo "<p class='info'>اسم الجلسة: " . session_name() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. فحص الملفات للبحث عن استدعاءات session_start()</h2>";

$files_to_check = [
    'config/init.php',
    'reports.php',
    'admin_login.php',
    'login.php',
    'admin_dashboard.php',
    'admin_system.php',
    'admin_manage_admins.php'
];

$session_issues = [];

foreach ($files_to_check as $file) {
    $file_path = __DIR__ . '/' . $file;
    if (file_exists($file_path)) {
        $content = file_get_contents($file_path);
        $lines = explode("\n", $content);
        
        foreach ($lines as $line_num => $line) {
            if (strpos($line, 'session_start()') !== false) {
                $session_issues[] = [
                    'file' => $file,
                    'line' => $line_num + 1,
                    'content' => trim($line),
                    'safe' => strpos($line, 'session_status()') !== false
                ];
            }
        }
    }
}

if (empty($session_issues)) {
    echo "<p class='success'>✅ لم يتم العثور على استدعاءات session_start()</p>";
} else {
    echo "<p class='info'>تم العثور على " . count($session_issues) . " استدعاء لـ session_start()</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الملف</th><th>السطر</th><th>المحتوى</th><th>آمن؟</th></tr>";
    
    foreach ($session_issues as $issue) {
        $safe_status = $issue['safe'] ? '✅ آمن' : '❌ غير آمن';
        $row_class = $issue['safe'] ? 'success' : 'warning';
        
        echo "<tr>";
        echo "<td>{$issue['file']}</td>";
        echo "<td>{$issue['line']}</td>";
        echo "<td><code>" . htmlspecialchars($issue['content']) . "</code></td>";
        echo "<td class='$row_class'>$safe_status</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. إصلاح الاستدعاءات غير الآمنة</h2>";

$fixes_applied = 0;

foreach ($session_issues as $issue) {
    if (!$issue['safe']) {
        $file_path = __DIR__ . '/' . $issue['file'];
        $content = file_get_contents($file_path);
        
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.backup.' . date('Y-m-d_H-i-s');
        file_put_contents($backup_path, $content);
        
        // استبدال session_start() بالنسخة الآمنة
        $old_pattern = '/session_start\(\);/';
        $new_replacement = 'if (session_status() == PHP_SESSION_NONE) {
    session_start();
}';
        
        $new_content = preg_replace($old_pattern, $new_replacement, $content);
        
        if ($new_content !== $content) {
            file_put_contents($file_path, $new_content);
            echo "<p class='success'>✅ تم إصلاح {$issue['file']} (نسخة احتياطية: " . basename($backup_path) . ")</p>";
            $fixes_applied++;
        }
    }
}

if ($fixes_applied > 0) {
    echo "<p class='success'>✅ تم تطبيق $fixes_applied إصلاح</p>";
} else {
    echo "<p class='info'>لا توجد إصلاحات مطلوبة</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. إنشاء دالة مساعدة للجلسات</h2>";

$helper_function = '<?php
/**
 * دالة مساعدة آمنة لبدء الجلسة
 */
function safe_session_start() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
        return true;
    }
    return false; // الجلسة نشطة بالفعل
}

/**
 * دالة للتحقق من حالة الجلسة
 */
function is_session_active() {
    return session_status() == PHP_SESSION_ACTIVE;
}

/**
 * دالة آمنة لإنهاء الجلسة
 */
function safe_session_destroy() {
    if (session_status() == PHP_SESSION_ACTIVE) {
        session_destroy();
        return true;
    }
    return false;
}
?>';

$helper_file = __DIR__ . '/includes/session_helper.php';
if (file_put_contents($helper_file, $helper_function)) {
    echo "<p class='success'>✅ تم إنشاء ملف المساعدة: includes/session_helper.php</p>";
    
    echo "<div class='code'>";
    echo "<h4>الدوال المتاحة:</h4>";
    echo "<ul>";
    echo "<li><code>safe_session_start()</code> - بدء آمن للجلسة</li>";
    echo "<li><code>is_session_active()</code> - فحص حالة الجلسة</li>";
    echo "<li><code>safe_session_destroy()</code> - إنهاء آمن للجلسة</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<p class='error'>❌ فشل في إنشاء ملف المساعدة</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. اختبار الإصلاحات</h2>";

echo "<h3>اختبار الصفحات:</h3>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>كشف الحساب</a></li>";
echo "<li><a href='admin_login.php' target='_blank'>تسجيل دخول المدير</a></li>";
echo "<li><a href='admin_dashboard.php' target='_blank'>لوحة تحكم المدير</a></li>";
echo "</ul>";

echo "<h3>مراقبة الأخطاء:</h3>";
echo "<p class='info'>تحقق من ملفات السجلات للتأكد من عدم ظهور تحذيرات الجلسة</p>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>6. نصائح لتجنب مشاكل الجلسات</h2>";

echo "<h3>أفضل الممارسات:</h3>";
echo "<ul>";
echo "<li><strong>استخدم session_status():</strong> تحقق دائماً من حالة الجلسة قبل استدعاء session_start()</li>";
echo "<li><strong>ملف واحد للجلسة:</strong> ابدأ الجلسة في ملف واحد فقط (init.php)</li>";
echo "<li><strong>تجنب التكرار:</strong> لا تستدعي session_start() في ملفات متعددة</li>";
echo "<li><strong>استخدم الدوال المساعدة:</strong> استخدم safe_session_start() بدلاً من session_start()</li>";
echo "</ul>";

echo "<h3>الكود الموصى به:</h3>";
echo "<div class='code'>";
echo "<pre>";
echo htmlspecialchars('// بدلاً من
session_start();

// استخدم
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// أو استخدم الدالة المساعدة
safe_session_start();');
echo "</pre>";
echo "</div>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>7. ملخص الإصلاحات</h2>";

if ($fixes_applied > 0) {
    echo "<p class='success'>✅ تم إصلاح جميع مشاكل الجلسات</p>";
    
    echo "<h3>ما تم إصلاحه:</h3>";
    echo "<ul>";
    echo "<li>استبدال session_start() بالنسخة الآمنة</li>";
    echo "<li>إضافة فحص session_status() قبل بدء الجلسة</li>";
    echo "<li>إنشاء دوال مساعدة للجلسات</li>";
    echo "<li>إنشاء نسخ احتياطية من الملفات المُعدلة</li>";
    echo "</ul>";
    
} else {
    echo "<p class='info'>جميع استدعاءات الجلسة آمنة بالفعل</p>";
}

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الإصلاح في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
