<?php
/**
 * نظام إدارة الأخطاء وتسجيلها
 * Error Handler and Logging System
 */

class ErrorHandler {
    private static $logDir = __DIR__ . '/../logs/';
    private static $maxLogSize = 5242880; // 5MB
    private static $maxLogFiles = 10;
    
    /**
     * تهيئة نظام الأخطاء
     */
    public static function init() {
        // إنشاء مجلد logs إذا لم يكن موجود
        if (!is_dir(self::$logDir)) {
            mkdir(self::$logDir, 0755, true);
        }
        
        // تعيين معالج الأخطاء المخصص
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleFatalError']);
        
        // إنشاء ملف .htaccess لحماية مجلد logs
        self::createHtaccess();
    }
    
    /**
     * تسجيل خطأ في ملف log
     */
    public static function logError($level, $message, $file = '', $line = 0, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $ip = self::getClientIP();
        $userId = $_SESSION['user_id'] ?? 'Guest';
        $url = $_SERVER['REQUEST_URI'] ?? '';
        
        $logEntry = [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'user_id' => $userId,
            'ip' => $ip,
            'url' => $url,
            'user_agent' => $userAgent,
            'context' => $context
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        
        // تحديد ملف log حسب التاريخ
        $logFile = self::$logDir . 'error_' . date('Y-m-d') . '.log';
        
        // تدوير ملفات log إذا كانت كبيرة
        self::rotateLogFile($logFile);
        
        // كتابة الخطأ في الملف
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // تنظيف ملفات log القديمة
        self::cleanOldLogs();
    }
    
    /**
     * معالج الأخطاء العادية
     */
    public static function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorTypes = [
            E_ERROR => 'ERROR',
            E_WARNING => 'WARNING',
            E_PARSE => 'PARSE',
            E_NOTICE => 'NOTICE',
            E_CORE_ERROR => 'CORE_ERROR',
            E_CORE_WARNING => 'CORE_WARNING',
            E_COMPILE_ERROR => 'COMPILE_ERROR',
            E_COMPILE_WARNING => 'COMPILE_WARNING',
            E_USER_ERROR => 'USER_ERROR',
            E_USER_WARNING => 'USER_WARNING',
            E_USER_NOTICE => 'USER_NOTICE',
            E_STRICT => 'STRICT',
            E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
            E_DEPRECATED => 'DEPRECATED',
            E_USER_DEPRECATED => 'USER_DEPRECATED'
        ];
        
        $errorType = $errorTypes[$severity] ?? 'UNKNOWN';
        
        self::logError($errorType, $message, $file, $line);
        
        // إظهار رسالة للمستخدم حسب نوع الخطأ
        if (in_array($severity, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            self::showUserError('حدث خطأ في النظام. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.');
        }
        
        return true;
    }
    
    /**
     * معالج الاستثناءات
     */
    public static function handleException($exception) {
        $message = $exception->getMessage();
        $file = $exception->getFile();
        $line = $exception->getLine();
        $trace = $exception->getTraceAsString();
        
        self::logError('EXCEPTION', $message, $file, $line, ['trace' => $trace]);
        
        self::showUserError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
    }
    
    /**
     * معالج الأخطاء الفادحة
     */
    public static function handleFatalError() {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            self::logError('FATAL', $error['message'], $error['file'], $error['line']);
            
            if (!headers_sent()) {
                self::showUserError('حدث خطأ فادح في النظام. يرجى الاتصال بالدعم الفني.');
            }
        }
    }
    
    /**
     * تسجيل خطأ قاعدة البيانات
     */
    public static function logDatabaseError($query, $error, $context = []) {
        $message = "Database Error: $error | Query: $query";
        self::logError('DATABASE', $message, '', 0, $context);
        
        self::showUserError('حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.');
    }
    
    /**
     * تسجيل خطأ تسجيل الدخول
     */
    public static function logAuthError($message, $username = '', $context = []) {
        $context['username'] = $username;
        self::logError('AUTH', $message, '', 0, $context);
    }
    
    /**
     * تسجيل خطأ الصلاحيات
     */
    public static function logPermissionError($message, $context = []) {
        self::logError('PERMISSION', $message, '', 0, $context);
        
        self::showUserError('ليس لديك صلاحية للوصول لهذه الصفحة.');
    }
    
    /**
     * تسجيل خطأ الملفات
     */
    public static function logFileError($message, $filename = '', $context = []) {
        $context['filename'] = $filename;
        self::logError('FILE', $message, '', 0, $context);
        
        self::showUserError('حدث خطأ في التعامل مع الملفات. يرجى المحاولة مرة أخرى.');
    }
    
    /**
     * إظهار رسالة خطأ للمستخدم
     */
    private static function showUserError($message) {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        $_SESSION['error'] = $message;
        
        // إذا كان طلب AJAX، أرسل JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $message
            ]);
            exit;
        }
    }
    
    /**
     * الحصول على IP العميل
     */
    private static function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
                   'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    }
    
    /**
     * تدوير ملفات log
     */
    private static function rotateLogFile($logFile) {
        if (file_exists($logFile) && filesize($logFile) > self::$maxLogSize) {
            $timestamp = date('Y-m-d_H-i-s');
            $rotatedFile = str_replace('.log', "_$timestamp.log", $logFile);
            rename($logFile, $rotatedFile);
        }
    }
    
    /**
     * تنظيف ملفات log القديمة
     */
    private static function cleanOldLogs() {
        $files = glob(self::$logDir . '*.log');
        if (count($files) > self::$maxLogFiles) {
            // ترتيب الملفات حسب تاريخ التعديل
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // حذف الملفات الأقدم
            $filesToDelete = array_slice($files, 0, count($files) - self::$maxLogFiles);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * إنشاء ملف .htaccess لحماية مجلد logs
     */
    private static function createHtaccess() {
        $htaccessFile = self::$logDir . '.htaccess';
        if (!file_exists($htaccessFile)) {
            $content = "Order Deny,Allow\nDeny from all\n";
            file_put_contents($htaccessFile, $content);
        }
    }
    
    /**
     * قراءة ملفات log (للمدير فقط)
     */
    public static function getLogs($date = null, $level = null, $limit = 100) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $logFile = self::$logDir . "error_$date.log";
        if (!file_exists($logFile)) {
            return [];
        }
        
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $logs = [];
        
        foreach (array_reverse($lines) as $line) {
            $log = json_decode($line, true);
            if ($log && (!$level || $log['level'] === $level)) {
                $logs[] = $log;
                if (count($logs) >= $limit) {
                    break;
                }
            }
        }
        
        return $logs;
    }
}

// تهيئة نظام الأخطاء
ErrorHandler::init();
?>
