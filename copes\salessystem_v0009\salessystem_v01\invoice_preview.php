<?php
/**
 * معاينة الفاتورة مع معلومات الشركة
 */

require_once __DIR__ . '/config/unified_db_config.php';
require_once __DIR__ . '/includes/invoice_functions.php';

// التحقق من المعاملات
$invoice_type = $_GET['type'] ?? 'sales'; // sales أو purchase
$invoice_id = intval($_GET['id'] ?? 0);

// جلب بيانات الفاتورة
$db = getUnifiedDB();
if (!$db) {
    die('خطأ في الاتصال بقاعدة البيانات');
}

$table = $invoice_type === 'sales' ? 'sales' : 'purchases';
$customer_table = $invoice_type === 'sales' ? 'customers' : 'suppliers';
$customer_field = $invoice_type === 'sales' ? 'customer_id' : 'supplier_id';

// جلب بيانات الفاتورة
$stmt = $db->prepare("
    SELECT i.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address, c.email as customer_email
    FROM $table i 
    LEFT JOIN $customer_table c ON i.$customer_field = c.id 
    WHERE i.id = ?
");
$stmt->bind_param("i", $invoice_id);
$stmt->execute();
$invoice = $stmt->get_result()->fetch_assoc();
$stmt->close();

if (!$invoice) {
    die('الفاتورة غير موجودة');
}

// جلب عناصر الفاتورة
$items_table = $invoice_type === 'sales' ? 'sales_items' : 'purchase_items';
$items_stmt = $db->prepare("
    SELECT si.*, p.name as product_name, p.unit 
    FROM $items_table si 
    LEFT JOIN products p ON si.product_id = p.id 
    WHERE si.{$table}_id = ?
");
$items_stmt->bind_param("i", $invoice_id);
$items_stmt->execute();
$items = $items_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$items_stmt->close();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم <?php echo htmlspecialchars($invoice['invoice_number']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <?php echo getInvoiceCSS(); ?>
</head>
<body>
    <div class="container my-4">
        <div class="row">
            <div class="col-12">
                <!-- أزرار الإجراءات -->
                <div class="d-print-none mb-3">
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="printInvoice()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                        <button type="button" class="btn btn-success" onclick="downloadInvoicePDF()">
                            <i class="fas fa-download me-1"></i>تحميل PDF
                        </button>
                        <button type="button" class="btn btn-info" onclick="emailInvoice()">
                            <i class="fas fa-envelope me-1"></i>إرسال بالبريد
                        </button>
                        <a href="javascript:history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                    </div>
                </div>

                <!-- محتوى الفاتورة -->
                <div class="card">
                    <div class="card-body">
                        <!-- رأس الفاتورة مع معلومات الشركة -->
                        <?php displayInvoiceHeader($invoice_type); ?>

                        <!-- معلومات الفاتورة -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>معلومات الفاتورة:</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td><strong>رقم الفاتورة:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>التاريخ:</strong></td>
                                        <td><?php echo date('Y-m-d', strtotime($invoice['invoice_date'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>طريقة الدفع:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['payment_method'] ?? 'نقدي'); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><?php echo $invoice_type === 'sales' ? 'معلومات العميل:' : 'معلومات المورد:'; ?></h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td><strong>الاسم:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['customer_name'] ?? 'غير محدد'); ?></td>
                                    </tr>
                                    <?php if ($invoice['customer_phone']): ?>
                                    <tr>
                                        <td><strong>الهاتف:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['customer_phone']); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <?php if ($invoice['customer_address']): ?>
                                    <tr>
                                        <td><strong>العنوان:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['customer_address']); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>

                        <!-- جدول العناصر -->
                        <div class="table-responsive mb-4">
                            <table class="table table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>الوحدة</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $subtotal = 0;
                                    foreach ($items as $index => $item): 
                                        $item_total = $item['quantity'] * $item['price'];
                                        $subtotal += $item_total;
                                    ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name'] ?? 'منتج محذوف'); ?></td>
                                        <td><?php echo htmlspecialchars($item['unit'] ?? 'قطعة'); ?></td>
                                        <td><?php echo number_format($item['quantity'], 2); ?></td>
                                        <td><?php echo formatCurrency($item['price']); ?></td>
                                        <td><?php echo formatCurrency($item_total); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- الإجماليات -->
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>المجموع الفرعي:</strong></td>
                                        <td class="text-end"><?php echo formatCurrency($subtotal); ?></td>
                                    </tr>
                                    <?php if (isset($invoice['tax_amount']) && $invoice['tax_amount'] > 0): ?>
                                    <tr>
                                        <td><strong>الضريبة:</strong></td>
                                        <td class="text-end"><?php echo formatCurrency($invoice['tax_amount']); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <?php if (isset($invoice['discount_amount']) && $invoice['discount_amount'] > 0): ?>
                                    <tr>
                                        <td><strong>الخصم:</strong></td>
                                        <td class="text-end">-<?php echo formatCurrency($invoice['discount_amount']); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <tr class="table-dark">
                                        <td><strong>الإجمالي النهائي:</strong></td>
                                        <td class="text-end"><strong><?php echo formatCurrency($invoice['total_amount']); ?></strong></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <?php if (!empty($invoice['notes'])): ?>
                        <div class="mt-4">
                            <h6>ملاحظات:</h6>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
                        </div>
                        <?php endif; ?>

                        <!-- تذييل الفاتورة -->
                        <?php displayInvoiceFooter(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo getInvoiceJS(); ?>
</body>
</html>
