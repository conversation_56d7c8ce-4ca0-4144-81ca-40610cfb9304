<?php
/**
 * فحص سريع للتأكد من إصلاح مشاكل قاعدة البيانات
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>🔧 فحص سريع للإصلاحات</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";

try {
    $db = getUnifiedDB();
    if ($db && !$db->connect_error) {
        echo "<p class='success'>✅ الاتصال بقاعدة البيانات: نجح</p>";
        echo "<p class='success'>✅ نوع الاتصال: " . get_class($db) . "</p>";
        
        // اختبار استعلام بسيط
        $result = $db->query("SELECT 1 as test");
        if ($result) {
            echo "<p class='success'>✅ اختبار الاستعلام: نجح</p>";
        } else {
            echo "<p class='error'>❌ اختبار الاستعلام: فشل - " . $db->error . "</p>";
        }
    } else {
        echo "<p class='error'>❌ الاتصال بقاعدة البيانات: فشل</p>";
        if ($db) {
            echo "<p class='error'>خطأ: " . $db->connect_error . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. اختبار صفحة إعدادات النظام</h2>";

try {
    // محاولة الوصول لجدول system_settings
    $settings_test = $db->query("SELECT COUNT(*) as count FROM system_settings");
    if ($settings_test) {
        $count = $settings_test->fetch_assoc()['count'];
        echo "<p class='success'>✅ جدول system_settings: موجود ($count إعداد)</p>";
        
        // اختبار دالة getSetting
        if (function_exists('getSystemSetting')) {
            $company_name = getSystemSetting('company_name', 'اسم افتراضي');
            echo "<p class='success'>✅ دالة getSystemSetting: تعمل - اسم الشركة: $company_name</p>";
        } else {
            echo "<p class='warning'>⚠️ دالة getSystemSetting: غير موجودة</p>";
        }
        
    } else {
        echo "<p class='error'>❌ جدول system_settings: غير موجود أو لا يمكن الوصول إليه</p>";
        echo "<p class='error'>خطأ: " . $db->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في اختبار إعدادات النظام: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. اختبار صفحة إدارة المديرين</h2>";

try {
    // محاولة الوصول لجدول admins
    $admins_test = $db->query("SELECT COUNT(*) as count FROM admins");
    if ($admins_test) {
        $count = $admins_test->fetch_assoc()['count'];
        echo "<p class='success'>✅ جدول admins: موجود ($count مدير)</p>";
        
        // اختبار جلب قائمة المديرين
        $admins_list = $db->query("SELECT id, username, full_name, role, status FROM admins ORDER BY created_at DESC LIMIT 5");
        if ($admins_list) {
            echo "<p class='success'>✅ جلب قائمة المديرين: نجح</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th><th>الحالة</th></tr>";
            
            while ($admin = $admins_list->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$admin['id']}</td>";
                echo "<td>{$admin['username']}</td>";
                echo "<td>{$admin['full_name']}</td>";
                echo "<td>{$admin['role']}</td>";
                echo "<td>{$admin['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ جلب قائمة المديرين: فشل - " . $db->error . "</p>";
        }
        
    } else {
        echo "<p class='error'>❌ جدول admins: غير موجود أو لا يمكن الوصول إليه</p>";
        echo "<p class='error'>خطأ: " . $db->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في اختبار إدارة المديرين: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. اختبار الدوال الأساسية</h2>";

$functions_to_test = [
    'getUnifiedDB' => 'دالة الاتصال بقاعدة البيانات',
    'isAdminLoggedIn' => 'دالة فحص تسجيل دخول المدير',
    'hasAdminPermission' => 'دالة فحص صلاحيات المدير',
    'getCurrentAdmin' => 'دالة الحصول على المدير الحالي',
    'getSystemSettings' => 'دالة جلب إعدادات النظام',
    'getSystemSetting' => 'دالة الحصول على إعداد محدد'
];

foreach ($functions_to_test as $func => $desc) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ $desc: موجودة</p>";
        
        // اختبار بعض الدوال
        try {
            if ($func == 'isAdminLoggedIn') {
                $result = isAdminLoggedIn();
                $status = $result ? 'مسجل دخول' : 'غير مسجل دخول';
                echo "<p class='success'>   └─ النتيجة: $status</p>";
            } elseif ($func == 'getSystemSetting') {
                $result = getSystemSetting('company_name', 'افتراضي');
                echo "<p class='success'>   └─ اختبار: $result</p>";
            }
        } catch (Exception $e) {
            echo "<p class='warning'>   └─ خطأ في الاختبار: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='error'>❌ $desc: غير موجودة</p>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. اختبار الصفحات</h2>";

echo "<h3>اختبار الروابط:</h3>";
echo "<ul>";
echo "<li><a href='admin_system.php' target='_blank'>صفحة إعدادات النظام</a></li>";
echo "<li><a href='admin_manage_admins.php' target='_blank'>صفحة إدارة المديرين</a></li>";
echo "<li><a href='admin_dashboard.php' target='_blank'>لوحة تحكم المدير</a></li>";
echo "<li><a href='admin_login.php' target='_blank'>تسجيل دخول المدير</a></li>";
echo "</ul>";

echo "<h3>أدوات أخرى:</h3>";
echo "<ul>";
echo "<li><a href='final_system_status.php' target='_blank'>تقرير حالة النظام النهائي</a></li>";
echo "<li><a href='improved_system_check.php' target='_blank'>الفحص المحسن</a></li>";
echo "<li><a href='test_functions.php' target='_blank'>اختبار الدوال</a></li>";
echo "</ul>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>6. ملخص الإصلاحات</h2>";

echo "<h3 class='success'>✅ المشاكل التي تم إصلاحها:</h3>";
echo "<ul>";
echo "<li><strong>admin_system.php السطر 36:</strong> استبدال \$main_db بـ \$db</li>";
echo "<li><strong>admin_manage_admins.php السطر 144:</strong> استبدال \$main_db بـ \$db</li>";
echo "<li><strong>جميع المراجع لـ \$main_db:</strong> تم استبدالها بـ \$db في كلا الملفين</li>";
echo "</ul>";

echo "<h3 class='success'>✅ النتيجة المتوقعة:</h3>";
echo "<p>لا يجب أن تظهر أخطاء 'Call to a member function query() on null' بعد الآن.</p>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الفحص في: " . date('Y-m-d H:i:s') . " | ";
echo "الذاكرة المستخدمة: " . number_format(memory_get_usage(true) / 1024 / 1024, 2) . " MB";
echo "</p>";

?>
