<?php
/**
 * صفحة تجريبية لمعاينة الفاتورة مع معلومات الشركة
 */

require_once __DIR__ . '/config/unified_db_config.php';
require_once __DIR__ . '/includes/invoice_functions.php';

// بيانات تجريبية للفاتورة
$invoice = [
    'invoice_number' => 'INV000001',
    'invoice_date' => date('Y-m-d'),
    'payment_method' => 'نقدي',
    'customer_name' => 'أحمد محمد علي',
    'customer_phone' => '0501234567',
    'customer_address' => 'الرياض، المملكة العربية السعودية',
    'customer_email' => '<EMAIL>',
    'total_amount' => 1150.00,
    'tax_amount' => 150.00,
    'discount_amount' => 0,
    'notes' => 'شكراً لتعاملكم معنا'
];

// عناصر تجريبية
$items = [
    [
        'product_name' => 'لابتوب Dell Inspiron',
        'unit' => 'قطعة',
        'quantity' => 1,
        'price' => 800.00
    ],
    [
        'product_name' => 'ماوس لاسلكي',
        'unit' => 'قطعة',
        'quantity' => 2,
        'price' => 50.00
    ],
    [
        'product_name' => 'كيبورد ميكانيكي',
        'unit' => 'قطعة',
        'quantity' => 1,
        'price' => 100.00
    ]
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة تجريبية - <?php echo htmlspecialchars($invoice['invoice_number']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <?php echo getInvoiceCSS(); ?>
</head>
<body>
    <div class="container my-4">
        <div class="row">
            <div class="col-12">
                <!-- أزرار الإجراءات -->
                <div class="d-print-none mb-3">
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="printInvoice()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                        <button type="button" class="btn btn-success" onclick="downloadInvoicePDF()">
                            <i class="fas fa-download me-1"></i>تحميل PDF
                        </button>
                        <button type="button" class="btn btn-info" onclick="emailInvoice()">
                            <i class="fas fa-envelope me-1"></i>إرسال بالبريد
                        </button>
                        <a href="admin_system.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع للإعدادات
                        </a>
                    </div>
                </div>

                <!-- محتوى الفاتورة -->
                <div class="card">
                    <div class="card-body">
                        <!-- رأس الفاتورة مع معلومات الشركة -->
                        <?php displayInvoiceHeader('sales'); ?>

                        <!-- معلومات الفاتورة -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>معلومات الفاتورة:</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td><strong>رقم الفاتورة:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>التاريخ:</strong></td>
                                        <td><?php echo $invoice['invoice_date']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>طريقة الدفع:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['payment_method']); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات العميل:</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td><strong>الاسم:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الهاتف:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['customer_phone']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>العنوان:</strong></td>
                                        <td><?php echo htmlspecialchars($invoice['customer_address']); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- جدول العناصر -->
                        <div class="table-responsive mb-4">
                            <table class="table table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>الوحدة</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $subtotal = 0;
                                    foreach ($items as $index => $item): 
                                        $item_total = $item['quantity'] * $item['price'];
                                        $subtotal += $item_total;
                                    ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                        <td><?php echo number_format($item['quantity'], 2); ?></td>
                                        <td><?php echo formatCurrency($item['price']); ?></td>
                                        <td><?php echo formatCurrency($item_total); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- الإجماليات -->
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>المجموع الفرعي:</strong></td>
                                        <td class="text-end"><?php echo formatCurrency($subtotal); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الضريبة:</strong></td>
                                        <td class="text-end"><?php echo formatCurrency($invoice['tax_amount']); ?></td>
                                    </tr>
                                    <tr class="table-dark">
                                        <td><strong>الإجمالي النهائي:</strong></td>
                                        <td class="text-end"><strong><?php echo formatCurrency($invoice['total_amount']); ?></strong></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mt-4">
                            <h6>ملاحظات:</h6>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
                        </div>

                        <!-- تذييل الفاتورة -->
                        <?php displayInvoiceFooter(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo getInvoiceJS(); ?>
</body>
</html>
