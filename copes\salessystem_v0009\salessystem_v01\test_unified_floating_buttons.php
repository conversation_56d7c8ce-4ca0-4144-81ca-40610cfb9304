<?php
/**
 * اختبار الأزرار العائمة الموحدة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-rocket me-2"></i>اختبار الأزرار العائمة الموحدة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الاختبار:</h6>
                        <ul class="mb-0">
                            <li>الأزرار العائمة موحدة مع الموجودة في الصفحة الرئيسية</li>
                            <li>تظهر في جميع الصفحات (المبيعات، المشتريات، العملاء، المنتجات)</li>
                            <li>عند النقر عليها، تنتقل للصفحة الرئيسية وتفتح الفاتورة السريعة</li>
                            <li>الأزرار مفتوحة دائماً ولا تحتاج لنقر إضافي</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6><i class="fas fa-file-invoice-dollar me-2"></i>اختبار زر المبيعات</h6>
                                </div>
                                <div class="card-body">
                                    <p>انقر على الزر الأخضر "مبيعات" في الأسفل لاختبار:</p>
                                    <ul>
                                        <li>الانتقال للصفحة الرئيسية</li>
                                        <li>فتح السلايد الجانبية للمبيعات</li>
                                        <li>عرض عنوان "فاتورة مبيعات سريعة"</li>
                                    </ul>
                                    <button type="button" class="btn btn-success" onclick="window.location.href='index.php?open_quick_invoice=sale'">
                                        <i class="fas fa-play me-1"></i>
                                        اختبار زر المبيعات
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6><i class="fas fa-shopping-cart me-2"></i>اختبار زر المشتريات</h6>
                                </div>
                                <div class="card-body">
                                    <p>انقر على الزر الأحمر "مشتريات" في الأسفل لاختبار:</p>
                                    <ul>
                                        <li>الانتقال للصفحة الرئيسية</li>
                                        <li>فتح السلايد الجانبية للمشتريات</li>
                                        <li>عرض عنوان "فاتورة مشتريات سريعة"</li>
                                    </ul>
                                    <button type="button" class="btn btn-danger" onclick="window.location.href='index.php?open_quick_invoice=purchase'">
                                        <i class="fas fa-play me-1"></i>
                                        اختبار زر المشتريات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6><i class="fas fa-link me-2"></i>اختبار في صفحات مختلفة</h6>
                                </div>
                                <div class="card-body">
                                    <p>انتقل للصفحات التالية للتأكد من ظهور الأزرار العائمة:</p>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="index.php" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="fas fa-home me-1"></i>
                                                الصفحة الرئيسية
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="sales.php" class="btn btn-outline-success w-100 mb-2">
                                                <i class="fas fa-file-invoice-dollar me-1"></i>
                                                المبيعات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="purchases.php" class="btn btn-outline-danger w-100 mb-2">
                                                <i class="fas fa-shopping-cart me-1"></i>
                                                المشتريات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="customers.php" class="btn btn-outline-info w-100 mb-2">
                                                <i class="fas fa-users me-1"></i>
                                                العملاء
                                            </a>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="products.php" class="btn btn-outline-secondary w-100 mb-2">
                                                <i class="fas fa-box me-1"></i>
                                                المنتجات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="reports.php" class="btn btn-outline-dark w-100 mb-2">
                                                <i class="fas fa-chart-bar me-1"></i>
                                                التقارير
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="settings.php" class="btn btn-outline-warning w-100 mb-2">
                                                <i class="fas fa-cog me-1"></i>
                                                الإعدادات
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="profile.php" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="fas fa-user me-1"></i>
                                                الملف الشخصي
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6><i class="fas fa-list-check me-2"></i>قائمة فحص الوظائف</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>الأزرار العائمة:</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check1">
                                                <label class="form-check-label" for="check1">
                                                    الأزرار ظاهرة في الزاوية اليمنى السفلى
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check2">
                                                <label class="form-check-label" for="check2">
                                                    زر المبيعات (أخضر) موجود
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check3">
                                                <label class="form-check-label" for="check3">
                                                    زر المشتريات (أحمر) موجود
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check4">
                                                <label class="form-check-label" for="check4">
                                                    الأزرار تحتوي على نص وأيقونة
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>الوظائف:</h6>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check5">
                                                <label class="form-check-label" for="check5">
                                                    النقر ينقل للصفحة الرئيسية
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check6">
                                                <label class="form-check-label" for="check6">
                                                    السلايد الجانبية تفتح تلقائياً
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check7">
                                                <label class="form-check-label" for="check7">
                                                    العنوان يتغير حسب نوع الفاتورة
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="check8">
                                                <label class="form-check-label" for="check8">
                                                    الأزرار تظهر في جميع الصفحات
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>تم التوحيد بنجاح!</h6>
                            <p class="mb-0">
                                الأزرار العائمة الآن موحدة مع الموجودة في الصفحة الرئيسية وتظهر في جميع الصفحات.
                                عند النقر عليها، تنتقل للصفحة الرئيسية وتفتح الفاتورة السريعة المطلوبة.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار عائمة للفواتير السريعة -->
<div class="floating-buttons">
    <button class="floating-btn purchase-btn" onclick="window.location.href='index.php?open_quick_invoice=purchase'" title="فاتورة مشتريات سريعة">
        <i class="fas fa-shopping-cart"></i>
        <span class="btn-label">مشتريات</span>
    </button>
    <button class="floating-btn sale-btn" onclick="window.location.href='index.php?open_quick_invoice=sale'" title="فاتورة مبيعات سريعة">
        <i class="fas fa-file-invoice-dollar"></i>
        <span class="btn-label">مبيعات</span>
    </button>
</div>

<style>
/* الأزرار العائمة */
.floating-buttons {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 9999;
    display: flex;
    flex-direction: column-reverse;
    gap: 15px;
}

.floating-btn {
    width: 120px;
    height: 60px;
    border-radius: 30px;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
    flex-direction: row;
    gap: 8px;
    font-weight: 600;
    padding: 0 15px;
    text-decoration: none;
}

.floating-btn i {
    font-size: 18px;
}

.btn-label {
    font-size: 14px;
    font-weight: 600;
}

.floating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    width: 130px;
    text-decoration: none;
    color: white;
}

.sale-btn {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.sale-btn:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
}

.purchase-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.purchase-btn:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .floating-buttons {
        bottom: 80px;
        right: 20px;
    }

    .floating-btn {
        width: 100px;
        height: 50px;
        font-size: 14px;
    }

    .floating-btn:hover {
        width: 105px;
    }

    .btn-label {
        font-size: 12px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
