<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()

// التحقق من وضع النافذة المنبثقة
$is_modal = isset($_GET['modal']) && $_GET['modal'] == '1';

if (!$is_modal) {
    require_once __DIR__.'/includes/header.php';
}
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات: " . ($db ? $db->connect_error : "اتصال غير موجود");
    header("Location: sales.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

if (!isset($_GET['id'])) {
    header("Location: sales.php");
    exit();
}

$sale_id = intval($_GET['id']);
$sale = null;
$items = null;

try {
    // جلب بيانات الفاتورة مع معلومات الدفع
    $stmt = $db->prepare("SELECT s.*, c.name AS customer_name, c.address AS customer_address,
                         c.phone AS customer_phone, c.tax_number AS customer_tax_number
                         FROM sales s
                         LEFT JOIN customers c ON s.customer_id = c.id
                         WHERE s.id = ?");
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $sale_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $sale = $result->fetch_assoc();
    $stmt->close(); // إغلاق الاستعلام

    if (!$sale) {
        $_SESSION['error'] = "فاتورة المبيعات غير موجودة";
        header("Location: sales.php");
        exit();
    }

    // تنظيف أي نتائج متبقية
    $db = resetDBConnection($db);

    // جلب عناصر الفاتورة
    $query = "SELECT si.*, p.name AS product_name
              FROM sale_items si
              JOIN products p ON si.product_id = p.id
              WHERE si.sale_id = ?";
    $stmt = $db->prepare($query);
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام عناصر الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $sale_id);
    $stmt->execute();
    $items = $stmt->get_result();

} catch (Exception $e) {
    error_log("خطأ في عرض الفاتورة: " . $e->getMessage());
    $_SESSION['error'] = "حدث خطأ أثناء جلب بيانات الفاتورة";
    header("Location: sales.php");
    exit();
}

if (!$is_modal) {
    displayMessages(); // عرض أي رسائل خطأ أو نجاح
}
?>

<?php if (!$is_modal): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>عرض فاتورة مبيعات</h5>
                    <div>
                        <a href="print_invoice.php?id=<?php echo $sale_id; ?>&type=sale" class="btn btn-sm btn-secondary" target="_blank">
                            <i class="fas fa-print"></i> طباعة
                        </a>
                        <a href="sales.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left"></i> رجوع
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
<?php else: ?>
<!-- محتوى النافذة المنبثقة -->
<div class="modal-content-wrapper">
<?php endif; ?>
                <div class="row mb-2">
                    <div class="col-md-6">
                        <h6 class="mb-1">معلومات العميل:</h6>
                        <small>
                            <strong>الاسم:</strong> <?php echo htmlspecialchars($sale['customer_name'] ?? 'N/A'); ?><br>
                            <strong>الهاتف:</strong> <?php echo htmlspecialchars($sale['customer_phone'] ?? 'N/A'); ?>
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <h6 class="mb-1">معلومات الفاتورة:</h6>
                        <small>
                            <strong>رقم الفاتورة:</strong> <?php echo $sale['invoice_number']; ?><br>
                            <strong>التاريخ:</strong> <?php echo $sale['date']; ?><br>
                            <?php
                            // ترجمة حالة الدفع
                            $payment_statuses = [
                                'paid' => ['text' => 'مدفوع بالكامل', 'class' => 'bg-success'],
                                'partial' => ['text' => 'مدفوع جزئياً', 'class' => 'bg-warning'],
                                'unpaid' => ['text' => 'غير مدفوع', 'class' => 'bg-danger']
                            ];
                            $status = $payment_statuses[$sale['payment_status'] ?? 'unpaid'] ?? ['text' => 'غير محدد', 'class' => 'bg-secondary'];
                            ?>
                            <strong>حالة الدفع:</strong> <span class="badge <?php echo $status['class']; ?>"><?php echo $status['text']; ?></span>
                        </small>
                    </div>
                </div>

                <!-- قسم معلومات الدفع -->
                <div class="row mb-2">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-header py-1">
                                <small class="mb-0 fw-bold">
                                    <i class="fas fa-credit-card me-1"></i>
                                    معلومات الدفع
                                </small>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <small><strong>طريقة الدفع:</strong><br>
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة ائتمان',
                                            'bank_transfer' => 'تحويل بنكي',
                                            'check' => 'شيك',
                                            'installment' => 'تقسيط',
                                            'other' => 'أخرى'
                                        ];
                                        echo $payment_methods[$sale['payment_method'] ?? 'cash'] ?? 'نقدي';
                                        ?></small>
                                    </div>
                                    <div class="col-md-6">
                                        <small><strong>المبلغ المدفوع:</strong><br>
                                        <span class="text-success fw-bold"><?php echo number_format($sale['paid_amount'] ?? 0, 2); ?> ر.س</span></small>
                                    </div>
                                </div>
                                <?php if (!empty($sale['payment_reference'])): ?>
                                <div class="row mt-1">
                                    <div class="col-md-12">
                                        <small><strong>مرجع الدفع:</strong> <?php echo htmlspecialchars($sale['payment_reference']); ?></small>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 5%;">#</th>
                                <th style="width: 35%;">المنتج</th>
                                <th style="width: 10%;">كمية</th>
                                <th style="width: 15%;">سعر</th>
                                <th style="width: 10%;">ضريبة%</th>
                                <th style="width: 15%;">ق.ضريبة</th>
                                <th style="width: 15%;">مجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                if ($items && $items->num_rows > 0) {
                                    $counter = 1;
                                    while ($item = $items->fetch_assoc()):
                                        $item_subtotal = $item['quantity'] * $item['unit_price'];
                                    ?>
                                    <tr>
                                        <td><?php echo $counter++; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo number_format($item['unit_price'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['tax_rate'], 2); ?>%</td>
                                        <td><?php echo number_format($item['tax_amount'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['total_price'], 2); ?> ر.س</td>
                                    </tr>
                                    <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="7" class="text-center">لا توجد عناصر في هذه الفاتورة</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("خطأ في عرض عناصر الفاتورة: " . $e->getMessage());
                                echo '<tr><td colspan="7" class="text-center">حدث خطأ أثناء جلب عناصر الفاتورة</td></tr>';
                            }
                            ?>
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="5"></th>
                                <th><small>فرعي:</small></th>
                                <td><small><?php echo number_format($sale['subtotal'], 2); ?> ر.س</small></td>
                            </tr>
                            <tr>
                                <th colspan="5"></th>
                                <th><small>ضريبة:</small></th>
                                <td><small><?php echo number_format($sale['tax_amount'], 2); ?> ر.س</small></td>
                            </tr>
                            <tr class="table-primary">
                                <th colspan="5"></th>
                                <th><strong>إجمالي:</strong></th>
                                <td class="fw-bold"><?php echo number_format($sale['total_amount'], 2); ?> ر.س</td>
                            </tr>
                            <?php if (($sale['remaining_amount'] ?? 0) > 0): ?>
                            <tr class="table-danger">
                                <th colspan="5"></th>
                                <th><small>متبقي:</small></th>
                                <td class="text-danger fw-bold">
                                    <small><?php echo number_format($sale['remaining_amount'], 2); ?> ر.س</small>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tfoot>
                    </table>
                </div>

                <?php if (!empty($sale['notes'])): ?>
                <div class="mt-3">
                    <h6>ملاحظات:</h6>
                    <p><?php echo htmlspecialchars($sale['notes']); ?></p>
                </div>
                <?php endif; ?>

<?php if (!$is_modal): ?>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
</div>

<!-- CSS مخصص للنوافذ المنبثقة -->
<style>
.modal-content-wrapper {
    font-size: 13px;
}

.modal-content-wrapper .table-sm th,
.modal-content-wrapper .table-sm td {
    padding: 4px 6px;
    font-size: 11px;
}

.modal-content-wrapper .card-header {
    padding: 6px 10px;
}

.modal-content-wrapper .card-body {
    padding: 8px 10px;
}

.modal-content-wrapper h6 {
    font-size: 14px;
    margin-bottom: 4px;
}

.modal-content-wrapper small {
    font-size: 11px;
}

.modal-content-wrapper .badge {
    font-size: 10px;
}
</style>
<?php endif; ?>

<?php
if (!$is_modal) {
    require_once 'includes/footer.php';
}
// إغلاق اتصال قاعدة البيانات
if (isset($db) && $db) {
    $db->close();
}
?>