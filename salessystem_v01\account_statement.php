<?php
/**
 * كشف حساب العميل/المورد مع معلومات الشركة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/invoice_functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// التحقق من المعاملات
$customer_type = $_GET['type'] ?? 'customer'; // customer أو supplier
$customer_id = intval($_GET['id'] ?? 0);
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // أول الشهر
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // اليوم

if (!$customer_id) {
    showErrorMessage('يجب تحديد العميل أو المورد');
    exit;
}

// جلب بيانات العميل/المورد
$db = getCurrentUserDB();
// في النظام الموحد، الموردين والعملاء في جدول واحد مع customer_type
$stmt = $db->prepare("SELECT * FROM customers WHERE id = ? AND customer_type = ?");
$stmt->bind_param("is", $customer_id, $customer_type);
$stmt->execute();
$customer = $stmt->get_result()->fetch_assoc();
$stmt->close();

if (!$customer) {
    showErrorMessage('العميل أو المورد غير موجود');
    exit;
}

// جلب معلومات الشركة
$company_name = getInvoiceSetting('company_name', 'نظام المبيعات');
$company_address = getInvoiceSetting('company_address');
$company_phone = getInvoiceSetting('company_phone');
$company_email = getInvoiceSetting('company_email');
$company_logo = getInvoiceSetting('company_logo');
$currency_symbol = getInvoiceSetting('currency_symbol', 'ريال');
$decimal_places = intval(getInvoiceSetting('decimal_places', '2'));

// جلب المعاملات
$transactions = [];
$total_debit = 0;
$total_credit = 0;

if ($customer_type === 'customer') {
    // جلب فواتير المبيعات
    $sales_stmt = $db->prepare("
        SELECT 'sale' as type, invoice_number, date as transaction_date, 
               total_amount as amount, paid_amount, remaining_amount, notes
        FROM sales 
        WHERE customer_id = ? AND date BETWEEN ? AND ?
        ORDER BY date ASC
    ");
    $sales_stmt->bind_param("iss", $customer_id, $date_from, $date_to);
    $sales_stmt->execute();
    $sales_result = $sales_stmt->get_result();
    
    while ($row = $sales_result->fetch_assoc()) {
        $row['debit'] = $row['amount']; // مدين
        $row['credit'] = $row['paid_amount']; // دائن
        $transactions[] = $row;
        $total_debit += $row['debit'];
        $total_credit += $row['credit'];
    }
    $sales_stmt->close();
} else {
    // جلب فواتير المشتريات
    $purchases_stmt = $db->prepare("
        SELECT 'purchase' as type, invoice_number, date as transaction_date,
               total_amount as amount, paid_amount, remaining_amount, notes
        FROM purchases 
        WHERE supplier_id = ? AND date BETWEEN ? AND ?
        ORDER BY date ASC
    ");
    $purchases_stmt->bind_param("iss", $customer_id, $date_from, $date_to);
    $purchases_stmt->execute();
    $purchases_result = $purchases_stmt->get_result();
    
    while ($row = $purchases_result->fetch_assoc()) {
        $row['debit'] = $row['paid_amount']; // مدين
        $row['credit'] = $row['amount']; // دائن
        $transactions[] = $row;
        $total_debit += $row['debit'];
        $total_credit += $row['credit'];
    }
    $purchases_stmt->close();
}

$balance = $total_debit - $total_credit;
$customer_title = $customer_type === 'customer' ? 'العميل' : 'المورد';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-alt me-2"></i>كشف حساب <?php echo $customer_title; ?></h2>
                <div>
                    <button onclick="printStatement()" class="btn btn-primary">
                        <i class="fas fa-print me-1"></i>طباعة
                    </button>
                    <a href="<?php echo $customer_type === 'customer' ? 'customers.php' : 'suppliers.php'; ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i>رجوع
                    </a>
                </div>
            </div>

            <!-- نموذج تصفية التواريخ -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="type" value="<?php echo $customer_type; ?>">
                        <input type="hidden" name="id" value="<?php echo $customer_id; ?>">
                        
                        <div class="col-md-4">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" class="form-control" value="<?php echo $date_from; ?>">
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" class="form-control" value="<?php echo $date_to; ?>">
                        </div>
                        
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-info d-block">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- كشف الحساب -->
            <div id="statement-content" class="card">
                <div class="card-body">
                    <!-- رأس كشف الحساب -->
                    <div class="statement-header text-center mb-4 pb-3 border-bottom">
                        <?php if ($company_logo && file_exists(__DIR__ . '/uploads/' . $company_logo)): ?>
                            <div class="mb-3">
                                <img src="uploads/<?php echo htmlspecialchars($company_logo); ?>" alt="شعار الشركة" style="max-height: 60px;">
                            </div>
                        <?php endif; ?>
                        
                        <h3 class="mb-2"><?php echo htmlspecialchars($company_name); ?></h3>
                        
                        <?php if ($company_address || $company_phone || $company_email): ?>
                            <div class="company-details text-muted small">
                                <?php if ($company_address): ?>
                                    <div><?php echo htmlspecialchars($company_address); ?></div>
                                <?php endif; ?>
                                <div>
                                    <?php if ($company_phone): ?>
                                        <span>📞 <?php echo htmlspecialchars($company_phone); ?></span>
                                    <?php endif; ?>
                                    <?php if ($company_email): ?>
                                        <span class="ms-3">📧 <?php echo htmlspecialchars($company_email); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <h4 class="mt-3 text-primary">كشف حساب <?php echo $customer_title; ?></h4>
                        <p class="text-muted">من <?php echo date('d/m/Y', strtotime($date_from)); ?> إلى <?php echo date('d/m/Y', strtotime($date_to)); ?></p>
                    </div>

                    <!-- معلومات العميل/المورد -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">معلومات <?php echo $customer_title; ?></h6>
                                    <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                                    <?php if (!empty($customer['phone'])): ?>
                                        <p class="mb-1"><strong>الهاتف:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($customer['email'])): ?>
                                        <p class="mb-1"><strong>البريد:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($customer['address'])): ?>
                                        <p class="mb-0"><strong>العنوان:</strong> <?php echo htmlspecialchars($customer['address']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">ملخص الحساب</h6>
                                    <p class="mb-1"><strong>إجمالي المدين:</strong> <span class="text-success"><?php echo formatCurrency($total_debit); ?></span></p>
                                    <p class="mb-1"><strong>إجمالي الدائن:</strong> <span class="text-danger"><?php echo formatCurrency($total_credit); ?></span></p>
                                    <p class="mb-0"><strong>الرصيد:</strong> 
                                        <span class="<?php echo $balance >= 0 ? 'text-success' : 'text-danger'; ?> fw-bold">
                                            <?php echo formatCurrency(abs($balance)); ?>
                                            <?php echo $balance >= 0 ? '(لصالح الشركة)' : '(لصالح ' . $customer_title . ')'; ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المعاملات -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 80px;">التاريخ</th>
                                    <th style="width: 120px;">رقم الفاتورة</th>
                                    <th style="width: 80px;">النوع</th>
                                    <th>البيان</th>
                                    <th style="width: 100px;">مدين</th>
                                    <th style="width: 100px;">دائن</th>
                                    <th style="width: 100px;">الرصيد</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($transactions)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle me-2"></i>لا توجد معاملات في الفترة المحددة
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php 
                                    $running_balance = 0;
                                    foreach ($transactions as $transaction): 
                                        $running_balance += ($transaction['debit'] - $transaction['credit']);
                                    ?>
                                        <tr>
                                            <td><?php echo date('d/m/Y', strtotime($transaction['transaction_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($transaction['invoice_number']); ?></td>
                                            <td>
                                                <?php if ($transaction['type'] === 'sale'): ?>
                                                    <span class="badge bg-success">مبيعات</span>
                                                <?php else: ?>
                                                    <span class="badge bg-info">مشتريات</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($transaction['notes'] ?? 'فاتورة ' . ($transaction['type'] === 'sale' ? 'مبيعات' : 'مشتريات')); ?></td>
                                            <td class="text-end">
                                                <?php echo $transaction['debit'] > 0 ? formatCurrency($transaction['debit']) : '-'; ?>
                                            </td>
                                            <td class="text-end">
                                                <?php echo $transaction['credit'] > 0 ? formatCurrency($transaction['credit']) : '-'; ?>
                                            </td>
                                            <td class="text-end <?php echo $running_balance >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo formatCurrency(abs($running_balance)); ?>
                                                <?php echo $running_balance >= 0 ? '+' : '-'; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <?php if (!empty($transactions)): ?>
                                <tfoot class="table-secondary">
                                    <tr class="fw-bold">
                                        <td colspan="4" class="text-end">الإجمالي:</td>
                                        <td class="text-end"><?php echo formatCurrency($total_debit); ?></td>
                                        <td class="text-end"><?php echo formatCurrency($total_credit); ?></td>
                                        <td class="text-end <?php echo $balance >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo formatCurrency(abs($balance)); ?>
                                            <?php echo $balance >= 0 ? '+' : '-'; ?>
                                        </td>
                                    </tr>
                                </tfoot>
                            <?php endif; ?>
                        </table>
                    </div>

                    <!-- تذييل كشف الحساب -->
                    <div class="statement-footer mt-4 pt-3 border-top text-center text-muted small">
                        <p>تم إنشاء هذا الكشف بواسطة <?php echo htmlspecialchars($company_name); ?></p>
                        <p>تاريخ الطباعة: <?php echo date('d/m/Y H:i:s'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .container-fluid {
        padding: 0 !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .btn, .card:first-child {
        display: none !important;
    }
    
    .statement-header {
        border-bottom: 2px solid #000 !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
        padding: 5px !important;
    }
    
    .table thead th {
        background-color: #f0f0f0 !important;
        color: #000 !important;
    }
}
</style>

<script>
function printStatement() {
    window.print();
}
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
