<?php
/**
 * إضافة المدير التجريبي إلى قاعدة البيانات
 */

require_once __DIR__ . '/config/init.php';

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    echo "<h2>إضافة المدير التجريبي</h2>";

    // التحقق من وجود جدول المديرين
    $check_table = $db->query("SHOW TABLES LIKE 'admins'");
    if ($check_table->num_rows == 0) {
        echo "<p style='color: orange;'>جدول المديرين غير موجود. سيتم إنشاؤه...</p>";
        
        // إنشاء جدول المديرين
        $create_admins_table = "
            CREATE TABLE admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                status ENUM('active', 'inactive') DEFAULT 'active',
                permissions JSON,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if ($db->query($create_admins_table)) {
            echo "<p style='color: green;'>✅ تم إنشاء جدول المديرين بنجاح</p>";
        } else {
            throw new Exception("فشل في إنشاء جدول المديرين: " . $db->error);
        }
    } else {
        echo "<p style='color: blue;'>✅ جدول المديرين موجود</p>";
    }

    // التحقق من وجود المدير التجريبي
    $check_admin = $db->prepare("SELECT id FROM admins WHERE username = ?");
    $admin_username = 'admin';
    $check_admin->bind_param("s", $admin_username);
    $check_admin->execute();
    $result = $check_admin->get_result();

    if ($result->num_rows > 0) {
        echo "<p style='color: orange;'>⚠️ المدير التجريبي موجود بالفعل</p>";
        
        // عرض بيانات المدير الموجود
        $admin_data = $result->fetch_assoc();
        echo "<h3>بيانات المدير الحالي:</h3>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $admin_data['id'] . "</li>";
        echo "<li><strong>اسم المستخدم:</strong> admin</li>";
        echo "<li><strong>كلمة المرور:</strong> admin123 (مشفرة)</li>";
        echo "</ul>";
        
        // تحديث كلمة المرور إذا لزم الأمر
        $new_password = password_hash('admin123', PASSWORD_DEFAULT);
        $update_stmt = $db->prepare("UPDATE admins SET password = ?, full_name = ?, email = ? WHERE username = ?");
        $full_name = 'مدير النظام الرئيسي';
        $email = '<EMAIL>';
        $update_stmt->bind_param("ssss", $new_password, $full_name, $email, $admin_username);
        
        if ($update_stmt->execute()) {
            echo "<p style='color: green;'>✅ تم تحديث بيانات المدير</p>";
        }
        $update_stmt->close();
        
    } else {
        echo "<p style='color: blue;'>إضافة المدير التجريبي الجديد...</p>";
        
        // إنشاء المدير التجريبي
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $admin_full_name = 'مدير النظام الرئيسي';
        $admin_email = '<EMAIL>';
        $admin_phone = '0500000000';
        
        // صلاحيات المدير الكاملة
        $admin_permissions = json_encode([
            'manage_users' => true,
            'manage_admins' => true,
            'view_all_data' => true,
            'manage_system' => true,
            'view_reports' => true,
            'manage_settings' => true,
            'view_logs' => true,
            'export_data' => true
        ]);
        
        $insert_admin = $db->prepare("INSERT INTO admins (username, password, full_name, email, phone, status, permissions) VALUES (?, ?, ?, ?, ?, 'active', ?)");
        $insert_admin->bind_param("ssssss", $admin_username, $admin_password, $admin_full_name, $admin_email, $admin_phone, $admin_permissions);
        
        if ($insert_admin->execute()) {
            $admin_id = $db->insert_id;
            echo "<p style='color: green;'>✅ تم إضافة المدير التجريبي بنجاح!</p>";
            
            echo "<h3>بيانات المدير الجديد:</h3>";
            echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; border-radius: 5px;'>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . $admin_id . "</li>";
            echo "<li><strong>اسم المستخدم:</strong> admin</li>";
            echo "<li><strong>كلمة المرور:</strong> admin123</li>";
            echo "<li><strong>الاسم الكامل:</strong> " . $admin_full_name . "</li>";
            echo "<li><strong>البريد الإلكتروني:</strong> " . $admin_email . "</li>";
            echo "<li><strong>رقم الهاتف:</strong> " . $admin_phone . "</li>";
            echo "<li><strong>الحالة:</strong> نشط</li>";
            echo "<li><strong>الصلاحيات:</strong> جميع الصلاحيات</li>";
            echo "</ul>";
            echo "</div>";
            
            // تسجيل العملية
            logActivity('admin_created', 'admins', $admin_id, null, ['username' => $admin_username], 'إنشاء مدير تجريبي جديد');
            
        } else {
            throw new Exception("فشل في إضافة المدير: " . $insert_admin->error);
        }
        $insert_admin->close();
    }
    $check_admin->close();

    // إضافة مستخدم تجريبي أيضاً
    echo "<hr><h2>إضافة مستخدم تجريبي</h2>";

    // التحقق من وجود جدول المستخدمين وإنشاؤه إذا لم يكن موجوداً
    $check_users_table = $db->query("SHOW TABLES LIKE 'users'");
    if ($check_users_table->num_rows == 0) {
        echo "<p style='color: blue;'>إنشاء جدول المستخدمين...</p>";
        $create_users_table = "
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                status ENUM('active', 'inactive') DEFAULT 'active',
                preferred_language VARCHAR(5) DEFAULT 'ar',
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        if ($db->query($create_users_table)) {
            echo "<p style='color: green;'>✅ تم إنشاء جدول المستخدمين</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء جدول المستخدمين: " . $db->error . "</p>";
        }
    }

    $check_user = $db->prepare("SELECT id FROM users WHERE username = ?");
    $user_username = 'user';
    $check_user->bind_param("s", $user_username);
    $check_user->execute();
    $user_result = $check_user->get_result();

    if ($user_result->num_rows > 0) {
        echo "<p style='color: orange;'>⚠️ المستخدم التجريبي موجود بالفعل</p>";
    } else {
        $user_password = password_hash('user123', PASSWORD_DEFAULT);
        $user_full_name = 'مستخدم تجريبي';
        $user_email = '<EMAIL>';
        $user_phone = '0501234567';

        $insert_user = $db->prepare("INSERT INTO users (username, password, full_name, email, phone, status) VALUES (?, ?, ?, ?, ?, 'active')");
        $insert_user->bind_param("sssss", $user_username, $user_password, $user_full_name, $user_email, $user_phone);

        if ($insert_user->execute()) {
            $user_id = $db->insert_id;
            echo "<p style='color: green;'>✅ تم إضافة المستخدم التجريبي بنجاح!</p>";

            echo "<h3>بيانات المستخدم الجديد:</h3>";
            echo "<div style='background: #f0fff0; padding: 15px; border: 1px solid #00cc66; border-radius: 5px;'>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . $user_id . "</li>";
            echo "<li><strong>اسم المستخدم:</strong> user</li>";
            echo "<li><strong>كلمة المرور:</strong> user123</li>";
            echo "<li><strong>الاسم الكامل:</strong> " . $user_full_name . "</li>";
            echo "<li><strong>البريد الإلكتروني:</strong> " . $user_email . "</li>";
            echo "<li><strong>رقم الهاتف:</strong> " . $user_phone . "</li>";
            echo "<li><strong>الحالة:</strong> نشط</li>";
            echo "</ul>";
            echo "</div>";

        } else {
            echo "<p style='color: red;'>❌ فشل في إضافة المستخدم: " . $insert_user->error . "</p>";
        }
        $insert_user->close();
    }
    $check_user->close();

    echo "<hr>";
    echo "<h2>روابط سريعة:</h2>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='admin_login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تسجيل دخول المدير</a> ";
    echo "<a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تسجيل دخول المستخدم</a> ";
    echo "<a href='admin_dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة تحكم المدير</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    ErrorHandler::logError('ERROR', 'Add admin user error: ' . $e->getMessage(), __FILE__, __LINE__);
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f8f9fa;
}

h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

h3 {
    color: #555;
    margin-top: 20px;
}

p {
    font-size: 16px;
    line-height: 1.5;
}

ul {
    list-style-type: none;
    padding: 0;
}

li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

li:last-child {
    border-bottom: none;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}

a {
    display: inline-block;
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>
