<?php
/**
 * صفحة التقارير المالية للمدير
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_reports')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // معاملات التقرير
    $report_type = $_GET['type'] ?? 'financial_overview';
    $date_from = $_GET['date_from'] ?? date('Y-m-01');
    $date_to = $_GET['date_to'] ?? date('Y-m-d');
    $user_id = $_GET['user_id'] ?? '';

    // جلب قائمة المستخدمين النشطين
    $users_query = "SELECT id, full_name, username FROM users WHERE status = 'active' ORDER BY full_name";
    $users_result = $db->query($users_query);
    if (!$users_result) {
        ErrorHandler::logDatabaseError($users_query, $db->error);
        throw new Exception("خطأ في جلب قائمة المستخدمين");
    }

    // جلب البيانات المالية الشاملة
    $financial_data = [];
    $total_sales_amount = 0;
    $total_purchases_amount = 0;
    $total_profit = 0;

    // جلب البيانات من قاعدة البيانات الموحدة
    $sales_query = "SELECT
        u.id as user_id,
        u.full_name as user_name,
        COUNT(s.id) as sales_count,
        COALESCE(SUM(s.total_amount), 0) as sales_total,
        COALESCE(SUM(s.tax_amount), 0) as sales_tax
        FROM users u
        LEFT JOIN sales s ON u.id = s.user_id AND s.date BETWEEN ? AND ?
        WHERE u.status = 'active'
        GROUP BY u.id, u.full_name";

    $sales_stmt = $db->prepare($sales_query);
    if (!$sales_stmt) {
        ErrorHandler::logDatabaseError($sales_query, $db->error);
        throw new Exception("خطأ في إعداد استعلام المبيعات");
    }

    $sales_stmt->bind_param("ss", $date_from, $date_to);
    $sales_stmt->execute();
    $sales_data = $sales_stmt->get_result();

    while ($user_sales = $sales_data->fetch_assoc()) {
        // جلب بيانات المشتريات للمستخدم
        $purchases_query = "SELECT
            COUNT(id) as purchases_count,
            COALESCE(SUM(total_amount), 0) as purchases_total,
            COALESCE(SUM(tax_amount), 0) as purchases_tax
            FROM purchases
            WHERE user_id = ? AND date BETWEEN ? AND ?";

        $purchases_stmt = $db->prepare($purchases_query);
        if ($purchases_stmt) {
            $purchases_stmt->bind_param("iss", $user_sales['user_id'], $date_from, $date_to);
            $purchases_stmt->execute();
            $purchases_data = $purchases_stmt->get_result()->fetch_assoc();
            $purchases_stmt->close();
        } else {
            $purchases_data = ['purchases_count' => 0, 'purchases_total' => 0, 'purchases_tax' => 0];
        }

        $user_sales_total = floatval($user_sales['sales_total']);
        $user_purchases_total = floatval($purchases_data['purchases_total']);
        $user_profit = $user_sales_total - $user_purchases_total;

        $financial_data[] = [
            'user_id' => $user_sales['user_id'],
            'user_name' => $user_sales['user_name'],
            'sales_count' => $user_sales['sales_count'],
            'sales_total' => $user_sales_total,
            'sales_tax' => floatval($user_sales['sales_tax']),
            'purchases_count' => $purchases_data['purchases_count'],
            'purchases_total' => $user_purchases_total,
            'purchases_tax' => floatval($purchases_data['purchases_tax']),
            'profit' => $user_profit
        ];

        $total_sales_amount += $user_sales_total;
        $total_purchases_amount += $user_purchases_total;
        $total_profit += $user_profit;
    }
    $sales_stmt->close();

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Admin financial reports error: ' . $e->getMessage(), __FILE__, __LINE__);
    $_SESSION['error'] = 'حدث خطأ في تحميل التقارير المالية: ' . $e->getMessage();

    // قيم افتراضية في حالة الخطأ
    $financial_data = [];
    $total_sales_amount = 0;
    $total_purchases_amount = 0;
    $total_profit = 0;
    $users_result = null;
}


require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="admin_financial_reports.php">
                            <i class="fas fa-chart-line me-2"></i>التقارير المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_system.php">
                            <i class="fas fa-cogs me-2"></i>إعدادات النظام
                        </a>
                    </li>
                    <?php if (hasAdminPermission('manage_admins')): ?>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_manage_admins.php">
                            <i class="fas fa-user-shield me-2"></i>إدارة المديرين
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-chart-line me-2 text-success"></i>التقارير المالية
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="printReport()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                    </div>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-filter me-2"></i>فلاتر التقرير المالي</h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="type" name="type">
                                <option value="financial_overview" <?php echo $report_type === 'financial_overview' ? 'selected' : ''; ?>>نظرة مالية عامة</option>
                                <option value="sales_details" <?php echo $report_type === 'sales_details' ? 'selected' : ''; ?>>تفاصيل المبيعات</option>
                                <option value="purchases_details" <?php echo $report_type === 'purchases_details' ? 'selected' : ''; ?>>تفاصيل المشتريات</option>
                                <option value="profit_analysis" <?php echo $report_type === 'profit_analysis' ? 'selected' : ''; ?>>تحليل الأرباح</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="user_id" class="form-label">المستخدم</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">جميع المستخدمين</option>
                                <?php if ($users_result && $users_result->num_rows > 0): ?>
                                    <?php $users_result->data_seek(0); // إعادة تعيين المؤشر ?>
                                    <?php while ($user = $users_result->fetch_assoc()): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $user_id == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                    <?php endwhile; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>تطبيق الفلاتر
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- الإحصائيات المالية الرئيسية -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        إجمالي المبيعات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($total_sales_amount, 2); ?> ر.س
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        إجمالي المشتريات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($total_purchases_amount, 2); ?> ر.س
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        صافي الربح
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($total_profit, 2); ?> ر.س
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        هامش الربح
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $profit_margin = $total_sales_amount > 0 ? ($total_profit / $total_sales_amount) * 100 : 0;
                                        echo number_format($profit_margin, 1); 
                                        ?>%
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-percentage fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التفاصيل المالية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل البيانات المالية حسب المستخدم</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead class="table-dark">
                                <tr>
                                    <th>المستخدم</th>
                                    <th>عدد المبيعات</th>
                                    <th>إجمالي المبيعات</th>
                                    <th>ضريبة المبيعات</th>
                                    <th>عدد المشتريات</th>
                                    <th>إجمالي المشتريات</th>
                                    <th>ضريبة المشتريات</th>
                                    <th>صافي الربح</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($financial_data)): ?>
                                    <?php foreach ($financial_data as $data): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($data['user_name']); ?></strong>
                                            <br><small class="text-muted">ID: <?php echo $data['user_id']; ?></small>
                                        </td>
                                        <td><?php echo number_format($data['sales_count']); ?></td>
                                        <td class="text-success">
                                            <strong><?php echo number_format($data['sales_total'], 2); ?> ر.س</strong>
                                        </td>
                                        <td><?php echo number_format($data['sales_tax'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($data['purchases_count']); ?></td>
                                        <td class="text-warning">
                                            <strong><?php echo number_format($data['purchases_total'], 2); ?> ر.س</strong>
                                        </td>
                                        <td><?php echo number_format($data['purchases_tax'], 2); ?> ر.س</td>
                                        <td class="<?php echo $data['profit'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <strong><?php echo number_format($data['profit'], 2); ?> ر.س</strong>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="admin_user_financial_details.php?user_id=<?php echo $data['user_id']; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>"
                                                   class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="admin_invoice_details.php?user_id=<?php echo $data['user_id']; ?>"
                                                   class="btn btn-sm btn-primary" title="عرض الفواتير">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-chart-line fa-3x mb-3"></i>
                                                <h5>لا توجد بيانات مالية</h5>
                                                <p>لم يتم العثور على بيانات مالية للفترة المحددة</p>
                                                <?php if (isset($_SESSION['error'])): ?>
                                                    <div class="alert alert-danger mt-3">
                                                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                            <?php if (!empty($financial_data)): ?>
                            <tfoot class="table-secondary">
                                <tr>
                                    <th>الإجمالي</th>
                                    <th><?php echo array_sum(array_column($financial_data, 'sales_count')); ?></th>
                                    <th class="text-success"><strong><?php echo number_format($total_sales_amount, 2); ?> ر.س</strong></th>
                                    <th><?php echo number_format(array_sum(array_column($financial_data, 'sales_tax')), 2); ?> ر.س</th>
                                    <th><?php echo array_sum(array_column($financial_data, 'purchases_count')); ?></th>
                                    <th class="text-warning"><strong><?php echo number_format($total_purchases_amount, 2); ?> ر.س</strong></th>
                                    <th><?php echo number_format(array_sum(array_column($financial_data, 'purchases_tax')), 2); ?> ر.س</th>
                                    <th class="<?php echo $total_profit >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <strong><?php echo number_format($total_profit, 2); ?> ر.س</strong>
                                    </th>
                                    <th>-</th>
                                </tr>
                            </tfoot>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">توزيع المبيعات والمشتريات</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="salesPurchasesChart" width="100" height="50"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">تحليل الأرباح</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="profitChart" width="100" height="50"></canvas>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>


<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمبيعات والمشتريات
const salesPurchasesCtx = document.getElementById('salesPurchasesChart').getContext('2d');
const salesPurchasesChart = new Chart(salesPurchasesCtx, {
    type: 'doughnut',
    data: {
        labels: [t("sales"), t("purchases")],
        datasets: [{
            data: [
                <?php echo $total_sales_amount; ?>,
                <?php echo $total_purchases_amount; ?>
            ],
            backgroundColor: ['#1cc88a', '#f6c23e']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني للأرباح
const profitCtx = document.getElementById('profitChart').getContext('2d');
const profitChart = new Chart(profitCtx, {
    type: 'bar',
    data: {
        labels: [<?php echo '"' . implode('", "', array_column($financial_data, 'user_name')) . '"'; ?>],
        datasets: [{
            label: 'صافي الربح (ر.س)',
            data: [<?php echo implode(', ', array_column($financial_data, 'profit')); ?>],
            backgroundColor: '#4e73df'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function printReport() {
    window.print();
}

function exportToExcel() {
    alert('سيتم إضافة وظيفة التصدير إلى Excel قريباً');
}
</script>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
