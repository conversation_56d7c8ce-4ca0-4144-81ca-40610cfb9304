<?php
/**
 * ملف إصلاح قاعدة البيانات والجداول
 * يقوم بفحص وإصلاح جميع الجداول المطلوبة
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('manage_system')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

$results = [];
$errors = [];

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // فحص وإصلاح جدول المستخدمين
    $results[] = "بدء فحص جدول المستخدمين...";
    
    // التحقق من وجود عمود status
    $check_status_column = $db->query("SHOW COLUMNS FROM users LIKE 'status'");
    if ($check_status_column->num_rows == 0) {
        // إضافة عمود status
        $add_status = $db->query("ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active' AFTER phone");
        if ($add_status) {
            $results[] = "✅ تم إضافة عمود status إلى جدول users";
            
            // تحديث البيانات الموجودة
            $update_status = $db->query("UPDATE users SET status = CASE WHEN is_active = 1 THEN 'active' ELSE 'inactive' END");
            if ($update_status) {
                $results[] = "✅ تم تحديث بيانات status للمستخدمين الموجودين";
            }
        } else {
            $errors[] = "❌ فشل في إضافة عمود status: " . $db->error;
        }
    } else {
        $results[] = "✅ عمود status موجود في جدول users";
    }

    // فحص وإصلاح جدول المديرين
    $results[] = "بدء فحص جدول المديرين...";
    
    $check_admins_table = $db->query("SHOW TABLES LIKE 'admins'");
    if ($check_admins_table->num_rows == 0) {
        // إنشاء جدول المديرين
        $create_admins = $db->query("
            CREATE TABLE admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                status ENUM('active', 'inactive') DEFAULT 'active',
                permissions JSON,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        if ($create_admins) {
            $results[] = "✅ تم إنشاء جدول admins";
            
            // إضافة مدير افتراضي
            $default_admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $default_permissions = json_encode([
                'manage_users' => true,
                'manage_admins' => true,
                'view_all_data' => true,
                'manage_system' => true,
                'view_reports' => true
            ]);
            
            $insert_admin = $db->prepare("INSERT INTO admins (username, password, full_name, email, permissions) VALUES (?, ?, ?, ?, ?)");
            $insert_admin->bind_param("sssss", 
                $admin_username = 'admin',
                $default_admin_password,
                $admin_name = 'مدير النظام',
                $admin_email = '<EMAIL>',
                $default_permissions
            );
            
            if ($insert_admin->execute()) {
                $results[] = "✅ تم إضافة المدير الافتراضي (admin/admin123)";
            } else {
                $errors[] = "❌ فشل في إضافة المدير الافتراضي: " . $insert_admin->error;
            }
            $insert_admin->close();
        } else {
            $errors[] = "❌ فشل في إنشاء جدول admins: " . $db->error;
        }
    } else {
        $results[] = "✅ جدول admins موجود";
    }

    // فحص وإصلاح جدول سجل الأنشطة
    $results[] = "بدء فحص جدول سجل الأنشطة...";
    
    $check_activity_table = $db->query("SHOW TABLES LIKE 'activity_log'");
    if ($check_activity_table->num_rows == 0) {
        // إنشاء جدول سجل الأنشطة
        $create_activity = $db->query("
            CREATE TABLE activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                user_type ENUM('user', 'admin') DEFAULT 'user',
                action VARCHAR(100) NOT NULL,
                table_name VARCHAR(50),
                record_id INT,
                old_values JSON,
                new_values JSON,
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_user_type (user_type),
                INDEX idx_action (action),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        if ($create_activity) {
            $results[] = "✅ تم إنشاء جدول activity_log";
        } else {
            $errors[] = "❌ فشل في إنشاء جدول activity_log: " . $db->error;
        }
    } else {
        $results[] = "✅ جدول activity_log موجود";
    }

    // فحص وإصلاح جدول المبيعات
    $results[] = "بدء فحص جدول المبيعات...";
    
    $check_sales_table = $db->query("SHOW TABLES LIKE 'sales'");
    if ($check_sales_table->num_rows == 0) {
        // إنشاء جدول المبيعات
        $create_sales = $db->query("
            CREATE TABLE sales (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                customer_id INT,
                invoice_number VARCHAR(50) NOT NULL,
                date DATE NOT NULL,
                subtotal DECIMAL(10,2) DEFAULT 0,
                tax_amount DECIMAL(10,2) DEFAULT 0,
                total_amount DECIMAL(10,2) NOT NULL,
                payment_method VARCHAR(50),
                payment_status ENUM('paid', 'unpaid', 'partial') DEFAULT 'unpaid',
                paid_amount DECIMAL(10,2) DEFAULT 0,
                remaining_amount DECIMAL(10,2) DEFAULT 0,
                payment_date DATE NULL,
                payment_reference VARCHAR(100),
                payment_notes TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_customer_id (customer_id),
                INDEX idx_invoice_number (invoice_number),
                INDEX idx_date (date),
                INDEX idx_payment_status (payment_status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        if ($create_sales) {
            $results[] = "✅ تم إنشاء جدول sales";
        } else {
            $errors[] = "❌ فشل في إنشاء جدول sales: " . $db->error;
        }
    } else {
        $results[] = "✅ جدول sales موجود";
    }

    // فحص وإصلاح جدول المشتريات
    $results[] = "بدء فحص جدول المشتريات...";
    
    $check_purchases_table = $db->query("SHOW TABLES LIKE 'purchases'");
    if ($check_purchases_table->num_rows == 0) {
        // إنشاء جدول المشتريات
        $create_purchases = $db->query("
            CREATE TABLE purchases (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                supplier_id INT,
                invoice_number VARCHAR(50) NOT NULL,
                date DATE NOT NULL,
                subtotal DECIMAL(10,2) DEFAULT 0,
                tax_amount DECIMAL(10,2) DEFAULT 0,
                total_amount DECIMAL(10,2) NOT NULL,
                payment_method VARCHAR(50),
                payment_status ENUM('paid', 'unpaid', 'partial') DEFAULT 'unpaid',
                paid_amount DECIMAL(10,2) DEFAULT 0,
                remaining_amount DECIMAL(10,2) DEFAULT 0,
                payment_date DATE NULL,
                payment_reference VARCHAR(100),
                payment_notes TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_supplier_id (supplier_id),
                INDEX idx_invoice_number (invoice_number),
                INDEX idx_date (date),
                INDEX idx_payment_status (payment_status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        if ($create_purchases) {
            $results[] = "✅ تم إنشاء جدول purchases";
        } else {
            $errors[] = "❌ فشل في إنشاء جدول purchases: " . $db->error;
        }
    } else {
        $results[] = "✅ جدول purchases موجود";
    }

    // فحص وإصلاح جدول العملاء
    $results[] = "بدء فحص جدول العملاء...";
    
    $check_customers_table = $db->query("SHOW TABLES LIKE 'customers'");
    if ($check_customers_table->num_rows == 0) {
        // إنشاء جدول العملاء
        $create_customers = $db->query("
            CREATE TABLE customers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                tax_number VARCHAR(50),
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_name (name),
                INDEX idx_phone (phone)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        if ($create_customers) {
            $results[] = "✅ تم إنشاء جدول customers";
        } else {
            $errors[] = "❌ فشل في إنشاء جدول customers: " . $db->error;
        }
    } else {
        $results[] = "✅ جدول customers موجود";
    }

    // تسجيل العملية
    logActivity('database_fix', 'system', 0, null, null, 'تم تشغيل أداة إصلاح قاعدة البيانات');

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Database fix error: ' . $e->getMessage(), __FILE__, __LINE__);
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar">
            <div class="position-sticky pt-3">
                <div class="sidebar-brand">
                    <h5><i class="fas fa-shield-alt me-2"></i>لوحة المدير</h5>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-section">
                        <div class="nav-section-title">الإدارة الرئيسية</div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_users.php">
                            <i class="fas fa-users"></i>
                            <span>إدارة المستخدمين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_activity.php">
                            <i class="fas fa-history"></i>
                            <span>سجل العمليات</span>
                        </a>
                    </li>
                    
                    <li class="nav-section">
                        <div class="nav-section-title">إدارة النظام</div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_error_logs.php">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>سجل الأخطاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_system.php">
                            <i class="fas fa-cogs"></i>
                            <span>إعدادات النظام</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                <div>
                    <h1 class="h3 mb-1 fw-bold text-primary">
                        <i class="fas fa-database me-2"></i>
                        إصلاح قاعدة البيانات
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="admin_dashboard.php">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active">إصلاح قاعدة البيانات</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="admin_system.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>العودة
                        </a>
                        <button onclick="location.reload()" class="btn btn-primary btn-sm">
                            <i class="fas fa-sync me-1"></i>إعادة تشغيل
                        </button>
                    </div>
                </div>
            </div>

            <!-- نتائج الإصلاح -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-list-check me-2"></i>نتائج فحص وإصلاح قاعدة البيانات
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($results)): ?>
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>العمليات المنجزة:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($results as $result): ?>
                                            <li><?php echo htmlspecialchars($result); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>الأخطاء:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (empty($results) && empty($errors)): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    لم يتم تنفيذ أي عمليات. تحقق من الاتصال بقاعدة البيانات.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
