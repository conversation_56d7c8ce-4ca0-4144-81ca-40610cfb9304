<?php
/**
 * تسجيل خروج المدير
 */

require_once __DIR__ . '/config/init.php';

// تسجيل العملية إذا كان المدير مسجل دخول
if (isAdminLoggedIn()) {
    logActivity('admin_logout', 'admins', $_SESSION['admin_id'], null, null, 'تسجيل خروج المدير');
}

// تنظيف جميع متغيرات الجلسة الخاصة بالمدير
unset($_SESSION['admin_id']);
unset($_SESSION['admin_username']);
unset($_SESSION['admin_full_name']);
unset($_SESSION['admin_email']);
unset($_SESSION['admin_permissions']);
unset($_SESSION['admin_is_super']);

// إعادة التوجيه إلى صفحة تسجيل دخول المدير
header("Location: admin_login.php");
exit();
?>
