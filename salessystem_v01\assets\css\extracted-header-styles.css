        :root {
            /* نظام ألوان متطور */
            --primary-gradient: linear-gradient(135deg,rgb(45, 55, 72));
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            
            
            /* ألوان أساسية */
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #4facfe;
            --warning-color: #43e97b;
            --danger-color: #fa709a;
            --info-color: #a8edea;
            
            /* ألوان النص والخلفية */
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-dark: #1a202c;
            --bg-sidebar: #2d3748;
            
            /* ظلال متطورة */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(182, 182, 182, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            /* حدود دائرية */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            
            /* انتقالات هادئة */
            --transition-fast: all 0.2s ease-out;
            --transition-base: all 0.25s ease-out;
            --transition-slow: all 0.3s ease-out;
        }

        /* الوضع الداكن المحسن */
        [data-theme="dark"] {
            --text-primary: #f7fafc;
            --text-secondary: #e2e8f0;
            --text-muted: #a0aec0;
            --bg-primary: #1a202c;
            --bg-secondary: #2d3748;
            --bg-sidebar: #1a202c;

            /* ألوان متدرجة للوضع الداكن */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

            /* ظلال للوضع الداكن */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }

        /* إعادة تعيين أساسية */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            min-height: 100vh;
            font-size: 14px;
            line-height: 1.6;
            color: var(--text-primary);
            overflow-x: hidden;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* الشريط العلوي المتطور */
        .modern-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: var(--transition-base);
            padding: 1rem 0;
        }

        .modern-navbar .navbar-brand {
            font-weight: 800;
            font-size: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            letter-spacing: -0.025em;
        }

        .modern-navbar .navbar-brand i {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.75rem;
        }

        /* الشريط الجانبي المتطور */
        .modern-sidebar {
            background: var(--bg-sidebar);
            min-height: calc(100vh - 80px);
            width: 280px;
            position: fixed;
            top: 80px;
            right: 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
            box-shadow: var(--shadow-xl);
            border-radius: var(--radius-2xl) 0 0 var(--radius-2xl);
            transition: var(--transition-base);
            z-index: 100;
        }

        .modern-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .modern-sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .modern-sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-full);
        }

        .modern-sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* عناصر الشريط الجانبي */
        .sidebar-section {
            padding: 1.5rem 1rem 0.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .sidebar-section-title {
            font-size: 0.75rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-bottom: 1rem;
            padding: 0 1rem;
        }

        .sidebar-nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: var(--radius-xl);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: var(--transition-base);
            position: relative;
            overflow: hidden;
            
        }

        .sidebar-nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-gradient);
            opacity: 0;
            transition: var(--transition-base);
            border-radius: var(--radius-xl);
        }

        .sidebar-nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-nav-link:hover::before {
            opacity: 0.15;
        }

        .sidebar-nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-nav-link.active::before {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            opacity: 1;
        }

        .sidebar-nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.125rem;
            transition: var(--transition-base);
        }

        .sidebar-nav-link:hover i,
        .sidebar-nav-link.active i {
            color: white;
        }

        /* البطاقات المتطورة */
        .modern-card {
            background: var(--bg-primary);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .modern-card:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .modern-card:hover::before {
            opacity: 1;
        }

        .modern-card-header {
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
            position: relative;
        }

        .modern-card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 2rem;
            right: 2rem;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
        }

        .modern-card-body {
            padding: 2rem;
            position: relative;
        }

        .modern-card-footer {
            padding: 1rem 2rem 2rem;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(102, 126, 234, 0.02) 100%);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* الأزرار المتطورة */
        .modern-btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
            transition: var(--transition-base);
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-fast);
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .modern-btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
            color: white;
        }

        .modern-btn-secondary {
            background: var(--secondary-gradient);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .modern-btn-secondary:hover {
            background: linear-gradient(135deg, #4c51bf 0%, #5a67d8 100%);
            color: white;
        }

        .modern-btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .modern-btn-success:hover {
            background: linear-gradient(135deg, #38a169 0%, #48bb78 100%);
            color: white;
        }

        .modern-btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            box-shadow: none;
        }

        .modern-btn-outline:hover {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
        }

        /* الجداول المتطورة */
        .modern-table {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .modern-table thead th {
            background: var(--primary-gradient);
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            padding: 1rem 1.5rem;
            border: none;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .modern-table tbody td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            vertical-align: middle;
            font-size: 0.875rem;
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        }

        /* النماذج المتطورة */
        .modern-form-control {
            background: var(--bg-primary);
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: var(--radius-lg);
            padding: 0.875rem 1.25rem;
            font-size: 0.875rem;
            transition: var(--transition-base);
            width: 100%;
        }

        .modern-form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modern-form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        /* بطاقات الإحصائيات المتطورة */
        .stats-card {
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 255, 255, 0.9) 100%);
            border-radius: 24px;
            padding: 2.5rem;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 24px 24px 0 0;
        }

        .stats-card::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            opacity: 0;
            transition: all 0.4s ease;
        }

        .stats-card:hover {
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border-color: rgba(102, 126, 234, 0.4);
        }

        .stats-card:hover::after {
            opacity: 1;
        }

        .stats-value {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
            margin-bottom: 0.75rem;
            position: relative;
            z-index: 2;
        }

        .stats-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .stats-change {
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            background: rgba(102, 126, 234, 0.1);
            color: var(--admin-slate);
            display: inline-block;
            position: relative;
            z-index: 2;
        }

        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stats-card:hover .stats-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        /* التأثيرات المتطورة */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* تأثيرات إضافية للبطاقات */
        .modern-card-enhanced {
            position: relative;
            overflow: hidden;
        }

        .modern-card-enhanced::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            opacity: 0;
            transition: all 0.6s ease;
            pointer-events: none;
        }

        .modern-card-enhanced:hover::after {
            opacity: 1;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* تأثير الانعكاس للبطاقات */
        .card-reflection {
            position: relative;
        }

        .card-reflection::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
            border-radius: inherit;
        }

        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hover-lift {
            transition: var(--transition-base);
        }

        .hover-lift:hover {
            box-shadow: var(--shadow-md);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .fade-in {
            /* إزالة التأثيرات المتحركة */
            opacity: 1;
        }

        /* الشارات المتطورة */
        .modern-badge {
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .modern-badge-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .modern-badge-success {
            background: var(--success-gradient);
            color: white;
        }

        .modern-badge-warning {
            background: var(--warning-gradient);
            color: var(--text-primary);
        }

        .modern-badge-danger {
            background: var(--danger-gradient);
            color: white;
        }

        /* الاستجابة للشاشات المختلفة */
        @media (max-width: 992px) {
            .modern-sidebar {
                position: fixed;
                top: 0;
                right: -100%;
                width: 280px;
                height: 100vh;
                z-index: 1000;
                border-radius: 0;
                transition: var(--transition-base);
            }

            .modern-sidebar.show {
                right: 0;
            }

            .main-content,
            .admin-content {
                margin-right: 0;
                padding: 1rem;
            }
        }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--transition-base);
            }

            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .modern-card-body {
                padding: 1.5rem;
            }

            .stats-card {
                padding: 1.5rem;
            }

            .stats-value {
                font-size: 2rem;
            }
        

        @media (max-width: 576px) {
            .modern-navbar .navbar-brand {
                font-size: 1.25rem;
            }

            .modern-card-body {
                padding: 1rem;
            }

            .stats-card {
                padding: 1rem;
            }

            .stats-value {
                font-size: 1.75rem;
            }

            .modern-btn {
                padding: 0.625rem 1.25rem;
                font-size: 0.8rem;
            }
        }

        /* الوضع الداكن المحسن */
        [data-theme="dark"] {
            /* ألوان متدرجة للوضع الداكن */
            --primary-gradient: linear-gradient(135deg,rgba(49, 57, 102, 0.5) 0%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

            /* ظلال للوضع الداكن */
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
        }

        [data-theme="dark"] .modern-navbar {
            background: rgba(26, 32, 44, 0.95);
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-card {
            background: linear-gradient(135deg, rgba(26, 32, 44, 0.9) 0%, rgba(45, 55, 72, 0.8) 100%);
            border-color: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        }

        [data-theme="dark"] .modern-card:hover {
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
            border-color: rgba(102, 126, 234, 0.5);
        }

        [data-theme="dark"] .modern-table {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-table tbody td {
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .modern-table tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .modern-form-control {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
        }

        [data-theme="dark"] .modern-form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        [data-theme="dark"] .stats-card {
            background: linear-gradient(135deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.4);
        }

        [data-theme="dark"] .stats-card:hover {
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5);
            border-color: rgba(102, 126, 234, 0.6);
        }

        [data-theme="dark"] .stats-card::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        [data-theme="dark"] .modern-sidebar {
            background: var(--bg-sidebar);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .dropdown-menu {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .dropdown-item {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        [data-theme="dark"] .modal-content {
            background: var(--bg-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        /* تحسينات الأداء */
        .modern-card,
        .modern-btn,
        .stats-card,
        .sidebar-nav-link {
            will-change: transform;
        }

        /* إزالة الحركة فقط مع الحفاظ على التأثيرات */
        .hover-lift:hover,
        .modern-card:hover,
        .stats-card:hover,
        .modern-btn:hover,
        .sidebar-nav-link:hover,
        .floating-action:hover {
            transform: none !important;
        }

        .fade-in {
            opacity: 1;
        }

        /* تأثيرات إضافية */
        .floating-action {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: var(--radius-full);
            background: var(--primary-gradient);
            color: white;
            border: none;
            box-shadow: var(--shadow-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            transition: var(--transition-base);
            z-index: 1000;
        }

        .floating-action:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
        }

        /* إصلاح تخطيط المحتوى الرئيسي */
        .main-content {
            margin-right: 300px;
            min-height: calc(100vh - 80px);
            padding: 1.5rem;
            transition: var(--transition-base);
        }

        /* تحسين التخطيط العام */
        .admin-layout {
            display: flex;
            min-height: calc(100vh - 80px);
        }

        .admin-content {
            flex: 1;
            margin-right: 300px;
            padding: 1.5rem;
            overflow-x: hidden;
        }

        /* إصلاح مشكلة التمرير */
        body {
            overflow-x: hidden;
        }

        .container-fluid {
            padding: 0;
            margin: 0;
            max-width: 100%;
        }
