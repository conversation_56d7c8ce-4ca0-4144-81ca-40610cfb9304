/**
 * تنسيقات خاصة بصفحة الملف الشخصي
 */

/* الصورة الرمزية */
.avatar-placeholder {
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.avatar-placeholder:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

/* بطاقة الملف الشخصي */
.profile-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.profile-card .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-bottom: none;
    padding: 1.5rem;
}

.profile-card .card-body {
    padding: 2rem;
}

/* جدول المعلومات */
.info-table {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
}

.info-table td {
    padding: 0.75rem 0;
    border: none;
}

.info-table td:first-child {
    color: #6c757d;
    font-weight: 500;
}

/* نموذج التحديث */
.update-form {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* قسم كلمة المرور */
.password-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    border-left: 4px solid #ffc107;
}

.password-section h6 {
    color: #495057;
    margin-bottom: 1rem;
}

/* الأزرار */
.btn-profile {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-profile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* الإحصائيات */
.stats-list {
    background: #e9ecef;
    border-radius: 10px;
    padding: 1rem;
}

.stats-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.stats-list li:last-child {
    border-bottom: none;
}

.stats-list i {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
}

/* الرسائل */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* التحقق من كلمة المرور */
.password-strength {
    margin-top: 0.5rem;
}

.password-strength-bar {
    height: 4px;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.password-strength-weak {
    background-color: #dc3545;
    width: 33%;
}

.password-strength-medium {
    background-color: #ffc107;
    width: 66%;
}

.password-strength-strong {
    background-color: #28a745;
    width: 100%;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .profile-card .card-body {
        padding: 1rem;
    }
    
    .avatar-placeholder {
        width: 80px !important;
        height: 80px !important;
        font-size: 32px !important;
    }
    
    .update-form {
        padding: 1rem;
    }
    
    .password-section {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .btn-profile {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .info-table {
        padding: 0.5rem;
    }
    
    .stats-list {
        padding: 0.5rem;
    }
}

/* تحسينات إضافية */
.card-header h4 {
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.text-muted {
    font-size: 0.9rem;
}

/* تأثيرات التركيز */
.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين الجداول */
.table td {
    vertical-align: middle;
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
}

/* تحسين الأيقونات */
.fas, .far {
    margin-right: 0.5rem;
}

/* تحسين المسافات */
.mb-custom {
    margin-bottom: 2rem;
}

.mt-custom {
    margin-top: 2rem;
}

/* تنسيقات النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: none;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: none;
    border-radius: 0 0 15px 15px;
    padding: 1.5rem;
    background: #f8f9fa;
}

/* تأثيرات النوافذ المنبثقة */
.modal.fade .modal-dialog {
    transform: translate(0, -50px);
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: translate(0, 0);
}

/* أزرار النوافذ المنبثقة */
.modal .btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.modal .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* مؤشر قوة كلمة المرور */
.password-strength {
    margin-top: 0.5rem;
}

.password-strength-bar {
    height: 6px;
    border-radius: 3px;
    transition: all 0.3s ease;
    margin-bottom: 0.25rem;
}

.password-strength-weak {
    background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
    width: 33%;
}

.password-strength-medium {
    background: linear-gradient(90deg, #ffc107 0%, #e0a800 100%);
    width: 66%;
}

.password-strength-strong {
    background: linear-gradient(90deg, #28a745 0%, #1e7e34 100%);
    width: 100%;
}

/* تحسين حقول الإدخال في النوافذ المنبثقة */
.modal .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.modal .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.modal .form-control:disabled {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

/* تحسين التسميات */
.modal .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

/* تحسين الرسائل التحذيرية */
.modal .alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.25rem;
}

.modal .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.modal .alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* تحسين أزرار الإغلاق */
.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* تأثيرات التحقق من صحة النماذج */
.form-control.is-invalid {
    border-color: #dc3545;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.form-control.is-valid {
    border-color: #28a745;
}

/* تحسين النصوص المساعدة */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* تحسين أزرار التحكم الرئيسية */
.btn-profile {
    min-width: 150px;
    margin: 0.25rem;
}

/* تحسين التصميم المتجاوب للنوافذ المنبثقة */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1rem;
    }

    .modal .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-footer {
        padding: 1rem;
        flex-direction: column;
    }
}
