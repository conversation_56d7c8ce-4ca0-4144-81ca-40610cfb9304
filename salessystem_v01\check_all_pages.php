<?php
/**
 * سكريپت فحص شامل لجميع صفحات المدير
 */

// قائمة جميع صفحات المدير
$admin_pages = [
    'admin_dashboard.php' => 'لوحة تحكم المدير',
    'admin_users.php' => 'إدارة المستخدمين',
    'admin_activity.php' => 'سجل العمليات',
    'admin_reports.php' => 'التقارير الشاملة',
    'admin_financial.php' => 'التقارير المالية',
    'admin_error_logs.php' => 'سجل الأخطاء',
    'admin_system.php' => 'إعدادات النظام',
    'admin_manage_admins.php' => 'إدارة المديرين',
    'admin_user_details.php' => 'تفاصيل المستخدم',
    'admin_invoice_details.php' => 'تفاصيل الفاتورة'
];

$status_report = [];
$total_issues = 0;

echo "<h2>🔍 فحص شامل لجميع صفحات المدير</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>📋 الفحوصات المطبقة:</h4>";
echo "<ul>";
echo "<li>✅ وجود الملف</li>";
echo "<li>✅ تنسيق الكود</li>";
echo "<li>✅ هيكل HTML</li>";
echo "<li>✅ CSS Classes</li>";
echo "<li>✅ JavaScript</li>";
echo "<li>✅ الشريط الجانبي</li>";
echo "<li>✅ رأس الصفحة</li>";
echo "</ul>";
echo "</div>";

foreach ($admin_pages as $page => $title) {
    $file_path = __DIR__ . '/' . $page;
    $issues = [];
    $status = 'success';
    
    echo "<h3>🔍 فحص: $title ($page)</h3>";
    
    // 1. فحص وجود الملف
    if (!file_exists($file_path)) {
        $issues[] = "❌ الملف غير موجود";
        $status = 'error';
        echo "<div style='color: red;'>❌ الملف غير موجود</div>";
        $status_report[$page] = ['status' => $status, 'issues' => $issues];
        continue;
    }
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    if ($content === false) {
        $issues[] = "❌ فشل في قراءة الملف";
        $status = 'error';
        echo "<div style='color: red;'>❌ فشل في قراءة الملف</div>";
        $status_report[$page] = ['status' => $status, 'issues' => $issues];
        continue;
    }
    
    // 2. فحص تنسيق الكود
    $line_count = substr_count($content, "\n");
    if ($line_count < 10) {
        $issues[] = "⚠️ الملف مضغوط في سطر واحد";
        $status = 'warning';
        echo "<div style='color: orange;'>⚠️ الملف مضغوط - عدد الأسطر: $line_count</div>";
    } else {
        echo "<div style='color: green;'>✅ تنسيق الكود جيد - عدد الأسطر: $line_count</div>";
    }
    
    // 3. فحص هيكل HTML الأساسي
    $html_checks = [
        'admin_header_new.php' => 'تضمين header الجديد',
        'admin-layout' => 'هيكل التخطيط الحديث',
        'admin-content' => 'منطقة المحتوى الرئيسي',
        'modern-sidebar' => 'الشريط الجانبي الحديث'
    ];
    
    foreach ($html_checks as $check => $description) {
        if (strpos($content, $check) !== false) {
            echo "<div style='color: green;'>✅ $description موجود</div>";
        } else {
            $issues[] = "⚠️ $description مفقود";
            if ($status !== 'error') $status = 'warning';
            echo "<div style='color: orange;'>⚠️ $description مفقود</div>";
        }
    }
    
    // 4. فحص CSS Classes الحديثة
    $css_checks = [
        'modern-card' => 'بطاقات حديثة',
        'modern-btn' => 'أزرار حديثة',
        'modern-table' => 'جداول حديثة',
        'stats-card' => 'بطاقات إحصائيات',
        'gradient-text' => 'نصوص متدرجة'
    ];
    
    $css_found = 0;
    foreach ($css_checks as $class => $description) {
        if (strpos($content, $class) !== false) {
            $css_found++;
            echo "<div style='color: green;'>✅ $description موجودة</div>";
        }
    }
    
    if ($css_found < 3) {
        $issues[] = "⚠️ CSS classes حديثة قليلة ($css_found/5)";
        if ($status !== 'error') $status = 'warning';
        echo "<div style='color: orange;'>⚠️ CSS classes حديثة قليلة ($css_found/5)</div>";
    }
    
    // 5. فحص JavaScript
    if (strpos($content, '<script>') !== false || strpos($content, '.js') !== false) {
        echo "<div style='color: green;'>✅ JavaScript موجود</div>";
    } else {
        echo "<div style='color: blue;'>ℹ️ لا يحتوي على JavaScript</div>";
    }
    
    // 6. فحص الشريط الجانبي
    if (strpos($content, 'sidebar-nav-link') !== false) {
        echo "<div style='color: green;'>✅ روابط الشريط الجانبي موجودة</div>";
        
        // فحص الرابط النشط
        if (strpos($content, 'sidebar-nav-link active') !== false) {
            echo "<div style='color: green;'>✅ الرابط النشط محدد</div>";
        } else {
            $issues[] = "⚠️ الرابط النشط غير محدد";
            if ($status !== 'error') $status = 'warning';
            echo "<div style='color: orange;'>⚠️ الرابط النشط غير محدد</div>";
        }
    } else {
        $issues[] = "❌ الشريط الجانبي مفقود";
        $status = 'error';
        echo "<div style='color: red;'>❌ الشريط الجانبي مفقود</div>";
    }
    
    // 7. فحص رأس الصفحة
    if (strpos($content, 'gradient-text') !== false && strpos($content, '<h1') !== false) {
        echo "<div style='color: green;'>✅ رأس الصفحة الحديث موجود</div>";
    } else {
        $issues[] = "⚠️ رأس الصفحة يحتاج تحديث";
        if ($status !== 'error') $status = 'warning';
        echo "<div style='color: orange;'>⚠️ رأس الصفحة يحتاج تحديث</div>";
    }
    
    // 8. فحص الأخطاء الشائعة
    $common_errors = [
        'class=""' => 'فئات فارغة',
        'style=""' => 'أنماط فارغة',
        'undefined' => 'متغيرات غير معرفة',
        'Notice:' => 'تحذيرات PHP',
        'Warning:' => 'تحذيرات PHP'
    ];
    
    foreach ($common_errors as $error => $description) {
        if (strpos($content, $error) !== false) {
            $issues[] = "⚠️ $description موجودة";
            if ($status !== 'error') $status = 'warning';
            echo "<div style='color: orange;'>⚠️ $description موجودة</div>";
        }
    }
    
    // تحديد الحالة النهائية
    if (empty($issues)) {
        echo "<div style='color: green; font-weight: bold;'>🎉 الصفحة سليمة تماماً!</div>";
    } else {
        $total_issues += count($issues);
        echo "<div style='color: " . ($status === 'error' ? 'red' : 'orange') . "; font-weight: bold;'>";
        echo ($status === 'error' ? '❌' : '⚠️') . " عدد المشاكل: " . count($issues) . "</div>";
    }
    
    $status_report[$page] = ['status' => $status, 'issues' => $issues];
    echo "<hr>";
}

// عرض الملخص الشامل
echo "<h3>📊 ملخص الفحص الشامل</h3>";

$success_count = 0;
$warning_count = 0;
$error_count = 0;

foreach ($status_report as $page => $report) {
    switch ($report['status']) {
        case 'success':
            $success_count++;
            break;
        case 'warning':
            $warning_count++;
            break;
        case 'error':
            $error_count++;
            break;
    }
}

echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";

// بطاقة النجاح
echo "<div style='flex: 1; background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;'>";
echo "<h4 style='color: #155724; margin: 0;'>✅ صفحات سليمة</h4>";
echo "<p style='color: #155724; margin: 5px 0 0 0; font-size: 24px; font-weight: bold;'>$success_count</p>";
echo "</div>";

// بطاقة التحذيرات
echo "<div style='flex: 1; background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
echo "<h4 style='color: #856404; margin: 0;'>⚠️ تحتاج تحسين</h4>";
echo "<p style='color: #856404; margin: 5px 0 0 0; font-size: 24px; font-weight: bold;'>$warning_count</p>";
echo "</div>";

// بطاقة الأخطاء
echo "<div style='flex: 1; background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;'>";
echo "<h4 style='color: #721c24; margin: 0;'>❌ تحتاج إصلاح</h4>";
echo "<p style='color: #721c24; margin: 5px 0 0 0; font-size: 24px; font-weight: bold;'>$error_count</p>";
echo "</div>";

echo "</div>";

// قائمة تفصيلية بالمشاكل
if ($total_issues > 0) {
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🔧 المشاكل التي تحتاج إصلاح:</h4>";
    foreach ($status_report as $page => $report) {
        if (!empty($report['issues'])) {
            echo "<h5>$page:</h5>";
            echo "<ul>";
            foreach ($report['issues'] as $issue) {
                echo "<li>$issue</li>";
            }
            echo "</ul>";
        }
    }
    echo "</div>";
}

// روابط الإصلاح
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>🛠️ أدوات الإصلاح:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
echo "<a href='fix_formatting_issues.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔧 إصلاح التنسيق</a>";
echo "<a href='fix_css_issues.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🎨 إصلاح CSS</a>";
echo "<a href='apply_modern_design.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>✨ تطبيق التصميم الحديث</a>";
echo "</div>";
echo "</div>";

// روابط الاختبار
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار الصفحات:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;'>";
foreach ($admin_pages as $page => $title) {
    $color = 'gray';
    if (isset($status_report[$page])) {
        switch ($status_report[$page]['status']) {
            case 'success': $color = '#4caf50'; break;
            case 'warning': $color = '#ff9800'; break;
            case 'error': $color = '#f44336'; break;
        }
    }
    echo "<a href='$page' style='background: $color; color: white; padding: 8px 12px; text-decoration: none; border-radius: 5px; font-size: 12px;'>$title</a>";
}
echo "</div>";
echo "</div>";
?>
