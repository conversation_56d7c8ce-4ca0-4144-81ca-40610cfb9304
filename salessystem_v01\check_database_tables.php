<?php
/**
 * فحص الجداول في قاعدة البيانات
 */

require_once __DIR__ . '/config/unified_db_config.php';

$db = getUnifiedDB();

if (!$db) {
    die("فشل في الاتصال بقاعدة البيانات");
}

echo "<h2>فحص الجداول في قاعدة البيانات</h2>";

// قائمة الجداول المطلوبة
$required_tables = ['users', 'customers', 'sales', 'purchases', 'products'];

echo "<h3>الجداول الموجودة:</h3>";
$result = $db->query("SHOW TABLES");
$existing_tables = [];

if ($result) {
    while ($row = $result->fetch_array()) {
        $table_name = $row[0];
        $existing_tables[] = $table_name;
        echo "✅ " . $table_name . "<br>";
    }
} else {
    echo "❌ فشل في جلب قائمة الجداول: " . $db->error;
}

echo "<h3>فحص الجداول المطلوبة:</h3>";
foreach ($required_tables as $table) {
    if (in_array($table, $existing_tables)) {
        echo "✅ جدول $table موجود<br>";
        
        // فحص هيكل الجدول
        $structure_result = $db->query("DESCRIBE $table");
        if ($structure_result) {
            echo "<details><summary>هيكل جدول $table</summary>";
            echo "<table border='1' style='margin: 10px;'>";
            echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($col = $structure_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $col['Field'] . "</td>";
                echo "<td>" . $col['Type'] . "</td>";
                echo "<td>" . $col['Null'] . "</td>";
                echo "<td>" . $col['Key'] . "</td>";
                echo "<td>" . $col['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table></details>";
        }
    } else {
        echo "❌ جدول $table غير موجود<br>";
    }
}

// فحص عدد السجلات
echo "<h3>عدد السجلات في كل جدول:</h3>";
foreach ($existing_tables as $table) {
    $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
    if ($count_result) {
        $count = $count_result->fetch_assoc()['count'];
        echo "📊 جدول $table: $count سجل<br>";
    }
}

// فحص الجداول التي تحتوي على user_id
echo "<h3>فحص عمود user_id:</h3>";
$tables_with_user_id = ['sales', 'purchases', 'customers'];
foreach ($tables_with_user_id as $table) {
    if (in_array($table, $existing_tables)) {
        $column_check = $db->query("SHOW COLUMNS FROM $table LIKE 'user_id'");
        if ($column_check && $column_check->num_rows > 0) {
            echo "✅ جدول $table يحتوي على عمود user_id<br>";
        } else {
            echo "❌ جدول $table لا يحتوي على عمود user_id<br>";
        }
    }
}

// فحص customer_type في جدول customers
echo "<h3>فحص عمود customer_type:</h3>";
if (in_array('customers', $existing_tables)) {
    $column_check = $db->query("SHOW COLUMNS FROM customers LIKE 'customer_type'");
    if ($column_check && $column_check->num_rows > 0) {
        echo "✅ جدول customers يحتوي على عمود customer_type<br>";
        
        // فحص القيم الموجودة
        $values_result = $db->query("SELECT customer_type, COUNT(*) as count FROM customers GROUP BY customer_type");
        if ($values_result) {
            echo "<ul>";
            while ($row = $values_result->fetch_assoc()) {
                echo "<li>" . ($row['customer_type'] ?: 'NULL') . ": " . $row['count'] . " سجل</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "❌ جدول customers لا يحتوي على عمود customer_type<br>";
    }
}

$db->close();
?>
