<?php
/**
 * مسح الملفات المخبئة وإعادة تشغيل النظام
 */

echo "<h1>🔄 مسح الملفات المخبئة وإعادة التشغيل</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .important { background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffc107; }
</style>";

echo "<div class='section'>";
echo "<h2>1. مسح ملفات PHP المخبئة</h2>";

// مسح OPcache إذا كان مفعلاً
if (function_exists('opcache_reset')) {
    if (opcache_reset()) {
        echo "<p class='success'>✅ تم مسح OPcache بنجاح</p>";
    } else {
        echo "<p class='warning'>⚠️ فشل في مسح OPcache</p>";
    }
} else {
    echo "<p class='info'>ℹ️ OPcache غير مفعل</p>";
}

// مسح ملفات الجلسة
if (session_status() == PHP_SESSION_ACTIVE) {
    session_write_close();
    echo "<p class='success'>✅ تم إغلاق الجلسة الحالية</p>";
}

// مسح متغيرات البيئة المخبئة
if (function_exists('apc_clear_cache')) {
    apc_clear_cache();
    echo "<p class='success'>✅ تم مسح APC cache</p>";
} else {
    echo "<p class='info'>ℹ️ APC cache غير متاح</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. فحص حالة الملفات</h2>";

$files_to_check = [
    'reports.php',
    'config/init.php',
    'includes/functions.php'
];

foreach ($files_to_check as $file) {
    $file_path = __DIR__ . '/' . $file;
    if (file_exists($file_path)) {
        $size = filesize($file_path);
        $modified = date('Y-m-d H:i:s', filemtime($file_path));
        echo "<p class='info'>📄 $file: " . number_format($size) . " بايت، آخر تعديل: $modified</p>";
    } else {
        echo "<p class='error'>❌ $file: غير موجود</p>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. إعادة تحميل الإعدادات</h2>";

// إعادة تحميل ملف init.php
try {
    ob_start();
    include_once __DIR__ . '/config/init.php';
    ob_end_clean();
    echo "<p class='success'>✅ تم إعادة تحميل init.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في تحميل init.php: " . $e->getMessage() . "</p>";
}

// إعادة تحميل ملف functions.php
try {
    ob_start();
    include_once __DIR__ . '/includes/functions.php';
    ob_end_clean();
    echo "<p class='success'>✅ تم إعادة تحميل functions.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في تحميل functions.php: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. اختبار سريع للنظام</h2>";

// اختبار الاتصال بقاعدة البيانات
try {
    if (function_exists('getUnifiedDB')) {
        $db = getUnifiedDB();
        if ($db && !$db->connect_error) {
            echo "<p class='success'>✅ الاتصال بقاعدة البيانات: يعمل</p>";
        } else {
            echo "<p class='error'>❌ الاتصال بقاعدة البيانات: فشل</p>";
        }
    } else {
        echo "<p class='error'>❌ دالة getUnifiedDB غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في اختبار قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار الجلسة
try {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (session_status() == PHP_SESSION_ACTIVE) {
        echo "<p class='success'>✅ الجلسة: تعمل بشكل صحيح</p>";
    } else {
        echo "<p class='error'>❌ الجلسة: لا تعمل</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في اختبار الجلسة: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='important'>";
echo "<h2>⚠️ خطوات مهمة لإكمال الإصلاح</h2>";

echo "<h3>1. إعادة تشغيل XAMPP (مطلوب):</h3>";
echo "<ol>";
echo "<li>اذهب إلى XAMPP Control Panel</li>";
echo "<li>أوقف Apache</li>";
echo "<li>انتظر 10 ثوان</li>";
echo "<li>شغل Apache مرة أخرى</li>";
echo "</ol>";

echo "<h3>2. مسح ذاكرة التخزين المؤقت للمتصفح:</h3>";
echo "<ol>";
echo "<li>اضغط Ctrl + Shift + Delete</li>";
echo "<li>اختر 'مسح البيانات المخبئة'</li>";
echo "<li>اضغط 'مسح البيانات'</li>";
echo "</ol>";

echo "<h3>3. اختبار النظام:</h3>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement&_cache_bust=" . time() . "' target='_blank'>كشف الحساب (مع منع التخزين المؤقت)</a></li>";
echo "<li><a href='check_syntax_errors.php' target='_blank'>فحص الأخطاء التركيبية</a></li>";
echo "<li><a href='fix_session_issues.php' target='_blank'>فحص مشاكل الجلسات</a></li>";
echo "</ul>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. إنشاء ملف اختبار بسيط</h2>";

$test_file_content = '<?php
// ملف اختبار بسيط لكشف الحساب
require_once __DIR__ . "/config/init.php";

echo "<h1>اختبار كشف الحساب البسيط</h1>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    
    $username = $_SESSION["username"] ?? "testuser";
    $user_id = $_SESSION["user_id"] ?? 1;
    
    $sales_table = getUserTableName("sales", $username);
    $purchases_table = getUserTableName("purchases", $username);
    
    $sales_count = $db->query("SELECT COUNT(*) as count FROM `$sales_table` WHERE user_id = $user_id")->fetch_assoc()["count"];
    $purchases_count = $db->query("SELECT COUNT(*) as count FROM `$purchases_table` WHERE user_id = $user_id")->fetch_assoc()["count"];
    
    echo "<p>المبيعات: $sales_count</p>";
    echo "<p>المشتريات: $purchases_count</p>";
    echo "<p>الإجمالي: " . ($sales_count + $purchases_count) . "</p>";
    
    if ($sales_count == 3 && $purchases_count == 1) {
        echo "<p style=\"color: green; font-weight: bold;\">✅ النتائج صحيحة!</p>";
    } else {
        echo "<p style=\"color: red; font-weight: bold;\">❌ النتائج غير صحيحة</p>";
    }
    
} catch (Exception $e) {
    echo "<p style=\"color: red;\">خطأ: " . $e->getMessage() . "</p>";
}
?>';

$test_file_path = __DIR__ . '/simple_account_test.php';
if (file_put_contents($test_file_path, $test_file_content)) {
    echo "<p class='success'>✅ تم إنشاء ملف الاختبار البسيط: <a href='simple_account_test.php' target='_blank'>simple_account_test.php</a></p>";
} else {
    echo "<p class='error'>❌ فشل في إنشاء ملف الاختبار</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>6. ملخص الحالة</h2>";

echo "<h3 class='success'>✅ تم إنجازه:</h3>";
echo "<ul>";
echo "<li>إصلاح مشاكل الجلسات (session_start)</li>";
echo "<li>إصلاح الأخطاء التركيبية (syntax errors)</li>";
echo "<li>إصلاح متغير \$transaction_type</li>";
echo "<li>مسح الملفات المخبئة</li>";
echo "<li>إنشاء ملف اختبار بسيط</li>";
echo "</ul>";

echo "<h3 class='warning'>⚠️ المطلوب منك:</h3>";
echo "<ol>";
echo "<li><strong>إعادة تشغيل XAMPP</strong> (مهم جداً)</li>";
echo "<li><strong>مسح ذاكرة المتصفح</strong></li>";
echo "<li><strong>اختبار النظام</strong> باستخدام الروابط أعلاه</li>";
echo "</ol>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء العملية في: " . date('Y-m-d H:i:s') . " | ";
echo "معرف العملية: " . uniqid();
echo "</p>";

?>
