<?php
/**
 * ملف ترحيل قاعدة البيانات للنظام الموحد
 * تم تحديثه للعمل مع النظام الموحد الجديد
 */

// دالة لترحيل بيانات المستخدم من النظام القديم إلى النظام الموحد
function migrateUserDataToUnified($user_id, $username) {
    $db = getUnifiedDB();
    if (!$db) {
        return [
            'success' => false,
            'error' => 'فشل في الاتصال بقاعدة البيانات الموحدة'
        ];
    }

    // التحقق من وجود قاعدة بيانات المستخدم القديمة
    $old_db_name = "sales_system_user_" . $user_id;

    $check_old_db = $db->query("SHOW DATABASES LIKE '$old_db_name'");
    if (!$check_old_db || $check_old_db->num_rows == 0) {
        return [
            'success' => false,
            'error' => 'لا توجد بيانات قديمة للترحيل',
            'old_db' => $old_db_name
        ];
    }

    // التحقق من وجود المستخدم في النظام الموحد
    $user_check = $db->prepare("SELECT id FROM users WHERE id = ?");
    $user_check->bind_param("i", $user_id);
    $user_check->execute();
    $user_result = $user_check->get_result();

    if ($user_result->num_rows == 0) {
        return [
            'success' => false,
            'error' => 'المستخدم غير موجود في النظام الموحد'
        ];
    }

    // قائمة الجداول للترحيل إلى النظام الموحد
    $tables_to_migrate = [
        'customers' => 'customers',
        'products' => 'products',
        'sales' => 'sales',
        'purchases' => 'purchases',
        'sale_items' => 'sale_items',
        'purchase_items' => 'purchase_items'
    ];

    $migrated_tables = [];
    $migration_errors = [];

    foreach ($tables_to_migrate as $old_table => $new_table) {
        try {
            // التحقق من وجود الجدول القديم
            $check_table = $db->query("SELECT 1 FROM `$old_db_name`.`$old_table` LIMIT 1");
            if (!$check_table) {
                continue; // الجدول غير موجود، تخطي
            }

            // نسخ البيانات إلى النظام الموحد مع إضافة user_id
            $copy_sql = "INSERT INTO `$new_table` (user_id, " .
                       "SELECT $user_id, * FROM `$old_db_name`.`$old_table`";

            // تنفيذ النسخ
            if ($db->query($copy_sql)) {
                $migrated_tables[] = $old_table;
            } else {
                $migration_errors[] = "فشل في ترحيل جدول $old_table: " . $db->error;
            }

        } catch (Exception $e) {
            $migration_errors[] = "خطأ في ترحيل جدول $old_table: " . $e->getMessage();
        }
    }

    return [
        'success' => count($migrated_tables) > 0,
        'migrated_tables' => $migrated_tables,
        'errors' => $migration_errors,
        'total_tables' => count($tables_to_migrate)
    ];
}

// دالة لترحيل جميع المستخدمين إلى النظام الموحد
function migrateAllUsersToUnified() {
    $db = getUnifiedDB();
    if (!$db) {
        return [
            'success' => false,
            'error' => 'فشل في الاتصال بقاعدة البيانات'
        ];
    }

    $users_result = $db->query("SELECT id, username FROM users ORDER BY id");
    if (!$users_result) {
        return [
            'success' => false,
            'error' => 'فشل في جلب قائمة المستخدمين'
        ];
    }

    $migration_results = [];

    while ($user = $users_result->fetch_assoc()) {
        $user_id = $user['id'];
        $username = $user['username'];

        $result = migrateUserDataToUnified($user_id, $username);
        $migration_results[$username] = $result;
    }

    return [
        'success' => true,
        'results' => $migration_results,
        'total_users' => count($migration_results)
    ];
}

// دالة للتحقق من سلامة البيانات في النظام الموحد
function verifyUnifiedMigration($user_id) {
    $db = getUnifiedDB();
    if (!$db) {
        return [
            'success' => false,
            'error' => 'فشل في الاتصال بقاعدة البيانات'
        ];
    }

    $tables_to_check = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
    $verification_results = [];

    foreach ($tables_to_check as $table) {
        try {
            // فحص وجود الجدول
            $check_table = $db->query("SHOW TABLES LIKE '$table'");
            if (!$check_table || $check_table->num_rows == 0) {
                $verification_results[$table] = [
                    'exists' => false,
                    'count' => 0,
                    'user_data_count' => 0
                ];
                continue;
            }

            // عدد السجلات الإجمالي
            $count_result = $db->query("SELECT COUNT(*) as count FROM `$table`");
            $total_count = $count_result ? $count_result->fetch_assoc()['count'] : 0;

            // عدد سجلات المستخدم المحدد
            $user_count_stmt = $db->prepare("SELECT COUNT(*) as count FROM `$table` WHERE user_id = ?");
            $user_count_stmt->bind_param("i", $user_id);
            $user_count_stmt->execute();
            $user_count_result = $user_count_stmt->get_result();
            $user_count = $user_count_result ? $user_count_result->fetch_assoc()['count'] : 0;

            $verification_results[$table] = [
                'exists' => true,
                'count' => $total_count,
                'user_data_count' => $user_count
            ];

        } catch (Exception $e) {
            $verification_results[$table] = [
                'exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    return [
        'success' => true,
        'results' => $verification_results
    ];
}

// دالة لإنشاء نسخة احتياطية قبل الترحيل
function createBackupBeforeMigration() {
    global $main_db;
    
    $backup_dir = __DIR__ . '/../backups';
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $backup_file = $backup_dir . '/backup_before_migration_' . date('Y-m-d_H-i-s') . '.sql';
    
    // هذه دالة مبسطة - في الواقع تحتاج لاستخدام mysqldump
    $tables_result = $main_db->query("SHOW TABLES");
    if (!$tables_result) {
        return false;
    }
    
    $backup_content = "-- نسخة احتياطية قبل الترحيل\n";
    $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
    
    while ($table_row = $tables_result->fetch_array()) {
        $table_name = $table_row[0];
        
        // هيكل الجدول
        $create_table_result = $main_db->query("SHOW CREATE TABLE `$table_name`");
        if ($create_table_result) {
            $create_table = $create_table_result->fetch_array();
            $backup_content .= "\n-- هيكل الجدول $table_name\n";
            $backup_content .= $create_table[1] . ";\n\n";
        }
        
        // بيانات الجدول (عينة صغيرة فقط)
        $data_result = $main_db->query("SELECT * FROM `$table_name` LIMIT 10");
        if ($data_result && $data_result->num_rows > 0) {
            $backup_content .= "-- عينة من بيانات الجدول $table_name\n";
            while ($row = $data_result->fetch_assoc()) {
                $values = array_map(function($value) {
                    return "'" . addslashes($value) . "'";
                }, array_values($row));
                $backup_content .= "INSERT INTO `$table_name` VALUES (" . implode(', ', $values) . ");\n";
            }
            $backup_content .= "\n";
        }
    }
    
    return file_put_contents($backup_file, $backup_content) !== false ? $backup_file : false;
}

// دالة لتنظيف قواعد البيانات القديمة (بعد التأكد من نجاح الترحيل)
function cleanupOldDatabases() {
    global $main_db;
    
    // البحث عن قواعد البيانات القديمة
    $databases_result = $main_db->query("SHOW DATABASES LIKE 'sales_system_user_%'");
    if (!$databases_result) {
        return false;
    }
    
    $old_databases = [];
    while ($db_row = $databases_result->fetch_array()) {
        $old_databases[] = $db_row[0];
    }
    
    return $old_databases;
}

// دالة لإنشاء تقرير الترحيل
function generateMigrationReport($migration_results) {
    $report = "تقرير ترحيل البيانات\n";
    $report .= "=====================\n";
    $report .= "تاريخ الترحيل: " . date('Y-m-d H:i:s') . "\n\n";
    
    $total_users = count($migration_results);
    $successful_migrations = 0;
    
    foreach ($migration_results as $username => $result) {
        $report .= "المستخدم: $username\n";
        
        if ($result && $result['success']) {
            $successful_migrations++;
            $report .= "  الحالة: نجح الترحيل\n";
            $report .= "  الجداول المرحلة: " . implode(', ', $result['migrated_tables']) . "\n";
        } else {
            $report .= "  الحالة: فشل الترحيل\n";
            if ($result && isset($result['errors'])) {
                $report .= "  الأخطاء: " . implode(', ', $result['errors']) . "\n";
            }
        }
        $report .= "\n";
    }
    
    $report .= "الملخص:\n";
    $report .= "========\n";
    $report .= "إجمالي المستخدمين: $total_users\n";
    $report .= "الترحيل الناجح: $successful_migrations\n";
    $report .= "الترحيل الفاشل: " . ($total_users - $successful_migrations) . "\n";
    
    return $report;
}

// دالة لاختبار النظام الموحد
function testUnifiedSystem() {
    $tests = [];

    // اختبار الاتصال بقاعدة البيانات الموحدة
    $db = getUnifiedDB();
    $tests['unified_db_connection'] = $db && !$db->connect_error;

    if ($db) {
        // اختبار الجداول الأساسية
        $required_tables = ['users', 'admins', 'activity_log', 'customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
        $tests['tables_exist'] = [];

        foreach ($required_tables as $table) {
            $check_table = $db->query("SHOW TABLES LIKE '$table'");
            $tests['tables_exist'][$table] = $check_table && $check_table->num_rows > 0;
        }

        // اختبار استعلام بسيط
        try {
            $result = $db->query("SELECT 1 as test");
            $tests['simple_query'] = $result && $result->num_rows > 0;
        } catch (Exception $e) {
            $tests['simple_query'] = false;
            $tests['query_error'] = $e->getMessage();
        }

        // اختبار دوال النظام الموحد
        $tests['connection_functions'] = [
            'getUnifiedDB' => function_exists('getUnifiedDB'),
            'getMainDB' => function_exists('getMainDB'),
            'getOperationsDB' => function_exists('getOperationsDB'),
            'testDatabaseConnection' => function_exists('testDatabaseConnection')
        ];
    }

    return $tests;
}
?>
