<?php
/**
 * حذف فاتورة مبيعات مع معالجة محسنة للأخطاء
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';
redirectIfNotLoggedIn();

try {
    $db = getCurrentUserDB();

    if ($db === null || $db->connect_error) {
        $_SESSION['error'] = "خطأ في الاتصال بقاعدة البيانات";
        header("Location: sales.php");
        exit();
    }

    if (!isset($_GET['id']) || empty($_GET['id'])) {
        $_SESSION['error'] = "معرف الفاتورة مطلوب";
        header("Location: sales.php");
        exit();
    }

    $sale_id = intval($_GET['id']);

    if ($sale_id <= 0) {
        $_SESSION['error'] = "معرف الفاتورة غير صحيح";
        header("Location: sales.php");
        exit();
    }

    // التحقق من وجود الفاتورة أولاً
    $check_stmt = $db->prepare("SELECT id, invoice_number, total_amount FROM sales WHERE id = ?");
    if (!$check_stmt) {
        $_SESSION['error'] = "خطأ في إعداد استعلام التحقق: " . $db->error;
        header("Location: sales.php");
        exit();
    }

    $check_stmt->bind_param("i", $sale_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows === 0) {
        $check_stmt->close();
        $_SESSION['error'] = "فاتورة المبيعات غير موجودة";
        header("Location: sales.php");
        exit();
    }

    $sale = $result->fetch_assoc();
    $invoice_number = $sale['invoice_number'];
    $total_amount = $sale['total_amount'];
    $check_stmt->close();

    // بدء المعاملة لضمان سلامة البيانات
    $db->autocommit(false);
    $db->begin_transaction();

    try {
        // حذف عناصر الفاتورة أولاً
        $delete_items_stmt = $db->prepare("DELETE FROM sale_items WHERE sale_id = ?");
        if (!$delete_items_stmt) {
            throw new Exception("خطأ في إعداد استعلام حذف العناصر: " . $db->error);
        }

        $delete_items_stmt->bind_param("i", $sale_id);
        $delete_items_stmt->execute();
        $deleted_items = $delete_items_stmt->affected_rows;
        $delete_items_stmt->close();

        // حذف الفاتورة
        $delete_sale_stmt = $db->prepare("DELETE FROM sales WHERE id = ?");
        if (!$delete_sale_stmt) {
            throw new Exception("خطأ في إعداد استعلام حذف الفاتورة: " . $db->error);
        }

        $delete_sale_stmt->bind_param("i", $sale_id);

        if ($delete_sale_stmt->execute()) {
            if ($delete_sale_stmt->affected_rows > 0) {
                // تأكيد المعاملة
                $db->commit();
                $_SESSION['success'] = "تم حذف فاتورة المبيعات رقم '$invoice_number' بقيمة $total_amount ر.س و $deleted_items عنصر بنجاح";
            } else {
                $db->rollback();
                $_SESSION['error'] = "لم يتم حذف أي فاتورة. قد تكون الفاتورة محذوفة مسبقاً";
            }
        } else {
            $db->rollback();
            $_SESSION['error'] = "حدث خطأ أثناء حذف فاتورة المبيعات: " . $delete_sale_stmt->error;
        }

        $delete_sale_stmt->close();

    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    } finally {
        $db->autocommit(true);
    }

} catch (mysqli_sql_exception $e) {
    $_SESSION['error'] = "خطأ في قاعدة البيانات: " . $e->getMessage();
    error_log("Database error in delete_sale.php: " . $e->getMessage());
} catch (Exception $e) {
    $_SESSION['error'] = "حدث خطأ غير متوقع: " . $e->getMessage();
    error_log("General error in delete_sale.php: " . $e->getMessage());
}

header("Location: sales.php");
exit();
?>