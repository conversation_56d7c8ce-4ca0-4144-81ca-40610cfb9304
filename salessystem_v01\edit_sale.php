<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()

// التحقق من وضع النافذة المنبثقة
$is_modal = isset($_GET['modal']) && $_GET['modal'] == '1';
// التحقق من وضع العرض المضمن
$is_inline = isset($_GET['inline']) && $_GET['inline'] == '1';

if (!$is_modal && !$is_inline) {
    require_once __DIR__.'/includes/header.php';
}
redirectIfNotLoggedIn();

$db = getCurrentUserDB();

if (!isset($_GET['id'])) {
    header("Location: sales.php");
    exit();
}

$sale_id = intval($_GET['id']);

// جلب بيانات الفاتورة
$stmt = $db->prepare("SELECT * FROM sales WHERE id = ?");
$stmt->bind_param("i", $sale_id);
$stmt->execute();
$result = $stmt->get_result();
$sale = $result->fetch_assoc();

if (!$sale) {
    $_SESSION['error'] = "فاتورة المبيعات غير موجودة";
    header("Location: sales.php");
    exit();
}

// جلب عناصر الفاتورة
$items_result = $db->query("SELECT * FROM sale_items WHERE sale_id = $sale_id AND user_id = " . $_SESSION['user_id']);
$items = [];
while ($item = $items_result->fetch_assoc()) {
    $items[] = $item;
}

// جلب قائمة العملاء والمنتجات
$customers = $db->query("SELECT id, name FROM customers ORDER BY name");
$products_result = $db->query("SELECT id, name, price, tax_rate FROM products ORDER BY name");
$products = [];
while ($product = $products_result->fetch_assoc()) {
    $products[] = $product;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_id = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : NULL;
    $date = $_POST['date'];
    $notes = trim($_POST['notes']);

    // استلام بيانات الدفع
    $payment_method = $_POST['payment_method'] ?? 'cash';
    $payment_status = $_POST['payment_status'] ?? 'unpaid';
    $paid_amount = floatval($_POST['paid_amount'] ?? 0);
    $payment_date = !empty($_POST['payment_date']) ? $_POST['payment_date'] : null;
    $payment_reference = trim($_POST['payment_reference'] ?? '');
    $payment_notes = trim($_POST['payment_notes'] ?? '');

    // معالجة العناصر
    $new_items = [];
    $product_ids = $_POST['product_id'] ?? [];
    $quantities = $_POST['quantity'] ?? [];
    $prices = $_POST['price'] ?? [];
    $tax_rates = $_POST['tax_rate'] ?? [];

    for ($i = 0; $i < count($product_ids); $i++) {
        if (!empty($product_ids[$i]) && !empty($quantities[$i])) {
            $new_items[] = [
                'product_id' => intval($product_ids[$i]),
                'quantity' => intval($quantities[$i]),
                'unit_price' => floatval($prices[$i]),
                'tax_rate' => floatval($tax_rates[$i])
            ];
        }
    }

    if (empty($new_items)) {
        $_SESSION['error'] = "يجب إضافة عنصر واحد على الأقل";

        // إذا كان في وضع النافذة المنبثقة أو العرض المضمن، أرسل استجابة JSON
        if ($is_modal || $is_inline) {
            echo json_encode(['success' => false, 'message' => 'يجب إضافة عنصر واحد على الأقل']);
            exit();
        }
    } else {
        // حساب المجموع والضريبة
        $calculations = calculateTaxAndTotal($new_items);

        // حساب المبلغ المتبقي
        $remaining_amount = $calculations['total'] - $paid_amount;

        // تحديث الفاتورة مع بيانات الدفع
        $stmt = $db->prepare("UPDATE sales SET
                             customer_id = ?,
                             date = ?,
                             subtotal = ?,
                             tax_amount = ?,
                             total_amount = ?,
                             payment_method = ?,
                             payment_status = ?,
                             paid_amount = ?,
                             remaining_amount = ?,
                             payment_date = ?,
                             payment_reference = ?,
                             payment_notes = ?,
                             notes = ?
                             WHERE id = ?");
        $stmt->bind_param("isdddssddssssi", $customer_id, $date,
                         $calculations['subtotal'], $calculations['tax_amount'],
                         $calculations['total'], $payment_method, $payment_status,
                         $paid_amount, $remaining_amount, $payment_date,
                         $payment_reference, $payment_notes, $notes, $sale_id);

        if ($stmt->execute()) {
            // حذف العناصر القديمة
            $db->query("DELETE FROM sale_items WHERE sale_id = $sale_id AND user_id = " . $_SESSION['user_id']);

            // إضافة العناصر الجديدة
            $item_stmt = $db->prepare("INSERT INTO sale_items
                                     (user_id, sale_id, product_id, quantity, unit_price, tax_rate, tax_amount, total_price)
                                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

            foreach ($new_items as $item) {
                $item_total = $item['quantity'] * $item['unit_price'];
                $item_tax = $item_total * ($item['tax_rate'] / 100);
                $total_price = $item_total + $item_tax;

                $item_stmt->bind_param("iiiidddd", $_SESSION['user_id'], $sale_id, $item['product_id'], $item['quantity'],
                                      $item['unit_price'], $item['tax_rate'], $item_tax, $total_price);
                $item_stmt->execute();
            }

            $_SESSION['success'] = "تم تحديث فاتورة المبيعات بنجاح";

            // إذا كان في وضع النافذة المنبثقة أو العرض المضمن، أرسل استجابة JSON
            if ($is_modal || $is_inline) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث فاتورة المبيعات بنجاح']);
                exit();
            }

            header("Location: sales.php");
            exit();
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث فاتورة المبيعات";

            // إذا كان في وضع النافذة المنبثقة أو العرض المضمن، أرسل استجابة JSON
            if ($is_modal || $is_inline) {
                echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث فاتورة المبيعات']);
                exit();
            }
        }
    }
}
?>

<?php if (!$is_modal && !$is_inline): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">تعديل فاتورة مبيعات</div>
            <div class="card-body">
<?php elseif ($is_inline): ?>
<!-- محتوى العرض المضمن -->
<div class="inline-content-wrapper">
<?php else: ?>
<!-- محتوى النافذة المنبثقة -->
<div class="modal-content-wrapper">
<?php endif; ?>
                <form method="POST" id="saleForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل</label>
                                <select class="form-select" id="customer_id" name="customer_id">
                                    <option value="">-- اختر عميل --</option>
                                    <?php while ($customer = $customers->fetch_assoc()): ?>
                                    <option value="<?php echo $customer['id']; ?>" <?php echo ($sale['customer_id'] == $customer['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date" class="form-label"><?php echo __('invoice_date'); ?></label>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo $sale['date']; ?>" required>
                            </div>
                        </div>
                    </div>

                    <h5 class="mt-4">عناصر الفاتورة</h5>
                    <div class="table-responsive">
                        <table class="table" id="itemsTable">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الضريبة %</th>
                                    <th>المجموع</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsBody">
                                <!-- سيتم إضافة العناصر ديناميكيًا هنا -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="6">
                                        <button type="button" class="btn btn-sm btn-success" id="addItemBtn">
                                            <i class="fas fa-plus"></i> إضافة عنصر
                                        </button>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- قسم خيارات الدفع -->
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-credit-card me-2"></i>
                                        خيارات الدفع
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                                <select class="form-select" id="payment_method" name="payment_method" onchange="updatePaymentFields()">
                                                    <option value="cash" <?php echo ($sale['payment_method'] ?? 'cash') == 'cash' ? 'selected' : ''; ?>>نقدي</option>
                                                    <option value="card" <?php echo ($sale['payment_method'] ?? '') == 'card' ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                                                    <option value="bank_transfer" <?php echo ($sale['payment_method'] ?? '') == 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                                                    <option value="check" <?php echo ($sale['payment_method'] ?? '') == 'check' ? 'selected' : ''; ?>>شيك</option>
                                                    <option value="installment" <?php echo ($sale['payment_method'] ?? '') == 'installment' ? 'selected' : ''; ?>>تقسيط</option>
                                                    <option value="other" <?php echo ($sale['payment_method'] ?? '') == 'other' ? 'selected' : ''; ?>>أخرى</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="payment_status" class="form-label">حالة الدفع</label>
                                                <select class="form-select" id="payment_status" name="payment_status" onchange="updatePaymentFields()">
                                                    <option value="unpaid" <?php echo ($sale['payment_status'] ?? 'unpaid') == 'unpaid' ? 'selected' : ''; ?>>غير مدفوع</option>
                                                    <option value="partial" <?php echo ($sale['payment_status'] ?? '') == 'partial' ? 'selected' : ''; ?>>مدفوع جزئياً</option>
                                                    <option value="paid" <?php echo ($sale['payment_status'] ?? '') == 'paid' ? 'selected' : ''; ?>>مدفوع بالكامل</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="paid_amount" name="paid_amount"
                                                           step="0.01" min="0" value="<?php echo $sale['paid_amount'] ?? 0; ?>" onchange="calculateRemainingAmount()">
                                                    <span class="input-group-text">ر.س</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="payment_date" class="form-label">تاريخ الدفع</label>
                                                <input type="date" class="form-control" id="payment_date" name="payment_date"
                                                       value="<?php echo $sale['payment_date'] ?? ''; ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="payment_reference" class="form-label">مرجع الدفع</label>
                                                <input type="text" class="form-control" id="payment_reference" name="payment_reference"
                                                       placeholder="رقم الشيك، رقم التحويل، إلخ..." value="<?php echo htmlspecialchars($sale['payment_reference'] ?? ''); ?>">
                                                <small class="form-text text-muted">اختياري - رقم الشيك، رقم التحويل، أو أي مرجع آخر</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="payment_notes" class="form-label">ملاحظات الدفع</label>
                                                <textarea class="form-control" id="payment_notes" name="payment_notes" rows="2"
                                                          placeholder="ملاحظات إضافية حول الدفع..."><?php echo htmlspecialchars($sale['payment_notes'] ?? ''); ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات عامة</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($sale['notes']); ?></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">ملخص الفاتورة</div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <th>المجموع الفرعي:</th>
                                            <td id="subtotalCell"><?php echo number_format($sale['subtotal'], 2); ?> ر.س</td>
                                        </tr>
                                        <tr>
                                            <th>الضريبة:</th>
                                            <td id="taxCell"><?php echo number_format($sale['tax_amount'], 2); ?> ر.س</td>
                                        </tr>
                                        <tr class="table-primary">
                                            <th>الإجمالي:</th>
                                            <td id="totalCell" class="fw-bold"><?php echo number_format($sale['total_amount'], 2); ?> ر.س</td>
                                        </tr>
                                        <tr class="table-success">
                                            <th>المبلغ المدفوع:</th>
                                            <td id="paidCell" class="text-success"><?php echo number_format($sale['paid_amount'] ?? 0, 2); ?> ر.س</td>
                                        </tr>
                                        <tr id="remainingRow" class="<?php echo (($sale['remaining_amount'] ?? 0) > 0) ? 'table-danger' : 'table-success'; ?>">
                                            <th>المبلغ المتبقي:</th>
                                            <td id="remainingCell" class="<?php echo (($sale['remaining_amount'] ?? 0) > 0) ? 'text-danger' : 'text-success'; ?> fw-bold">
                                                <?php echo number_format($sale['remaining_amount'] ?? 0, 2); ?> ر.س
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        <?php if (!$is_modal && !$is_inline): ?>
                        <a href="sales.php" class="btn btn-secondary">إلغاء</a>
                        <?php elseif ($is_inline): ?>
                        <button type="button" class="btn btn-secondary" onclick="hideEditForm()">إلغاء</button>
                        <?php else: ?>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <?php endif; ?>
                    </div>
                </form>

<?php if (!$is_modal && !$is_inline): ?>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
</div>
<?php endif; ?>

<script>
// متغير لتخزين بيانات المنتجات
let products = [];

// تحميل بيانات المنتجات
<?php if ($is_inline): ?>
// في حالة العرض المضمن، نحتاج لتحميل البيانات عبر AJAX
fetch('get_products.php')
    .then(response => response.json())
    .then(data => {
        products = data;
        // تشغيل التهيئة بعد تحميل المنتجات
        if (typeof initializeForm === 'function') {
            initializeForm();
        }
    })
    .catch(error => {
        console.error('Error loading products:', error);
        products = [];
        // حتى لو فشل تحميل المنتجات، جرب التهيئة
        if (typeof initializeForm === 'function') {
            initializeForm();
        }
    });
<?php else: ?>
// في الحالة العادية، استخدم البيانات من PHP
products = [
    <?php
    foreach ($products as $product):
        echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}},";
    endforeach;
    ?>
];
<?php endif; ?>

// دالة لإضافة صف عنصر جديد
function addItemRow(item = null) {
    const tbody = document.getElementById('itemsBody');
    if (!tbody) {
        console.error('itemsBody element not found!');
        return null;
    }

    const rowId = Date.now();

    const row = document.createElement('tr');
    row.id = `row_${rowId}`;

    // عمود المنتج
    const productCell = document.createElement('td');
    const productSelect = document.createElement('select');
    productSelect.className = 'form-select product-select';
    productSelect.name = 'product_id[]';
    productSelect.required = true;

    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '-- اختر منتج --';
    productSelect.appendChild(defaultOption);

    products.forEach(p => {
        const option = document.createElement('option');
        option.value = p.id;
        option.textContent = p.name;
        option.dataset.price = p.price;
        option.dataset.taxRate = p.tax_rate;

        if (item && item.product_id == p.id) {
            option.selected = true;
        }

        productSelect.appendChild(option);
    });

    productSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const price = selectedOption.dataset.price || 0;
        const taxRate = selectedOption.dataset.taxRate || 0;

        const row = this.closest('tr');
        row.querySelector('.price-input').value = price;
        row.querySelector('.tax-rate-input').value = taxRate;

        calculateRowTotal(row);
        updateInvoiceSummary();
    });

    productCell.appendChild(productSelect);
    row.appendChild(productCell);

    // عمود الكمية
    const quantityCell = document.createElement('td');
    const quantityInput = document.createElement('input');
    quantityInput.type = 'number';
    quantityInput.className = 'form-control quantity-input';
    quantityInput.name = 'quantity[]';
    quantityInput.min = '1';
    quantityInput.value = item ? item.quantity : '1';
    quantityInput.required = true;
    quantityInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    quantityCell.appendChild(quantityInput);
    row.appendChild(quantityCell);

    // عمود السعر
    const priceCell = document.createElement('td');
    const priceInput = document.createElement('input');
    priceInput.type = 'number';
    priceInput.className = 'form-control price-input';
    priceInput.name = 'price[]';
    priceInput.step = '0.01';
    priceInput.min = '0';
    priceInput.value = item ? item.unit_price : '0';
    priceInput.required = true;
    priceInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    priceCell.appendChild(priceInput);
    row.appendChild(priceCell);

    // عمود الضريبة
    const taxCell = document.createElement('td');
    const taxInput = document.createElement('input');
    taxInput.type = 'number';
    taxInput.className = 'form-control tax-rate-input';
    taxInput.name = 'tax_rate[]';
    taxInput.step = '0.01';
    taxInput.min = '0';
    taxInput.value = item ? item.tax_rate : '0';
    taxInput.required = true;
    taxInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    taxCell.appendChild(taxInput);
    row.appendChild(taxCell);

    // عمود المجموع
    const totalCell = document.createElement('td');
    totalCell.className = 'row-total';
    totalCell.textContent = item ? (item.total_price).toFixed(2) + ' ر.س' : '0.00 ر.س';
    row.appendChild(totalCell);

    // عمود الإجراءات
    const actionsCell = document.createElement('td');
    const deleteBtn = document.createElement('button');
    deleteBtn.type = 'button';
    deleteBtn.className = 'btn btn-sm btn-danger';
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
    deleteBtn.addEventListener('click', function() {
        row.remove();
        updateInvoiceSummary();
    });
    actionsCell.appendChild(deleteBtn);
    row.appendChild(actionsCell);

    tbody.appendChild(row);

    // إذا تم تمرير عنصر، احسب المجموع للصف
    if (item) {
        calculateRowTotal(row);
    }

    return row;
}

// دالة لحساب مجموع الصف
function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.price-input').value) || 0;
    const taxRate = parseFloat(row.querySelector('.tax-rate-input').value) || 0;

    const subtotal = quantity * price;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    row.querySelector('.row-total').textContent = total.toFixed(2) + ' ر.س';
}

// دالة لتحديث ملخص الفاتورة
function updateInvoiceSummary() {
    let subtotal = 0;
    let taxAmount = 0;

    document.querySelectorAll('#itemsBody tr').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(row.querySelector('.price-input').value) || 0;
        const taxRate = parseFloat(row.querySelector('.tax-rate-input').value) || 0;

        const rowSubtotal = quantity * price;
        const rowTax = rowSubtotal * (taxRate / 100);

        subtotal += rowSubtotal;
        taxAmount += rowTax;
    });

    const total = subtotal + taxAmount;

    document.getElementById('subtotalCell').textContent = subtotal.toFixed(2) + ' ر.س';
    document.getElementById('taxCell').textContent = taxAmount.toFixed(2) + ' ر.س';
    document.getElementById('totalCell').textContent = total.toFixed(2) + ' ر.س';

    // تحديث حقول الدفع تلقائياً
    updatePaymentFields();
}

// دالة لتحديث حقول الدفع بناءً على حالة الدفع
function updatePaymentFields() {
    const paymentStatus = document.getElementById('payment_status').value;
    const paidAmountField = document.getElementById('paid_amount');
    const paymentDateField = document.getElementById('payment_date');
    const totalAmount = parseFloat(document.getElementById('totalCell').textContent.replace(' ر.س', '')) || 0;

    if (paymentStatus === 'paid') {
        // إذا كان مدفوع بالكامل، اجعل المبلغ المدفوع = الإجمالي
        paidAmountField.value = totalAmount.toFixed(2);
        paymentDateField.required = true;
    } else if (paymentStatus === 'unpaid') {
        // إذا كان غير مدفوع، اجعل المبلغ المدفوع = 0
        paidAmountField.value = '0.00';
        paymentDateField.required = false;
    } else if (paymentStatus === 'partial') {
        // إذا كان مدفوع جزئياً، اتركه كما هو
        paymentDateField.required = true;
    }

    calculateRemainingAmount();
}

// دالة لحساب المبلغ المتبقي
function calculateRemainingAmount() {
    const totalAmount = parseFloat(document.getElementById('totalCell').textContent.replace(' ر.س', '')) || 0;
    const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
    const remainingAmount = totalAmount - paidAmount;

    // تحديث عرض المبلغ المدفوع والمتبقي
    document.getElementById('paidCell').textContent = paidAmount.toFixed(2) + ' ر.س';
    document.getElementById('remainingCell').textContent = remainingAmount.toFixed(2) + ' ر.س';

    // تغيير ألوان المبلغ المتبقي
    const remainingRow = document.getElementById('remainingRow');
    const remainingCell = document.getElementById('remainingCell');

    if (remainingAmount > 0) {
        remainingRow.className = 'table-danger';
        remainingCell.className = 'text-danger fw-bold';
    } else {
        remainingRow.className = 'table-success';
        remainingCell.className = 'text-success fw-bold';
    }
}

// دالة تهيئة النموذج
function initializeForm() {
    // إضافة العناصر الموجودة
    <?php foreach ($items as $item): ?>
        addItemRow({
            product_id: <?php echo $item['product_id']; ?>,
            quantity: <?php echo $item['quantity']; ?>,
            unit_price: <?php echo $item['unit_price']; ?>,
            tax_rate: <?php echo $item['tax_rate']; ?>,
            total_price: <?php echo $item['total_price']; ?>
        });
    <?php endforeach; ?>

    // إضافة صف فارغ إذا لم يكن هناك عناصر
    if (document.querySelectorAll('#itemsBody tr').length === 0) {
        addItemRow();
    }

    // معالجة زر إضافة عنصر
    const addBtn = document.getElementById('addItemBtn');
    if (addBtn) {
        addBtn.addEventListener('click', function() {
            addItemRow();
        });
    }

    // تحديث الملخص عند أي تغيير
    const itemsBody = document.getElementById('itemsBody');
    if (itemsBody) {
        itemsBody.addEventListener('input', function() {
            updateInvoiceSummary();
        });
    }

    // معالجة إرسال النموذج في النوافذ المنبثقة
    <?php if ($is_modal || $is_inline): ?>
    const saleForm = document.getElementById('saleForm');
    if (saleForm) {
        saleForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    <?php if ($is_modal): ?>
                    // إغلاق النافذة المنبثقة وإعادة تحميل الصفحة الرئيسية
                    const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
                    if (modal) {
                        modal.hide();
                    }
                    // إعادة تحميل الصفحة الرئيسية
                    if (window.parent && window.parent.location) {
                        window.parent.location.reload();
                    } else {
                        window.location.reload();
                    }
                    <?php elseif ($is_inline): ?>
                    // إظهار رسالة نجاح
                    if (typeof showSuccessMessage === 'function') {
                        showSuccessMessage(data.message || 'تم تحديث فاتورة المبيعات بنجاح');
                    }

                    // إخفاء نموذج التعديل وإعادة تحميل الصفحة
                    setTimeout(() => {
                        if (typeof hideEditForm === 'function') {
                            hideEditForm();
                        }
                        window.location.reload();
                    }, 1500);
                    <?php endif; ?>
                } else {
                    // عرض رسالة الخطأ
                    alert(data.message || 'حدث خطأ أثناء حفظ البيانات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ البيانات');
            });
        });
    }
    <?php endif; ?>
}

// تشغيل التهيئة عند تحميل الصفحة أو عند تحميل المحتوى عبر AJAX
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeForm);
} else {
    // إذا كان DOM جاهزاً بالفعل (في حالة AJAX)
    initializeForm();
}
</script>

<?php
if (!$is_modal && !$is_inline) {
    require_once 'includes/footer.php';
}
?>