<?php
/**
 * إصلاح نهائي شامل لخطأ $transaction_type غير المعرف
 */

echo "<h1>🔧 إصلاح نهائي لخطأ transaction_type</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }
</style>";

echo "<div class='section'>";
echo "<h2>1. فحص الملف الحالي</h2>";

$file_path = __DIR__ . '/reports.php';
if (!file_exists($file_path)) {
    echo "<p class='error'>❌ ملف reports.php غير موجود</p>";
    exit;
}

$content = file_get_contents($file_path);
if ($content === false) {
    echo "<p class='error'>❌ فشل في قراءة ملف reports.php</p>";
    exit;
}

echo "<p class='info'>تم قراءة الملف بنجاح (" . strlen($content) . " حرف)</p>";

// البحث عن أي استخدام خاطئ لـ $transaction_type
$lines = explode("\n", $content);
$problematic_lines = [];

foreach ($lines as $line_num => $line) {
    // البحث عن $transaction_type بدون أقواس مربعة (أي ليس $transaction['transaction_type'])
    if (preg_match('/\$transaction_type(?!\[)/', $line) && !preg_match('/\$transaction_type_clean/', $line)) {
        $problematic_lines[] = [
            'line_num' => $line_num + 1,
            'content' => trim($line)
        ];
    }
}

if (!empty($problematic_lines)) {
    echo "<h3 class='error'>❌ تم العثور على استخدامات خاطئة لـ \$transaction_type:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم السطر</th><th>المحتوى</th></tr>";
    foreach ($problematic_lines as $problem) {
        echo "<tr>";
        echo "<td>{$problem['line_num']}</td>";
        echo "<td><code>" . htmlspecialchars($problem['content']) . "</code></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='success'>✅ لم يتم العثور على استخدامات خاطئة لـ \$transaction_type</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. تطبيق الإصلاحات</h2>";

$fixes_applied = 0;
$backup_created = false;

try {
    // إنشاء نسخة احتياطية
    $backup_path = __DIR__ . '/reports_backup_final_fix_' . date('Y-m-d_H-i-s') . '.php';
    if (file_put_contents($backup_path, $content) !== false) {
        echo "<p class='success'>✅ تم إنشاء نسخة احتياطية: " . basename($backup_path) . "</p>";
        $backup_created = true;
    }
    
    // الإصلاح 1: إزالة أي استخدام خاطئ لـ $transaction_type
    $patterns_to_fix = [
        // البحث عن أي سطر يحتوي على $transaction_type بدون أقواس مربعة
        '/\$transaction_type(?!\[|\s*=|\s*_)/' => '$transaction[\'transaction_type\']',
        
        // إصلاح أي استخدام في شروط if
        '/if\s*\(\s*\$transaction_type\s*===/' => 'if ($transaction[\'transaction_type\'] ===',
        '/if\s*\(\s*\$transaction_type\s*==/' => 'if ($transaction[\'transaction_type\'] ==',
        
        // إصلاح أي استخدام في echo أو print
        '/echo\s+\$transaction_type/' => 'echo $transaction[\'transaction_type\']',
        '/print\s+\$transaction_type/' => 'print $transaction[\'transaction_type\']',
    ];
    
    foreach ($patterns_to_fix as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $fixes_applied++;
            echo "<p class='success'>✅ تم إصلاح نمط: " . htmlspecialchars($pattern) . "</p>";
        }
    }
    
    // الإصلاح 2: التأكد من عدم وجود أي متغير $transaction_type منفرد
    $lines = explode("\n", $content);
    $modified = false;
    
    foreach ($lines as $line_num => &$line) {
        // إذا كان السطر يحتوي على $transaction_type منفرد (ليس جزء من array)
        if (preg_match('/\$transaction_type(?!\[|_clean)/', $line) && !preg_match('/\/\//', $line)) {
            // تحويله إلى تعليق
            $line = '        // ' . trim($line) . ' // تم تعطيله لتجنب خطأ المتغير غير المعرف';
            $modified = true;
            $fixes_applied++;
            echo "<p class='success'>✅ تم تعطيل السطر " . ($line_num + 1) . "</p>";
        }
    }
    
    if ($modified) {
        $content = implode("\n", $lines);
    }
    
    // الإصلاح 3: إضافة تحقق إضافي في بداية حلقة foreach
    $foreach_pattern = '/foreach\s*\(\s*\$account_transactions\s+as\s+&?\$transaction\s*\)\s*\{/';
    if (preg_match($foreach_pattern, $content)) {
        $replacement = 'foreach ($account_transactions as &$transaction) {
        // التأكد من وجود transaction_type
        if (!isset($transaction[\'transaction_type\'])) {
            $transaction[\'transaction_type\'] = \'unknown\';
            error_log("Missing transaction_type for transaction ID: " . ($transaction[\'id\'] ?? \'unknown\'));
        }';
        
        $content = preg_replace($foreach_pattern, $replacement, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم إضافة تحقق من وجود transaction_type</p>";
    }
    
    // حفظ الملف المحدث
    if (file_put_contents($file_path, $content) !== false) {
        echo "<p class='success'>✅ تم حفظ الملف المحدث بنجاح</p>";
        echo "<p class='info'>إجمالي الإصلاحات المطبقة: $fixes_applied</p>";
    } else {
        throw new Exception('فشل في حفظ الملف المحدث');
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    
    // استعادة النسخة الاحتياطية في حالة الخطأ
    if ($backup_created && file_exists($backup_path)) {
        file_put_contents($file_path, file_get_contents($backup_path));
        echo "<p class='warning'>⚠️ تم استعادة النسخة الاحتياطية</p>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. فحص نهائي</h2>";

// إعادة قراءة الملف للتحقق
$updated_content = file_get_contents($file_path);
$updated_lines = explode("\n", $updated_content);
$remaining_issues = [];

foreach ($updated_lines as $line_num => $line) {
    if (preg_match('/\$transaction_type(?!\[|_clean)/', $line) && !preg_match('/\/\//', $line)) {
        $remaining_issues[] = [
            'line_num' => $line_num + 1,
            'content' => trim($line)
        ];
    }
}

if (!empty($remaining_issues)) {
    echo "<h3 class='warning'>⚠️ لا تزال هناك مشاكل:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم السطر</th><th>المحتوى</th></tr>";
    foreach ($remaining_issues as $issue) {
        echo "<tr>";
        echo "<td>{$issue['line_num']}</td>";
        echo "<td><code>" . htmlspecialchars($issue['content']) . "</code></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='success'>✅ تم إصلاح جميع المشاكل بنجاح!</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. اختبار الإصلاح</h2>";

echo "<h3>اختبار كشف الحساب:</h3>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب</a></li>";
echo "<li><a href='final_account_statement_test.php' target='_blank'>اختبار شامل</a></li>";
echo "</ul>";

echo "<h3>مراقبة السجلات:</h3>";
echo "<p class='info'>تحقق من ملفات السجلات للتأكد من عدم ظهور أخطاء جديدة</p>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. ملخص الإصلاحات</h2>";

if ($fixes_applied > 0) {
    echo "<p class='success'>✅ تم تطبيق $fixes_applied إصلاح</p>";
    
    echo "<h3>الإصلاحات المطبقة:</h3>";
    echo "<ul>";
    echo "<li>إصلاح استخدامات \$transaction_type الخاطئة</li>";
    echo "<li>تحويل الأسطر المشكوك فيها إلى تعليقات</li>";
    echo "<li>إضافة تحقق من وجود transaction_type</li>";
    echo "<li>تحسين معالجة الأخطاء</li>";
    echo "</ul>";
    
} else {
    echo "<p class='info'>لم يتم العثور على مشاكل تحتاج إلى إصلاح</p>";
}

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الإصلاح النهائي في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
