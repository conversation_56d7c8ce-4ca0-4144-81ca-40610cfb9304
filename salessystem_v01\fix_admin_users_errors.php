<?php
/**
 * إصلاح أخطاء admin_users.php
 */

$file_path = __DIR__ . '/admin_users.php';

echo "<h2>🔧 إصلاح أخطاء admin_users.php</h2>";

if (!file_exists($file_path)) {
    echo "<div style='color: red;'>❌ الملف غير موجود</div>";
    exit;
}

$content = file_get_contents($file_path);
if ($content === false) {
    echo "<div style='color: red;'>❌ فشل في قراءة الملف</div>";
    exit;
}

// عد الأخطاء قبل الإصلاح
$error_count_before = substr_count($content, '$$');
echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>📊 حالة الملف قبل الإصلاح:</h4>";
echo "<p>عدد أخطاء $$: <strong>$error_count_before</strong></p>";
echo "</div>";

// إنشاء نسخة احتياطية
$backup_path = $file_path . '.fix_backup.' . date('Y-m-d-H-i-s');
copy($file_path, $backup_path);

// إصلاح جميع أخطاء $$
$fixes_applied = [];

// 1. إصلاح المتغيرات الأساسية
$basic_fixes = [
    '$$_SESSION' => '$_SESSION',
    '$$_GET' => '$_GET', 
    '$$_POST' => '$_POST',
    '$$_SERVER' => '$_SERVER',
    '$$user' => '$user',
    '$$users_stats' => '$users_stats',
    '$$query_params' => '$query_params'
];

foreach ($basic_fixes as $wrong => $correct) {
    $count = substr_count($content, $wrong);
    if ($count > 0) {
        $content = str_replace($wrong, $correct, $content);
        $fixes_applied[] = "إصلاح $count حالة من $wrong إلى $correct";
    }
}

// 2. إصلاح مشاكل التنسيق الأخرى
$formatting_fixes = [
    // إصلاح الأسطر المكسورة
    "if (\$user_id >\n 0)" => "if (\$user_id > 0)",
    "if (\$uid\n<= 0)" => "if (\$uid <= 0)",
    "if (\$success_count >\n 0)" => "if (\$success_count > 0)",
    "if (\$error_count >\n 0)" => "if (\$error_count > 0)",
    "if (\$total_pages >\n 1)" => "if (\$total_pages > 1)",
    
    // إصلاح الدوال المكسورة
    "\$db->\nprepare(" => "\$db->prepare(",
    "\$stmt->\nbind_param(" => "\$stmt->bind_param(",
    "\$stmt->\nexecute(" => "\$stmt->execute(",
    "\$stmt->\nclose(" => "\$stmt->close(",
    "\$db->\nbegin_transaction(" => "\$db->begin_transaction(",
    "\$db->\ncommit(" => "\$db->commit(",
    "\$db->\nrollback(" => "\$db->rollback(",
    "\$db->\nquery(" => "\$db->query(",
    "\$count_stmt->\nbind_param(" => "\$count_stmt->bind_param(",
    "\$count_stmt->\nexecute(" => "\$count_stmt->execute(",
    "\$count_stmt->\nget_result()->\nfetch_assoc()[" => "\$count_stmt->get_result()->fetch_assoc()[",
    "\$count_stmt->\nclose(" => "\$count_stmt->close(",
    "\$count_result->\nfetch_assoc()[" => "\$count_result->fetch_assoc()[",
    "\$stmt->\nget_result(" => "\$stmt->get_result(",
    "\$users_result = \$db->\nquery(" => "\$users_result = \$db->query(",
    
    // إصلاح المصفوفات
    "[\n    'total'\n]" => "['total']",
    "[\n    'user_id' =>\n \$user_id]" => "['user_id' => \$user_id]",
    
    // إصلاح JavaScript المكسور
    "function exportUsers()    {" => "function exportUsers() {",
    "url += '&user_ids=' + userIds.join(',\n    ');" => "url += '&user_ids=' + userIds.join(',');",
    
    // إصلاح echo المكسورة
    "echo \$e->\ngetMessage()" => "echo \$e->getMessage()",
    "echo \$db->\nerror" => "echo \$db->error",
    "echo \$stmt->\nerror" => "echo \$stmt->error",
];

foreach ($formatting_fixes as $wrong => $correct) {
    if (strpos($content, $wrong) !== false) {
        $content = str_replace($wrong, $correct, $content);
        $fixes_applied[] = "إصلاح تنسيق: " . substr($wrong, 0, 30) . "...";
    }
}

// 3. إصلاح مشاكل HTML
$html_fixes = [
    'title=t("delete")' => 'title="حذف"',
    'echo \$user[\'status\'] === \'active\' ? \'fa-check\' : \'fa-times\';' => 'echo $user[\'status\'] === \'active\' ? \'fa-check\' : \'fa-times\';',
    'echo \$user[\'status\'] === \'active\' ? t("active") : t("inactive");' => 'echo $user[\'status\'] === \'active\' ? "نشط" : "غير نشط";',
    'echo \$user[\'status\'] === \'active\' ? \'warning\' : \'success\';' => 'echo $user[\'status\'] === \'active\' ? \'warning\' : \'success\';',
    'echo \$user[\'status\'] === \'active\' ? \'إلغاء التفعيل\' : \'تفعيل\';' => 'echo $user[\'status\'] === \'active\' ? \'إلغاء التفعيل\' : \'تفعيل\';',
    'echo \$user[\'status\'] === \'active\' ? \'fa-ban\' : \'fa-check\';' => 'echo $user[\'status\'] === \'active\' ? \'fa-ban\' : \'fa-check\';',
];

foreach ($html_fixes as $wrong => $correct) {
    if (strpos($content, $wrong) !== false) {
        $content = str_replace($wrong, $correct, $content);
        $fixes_applied[] = "إصلاح HTML: " . substr($wrong, 0, 30) . "...";
    }
}

// حفظ الملف المُصلح
if (file_put_contents($file_path, $content) !== false) {
    $error_count_after = substr_count($content, '$$');
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>✅ تم إصلاح الملف بنجاح!</h4>";
    echo "<p>📁 نسخة احتياطية: " . basename($backup_path) . "</p>";
    echo "<p>📊 أخطاء $$: $error_count_before → $error_count_after</p>";
    echo "<p>🔧 عدد الإصلاحات المطبقة: " . count($fixes_applied) . "</p>";
    echo "</div>";
    
    if (!empty($fixes_applied)) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>📋 تفاصيل الإصلاحات:</h4>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if ($error_count_after === 0) {
        echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
        echo "<h3>🎉 تم إصلاح جميع الأخطاء!</h3>";
        echo "<p>الملف الآن خالي من أخطاء $$ تماماً</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3e0; color: #f57c00; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
        echo "<h3>⚠️ متبقي $error_count_after خطأ</h3>";
        echo "<p>قد تحتاج إصلاح يدوي إضافي</p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #ffebee; color: #d32f2f; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>❌ فشل في حفظ الملف</h3>";
    echo "</div>";
}

// روابط الاختبار
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار الملف:</h4>";
echo "<a href='admin_users.php' target='_blank' style='background: #4caf50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🧪 اختبار admin_users.php</a>";
echo "<a href='comprehensive_error_scanner.php' style='background: #2196f3; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🔍 فحص شامل</a>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🏠 لوحة التحكم</a>";
echo "</div>";
?>
