<?php
session_start();
require_once 'config/unified_db_config.php';
require_once 'config/email_config.php';
require_once 'includes/functions.php';

// إذا كان المستخدم مسجل دخوله، إعادة توجيه للصفحة الرئيسية
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$message = '';
$error = '';

// معالجة طلب إعادة تعيين كلمة المرور
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reset_password'])) {
    $email = trim($_POST['email']);
    
    if (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } else {
        $db = getDB();
        if ($db) {
            // التحقق من وجود الأعمدة المطلوبة أولاً
            $check_columns = $db->query("SHOW COLUMNS FROM users LIKE 'reset_token'");
            if (!$check_columns || $check_columns->num_rows == 0) {
                $error = 'النظام غير مهيأ بشكل صحيح. يرجى <a href="update_database.php">تحديث قاعدة البيانات</a> أولاً.';
            } else {
                // التحقق من وجود البريد الإلكتروني
                $stmt = $db->prepare("SELECT id, username, full_name FROM users WHERE email = ? AND status = 'active'");
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $user = $result->fetch_assoc();

                    // تنظيف الرموز المنتهية الصلاحية أولاً
                    cleanExpiredTokens();

                    // إنشاء رمز إعادة تعيين كلمة المرور
                    $reset_token = generateResetToken();
                    $expires = date('Y-m-d H:i:s', time() + RESET_TOKEN_EXPIRY);

                    // حفظ الرمز في قاعدة البيانات
                    $update_stmt = $db->prepare("UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?");
                    $update_stmt->bind_param("ssi", $reset_token, $expires, $user['id']);

                    if ($update_stmt->execute()) {
                        // إنشاء رابط إعادة التعيين
                        $reset_link = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/reset_password.php?token=" . $reset_token;

                        // إرسال البريد الإلكتروني
                        $email_sent = sendEnhancedPasswordResetEmail($email, $reset_link, $user['full_name'] ?: $user['username']);

                        if ($email_sent) {
                            $message = "تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.";
                            $message .= "<br><small class='text-muted'>يرجى التحقق من صندوق الوارد وصندوق الرسائل غير المرغوب فيها.</small>";

                            // تسجيل العملية
                            error_log("Password reset requested for user: " . $user['username'] . " (" . $email . ")");
                        } else {
                            $error = 'حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.';
                        }
                    } else {
                        $error = 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.';
                    }
                    $update_stmt->close();
                } else {
                    // عرض رسالة واضحة أن البريد غير مسجل
                    $error = "البريد الإلكتروني المُدخل غير مسجل في النظام. يرجى التأكد من البريد الإلكتروني أو إنشاء حساب جديد.";
                }
                $stmt->close();
            }
        } else {
            $error = 'خطأ في الاتصال بقاعدة البيانات';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسيت كلمة المرور - نظام إدارة المبيعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .forgot-password-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 20px;
        }
        .forgot-password-header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .forgot-password-header h2 {
            margin: 0;
            font-weight: 600;
        }
        .forgot-password-body {
            padding: 2rem;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            border: none;
            padding: 12px;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .back-to-login {
            text-align: center;
            margin-top: 1rem;
        }
        .back-to-login a {
            color: #6b7280;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        .back-to-login a:hover {
            color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <div class="forgot-password-header">
            <i class="fas fa-key fa-2x mb-3"></i>
            <h2>نسيت كلمة المرور؟</h2>
            <p class="mb-0">أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور</p>
        </div>
        
        <div class="forgot-password-body">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $message; ?>
                </div>
            <?php else: ?>
                <form method="POST" action="">
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="البريد الإلكتروني" required>
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                        </label>
                    </div>
                    
                    <button type="submit" name="reset_password" class="btn btn-primary w-100">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال رابط إعادة التعيين
                    </button>
                </form>
            <?php endif; ?>
            
            <div class="back-to-login">
                <a href="login.php">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة إلى تسجيل الدخول
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
