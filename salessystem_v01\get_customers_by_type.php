<?php
/**
 * جلب العملاء أو الموردين حسب النوع
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// إعداد الاستجابة JSON
header('Content-Type: application/json');

try {
    // الحصول على نوع العميل
    $customer_type = $_GET['type'] ?? 'customer';
    
    // التحقق من صحة النوع
    if (!in_array($customer_type, ['customer', 'supplier'])) {
        throw new Exception('نوع العميل غير صحيح');
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = getCurrentUserDB();
    if ($db === null || $db->connect_error) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    // الحصول على اسم الجدول مع البادئة
    $username = $_SESSION['username'];
    $customers_table = getUserTableName('customers', $username);
    
    // جلب العملاء/الموردين
    $query = "SELECT id, name, phone, email FROM `$customers_table` 
              WHERE user_id = ? AND customer_type = ? 
              ORDER BY name ASC";
    
    $stmt = $db->prepare($query);
    if (!$stmt) {
        throw new Exception('خطأ في إعداد الاستعلام: ' . $db->error);
    }
    
    $stmt->bind_param("is", $_SESSION['user_id'], $customer_type);
    
    if (!$stmt->execute()) {
        throw new Exception('خطأ في تنفيذ الاستعلام: ' . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $customers = [];
    
    while ($row = $result->fetch_assoc()) {
        $customers[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'phone' => $row['phone'],
            'email' => $row['email']
        ];
    }
    
    $stmt->close();
    $db->close();
    
    // إرسال الاستجابة
    echo json_encode([
        'success' => true,
        'customers' => $customers,
        'count' => count($customers),
        'type' => $customer_type
    ]);
    
} catch (Exception $e) {
    // تسجيل الخطأ
    ErrorHandler::logError('ERROR', 'Get customers by type error: ' . $e->getMessage(), __FILE__, __LINE__, [
        'customer_type' => $customer_type ?? null,
        'user_id' => $_SESSION['user_id'] ?? null
    ]);
    
    // إرسال رسالة خطأ
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'customers' => []
    ]);
}
?>
