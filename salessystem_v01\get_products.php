<?php
require_once 'config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

header('Content-Type: application/json');

try {
    // الحصول على اتصال قاعدة البيانات
    $db = getCurrentUserDB();
    if (!$db) {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في الاتصال بقاعدة البيانات']);
        exit();
    }

    // جلب جميع المنتجات
    $result = $db->query("SELECT id, name, price, tax_rate FROM products WHERE is_active = 1 ORDER BY name");

    if (!$result) {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في استعلام قاعدة البيانات']);
        exit();
    }

    $products = [];
    while ($row = $result->fetch_assoc()) {
        $products[] = $row;
    }

    echo json_encode($products);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في قاعدة البيانات']);
}
?>
