<?php
// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تحديد المسار الصحيح لملف التكوين
$config_path = __DIR__ . '/../config/unified_db_config.php';
if (!file_exists($config_path)) {
    $config_path = __DIR__ . '/../config/database.php';
}
if (file_exists($config_path)) {
    require_once $config_path;
}

// معالجة تسجيل الدخول
if (isset($_POST['login'])) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    // الحصول على اتصال قاعدة البيانات الموحدة
    $db = getUnifiedDB();
    if (!$db) {
        ErrorHandler::logDatabaseError('getUnifiedDB() in login', 'Database connection failed');
        $_SESSION['error'] = __('database_connection_failed');
        header("Location: ../login.php");
        exit();
    }

    // التحقق من بيانات المستخدم مع حالة الحساب
    $stmt = $db->prepare("SELECT id, password, full_name, status FROM users WHERE username = ?");
    if (!$stmt) {
        ErrorHandler::logDatabaseError("SELECT user for login", $db->error, ['username' => $username]);
        $_SESSION['error'] = __('database_connection_failed');
        header("Location: ../login.php");
        exit();
    }

    $stmt->bind_param("s", $username);
    if (!$stmt->execute()) {
        ErrorHandler::logDatabaseError("Execute user login query", $stmt->error, ['username' => $username]);
        $_SESSION['error'] = __('database_connection_failed');
        header("Location: ../login.php");
        exit();
    }

    $stmt->store_result();

    if ($stmt->num_rows == 1) {
        $stmt->bind_result($user_id, $hashed_password, $full_name, $user_status);
        $stmt->fetch();

        // التحقق من حالة الحساب أولاً
        if ($user_status !== 'active') {
            // تسجيل محاولة دخول لحساب معطل
            error_log("Login attempt for disabled account: " . $username . " (Status: " . $user_status . ")");
            logActivity('login_attempt_disabled', 'users', $user_id, null, null, 'محاولة دخول لحساب معطل');

            $_SESSION['error'] = 'تم تعطيل حسابك. يرجى التواصل مع الإدارة لإعادة تفعيل الحساب.';
            header("Location: ../login.php");
            exit();
        }

        if (password_verify($password, $hashed_password)) {
            $_SESSION['user_id'] = $user_id;
            $_SESSION['username'] = $username;
            $_SESSION['full_name'] = $full_name;

            // تحديث آخر تسجيل دخول
            $update_stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $update_stmt->bind_param("i", $user_id);
            $update_stmt->execute();
            $update_stmt->close();

            // في النظام الموحد، لا حاجة للتحقق من جداول منفصلة
            // الجداول موجودة بالفعل ونستخدم user_id للفصل

            // تسجيل العملية
            logActivity('user_login', 'users', $user_id, null, null, 'تسجيل دخول المستخدم');

            header("Location: ../index.php");
            exit();
        } else {
            // تسجيل محاولة دخول خاطئة
            error_log("Invalid password attempt for username: " . $username);
        }
    } else {
        // تسجيل محاولة دخول بمستخدم غير موجود
        error_log("Login attempt with non-existent username: " . $username);
    }

    $_SESSION['error'] = __('invalid_credentials');
    header("Location: ../login.php");
    exit();
}

// معالجة التسجيل
if (isset($_POST['register'])) {
    $username = trim($_POST['username']);
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    
    // الحصول على اتصال قاعدة البيانات الموحدة
    $db = getUnifiedDB();
    if (!$db) {
        $_SESSION['error'] = __('database_connection_failed');
        header("Location: ../register.php");
        exit();
    }

    // التحقق من عدم وجود مستخدم بنفس الاسم
    $stmt = $db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param("ss", $username, $email);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        $_SESSION['error'] = __('username_or_email_exists');
        header("Location: ../register.php");
        exit();
    }

    // إضافة المستخدم الجديد
    $stmt = $db->prepare("INSERT INTO users (username, password, full_name, email) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $username, $password, $full_name, $email);
    
    if ($stmt->execute()) {
        $user_id = $stmt->insert_id;

        // في النظام الموحد، لا حاجة لإنشاء جداول منفصلة
        // الجداول موجودة بالفعل ونستخدم user_id للفصل

        // تسجيل العملية
        logActivity('user_register', 'users', $user_id, null, ['username' => $username, 'full_name' => $full_name, 'email' => $email], 'تسجيل مستخدم جديد');

        $_SESSION['success'] = __('register_success');
        header("Location: ../login.php");
        exit();
    } else {
        $_SESSION['error'] = __('register_failed');
        header("Location: ../register.php");
        exit();
    }
}

// دالة للتوافق مع النظام القديم (لا تحتاج في النظام الموحد)
function createUserDatabase($user_id) {
    // في النظام الموحد، لا حاجة لإنشاء جداول منفصلة
    // الجداول موجودة بالفعل ونستخدم user_id للفصل
    return true;
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة إعادة التوجيه إذا لم يكن المستخدم مسجل الدخول
function redirectIfNotLoggedIn() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

// دالة للحصول على معرف المستخدم الحالي
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

// دالة للحصول على اسم المستخدم الحالي
function getCurrentUsername() {
    return $_SESSION['username'] ?? null;
}

// دالة تسجيل الأنشطة (مبسطة)
function logActivity($action, $table, $record_id, $old_data = null, $new_data = null, $description = '') {
    // يمكن تطوير هذه الدالة لاحقاً لحفظ الأنشطة في قاعدة البيانات
    return true;
}

// دالة الترجمة المبسطة
function __($key) {
    $translations = [
        'database_connection_failed' => 'فشل في الاتصال بقاعدة البيانات',
        'invalid_credentials' => 'بيانات الدخول غير صحيحة',
        'username_or_email_exists' => 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل',
        'register_success' => 'تم التسجيل بنجاح',
        'register_failed' => 'فشل في التسجيل'
    ];

    return $translations[$key] ?? $key;
}
?>

