<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()

// فحص تسجيل الدخول قبل إرسال أي محتوى
if (isLoggedIn()) {
    header("Location: index.php");
    exit();
}

require_once __DIR__.'/includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header"><?php echo __('login_title'); ?></div>
            <div class="card-body">
                <?php displayMessages(); ?>
                <form method="POST" action="includes/auth.php">
                    <div class="mb-3">
                        <label for="username" class="form-label"><?php echo __('username'); ?></label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label"><?php echo __('password'); ?></label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" name="login" class="btn btn-primary"><?php echo __('login'); ?></button>
                </form>
                <div class="mt-3 text-center">
                    <a href="forgot_password.php" class="text-decoration-none text-muted">
                        <i class="fas fa-key me-1"></i>نسيت كلمة المرور؟
                    </a>
                </div>
                <div class="mt-2">
                    <?php echo __('dont_have_account'); ?> <a href="register.php"><?php echo __('register_now'); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>