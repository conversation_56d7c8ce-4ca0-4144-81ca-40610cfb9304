<?php
/**
 * معالجة الفواتير السريعة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "طريقة إرسال غير صحيحة";
    header("Location: index.php");
    exit();
}

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    ErrorHandler::logDatabaseError('getCurrentUserDB()', $db ? $db->connect_error : 'Database connection is null');
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات";
    header("Location: index.php");
    exit();
}

try {
    // استلام البيانات
    $invoice_type = $_POST['invoice_type'] ?? '';
    $customer_id = $_POST['customer_id'] ?? '';
    $date = $_POST['date'] ?? date('Y-m-d');
    $notes = $_POST['notes'] ?? '';

    // استلام بيانات الدفع
    $payment_method = $_POST['payment_method'] ?? 'cash';
    $payment_status = $_POST['payment_status'] ?? 'unpaid';
    $paid_amount = floatval($_POST['paid_amount'] ?? 0);
    $payment_date = !empty($_POST['payment_date']) ? $_POST['payment_date'] : null;
    $payment_reference = trim($_POST['payment_reference'] ?? '');
    $payment_notes = trim($_POST['payment_notes'] ?? '');

    // استلام بيانات المنتجات
    $product_ids = $_POST['product_id'] ?? [];
    $quantities = $_POST['quantity'] ?? [];
    $prices = $_POST['price'] ?? [];
    $tax_rates = $_POST['tax_rate'] ?? [];

    // التحقق من البيانات الأساسية
    if (empty($invoice_type) || !in_array($invoice_type, ['sale', 'purchase'])) {
        throw new Exception("نوع الفاتورة غير صحيح");
    }

    if (empty($customer_id)) {
        $entity_name = $invoice_type === 'sale' ? 'العميل' : 'المورد';
        throw new Exception("يجب اختيار $entity_name");
    }

    // التحقق من نوع العميل/المورد المطلوب
    $expected_customer_type = $invoice_type === 'sale' ? 'customer' : 'supplier';
    $username = $_SESSION['username'];
    $customers_table = getUserTableName('customers', $username);

    $customer_check_stmt = $db->prepare("SELECT customer_type FROM `$customers_table` WHERE id = ? AND user_id = ?");
    $customer_check_stmt->bind_param("ii", $customer_id, $_SESSION['user_id']);
    $customer_check_stmt->execute();
    $customer_check_result = $customer_check_stmt->get_result();

    if ($customer_check_result->num_rows === 0) {
        throw new Exception("العميل/المورد المحدد غير موجود");
    }

    $customer_data = $customer_check_result->fetch_assoc();
    if ($customer_data['customer_type'] !== $expected_customer_type) {
        $error_msg = $invoice_type === 'sale' ?
            'لا يمكن استخدام مورد في فاتورة مبيعات. يرجى اختيار عميل.' :
            'لا يمكن استخدام عميل في فاتورة مشتريات. يرجى اختيار مورد.';
        throw new Exception($error_msg);
    }
    $customer_check_stmt->close();

    if (empty($product_ids) || !is_array($product_ids)) {
        throw new Exception("يجب إضافة عنصر واحد على الأقل");
    }

    // التحقق من صحة الأصناف
    $valid_items = [];
    $subtotal = 0;

    for ($i = 0; $i < count($product_ids); $i++) {
        $product_id = intval($product_ids[$i] ?? 0);
        $quantity = floatval($quantities[$i] ?? 0);
        $price = floatval($prices[$i] ?? 0);
        $tax_rate = floatval($tax_rates[$i] ?? 15);

        if ($product_id <= 0 || $quantity <= 0 || $price < 0) {
            continue; // تجاهل الأصناف غير الصحيحة
        }

        // جلب بيانات المنتج من قاعدة البيانات المشتركة (بدون فلترة user_id)
        // التحقق من وجود عمود is_active
        $columns_check = $db->query("SHOW COLUMNS FROM products LIKE 'is_active'");
        $has_is_active = $columns_check && $columns_check->num_rows > 0;

        if ($has_is_active) {
            $product_stmt = $db->prepare("SELECT name, category FROM products WHERE id = ? AND is_active = 1");
        } else {
            $product_stmt = $db->prepare("SELECT name, category FROM products WHERE id = ?");
        }

        $product_stmt->bind_param("i", $product_id);
        $product_stmt->execute();
        $product_result = $product_stmt->get_result();

        if ($product_result->num_rows === 0) {
            continue; // تجاهل المنتجات غير الموجودة أو غير النشطة
        }

        $product_data = $product_result->fetch_assoc();
        $product_name = $product_data['name'];
        $product_category = $product_data['category'];
        $product_stmt->close();

        $item_total = $quantity * $price;
        $subtotal += $item_total;

        $valid_items[] = [
            'product_id' => $product_id,
            'product_name' => $product_name,
            'product_category' => $product_category,
            'quantity' => $quantity,
            'price' => $price,
            'tax_rate' => $tax_rate,
            'total' => $item_total
        ];
    }

    if (empty($valid_items)) {
        throw new Exception("لا توجد عناصر صحيحة");
    }

    // حساب الإجماليات (الضريبة محسوبة لكل عنصر على حدة)
    $tax_amount = 0;
    foreach ($valid_items as $item) {
        $item_subtotal = $item['quantity'] * $item['price'];
        $item_tax = $item_subtotal * ($item['tax_rate'] / 100);
        $tax_amount += $item_tax;
    }

    $total_amount = $subtotal + $tax_amount;

    // حساب المبلغ المتبقي
    $remaining_amount = $total_amount - $paid_amount;

    // توليد رقم فاتورة
    $prefix = $invoice_type === 'sale' ? 'S' : 'P';
    $invoice_number = $prefix . date('YmdHis') . rand(100, 999);

    // بدء المعاملة
    $db->begin_transaction();

    // تحديد الجدول المناسب مع البادئة
    $username = $_SESSION['username'];
    $user_id = $_SESSION['user_id'];
    $table = $invoice_type === 'sale' ? getUserTableName('sales', $username) : getUserTableName('purchases', $username);
    $items_table = $invoice_type === 'sale' ? getUserTableName('sale_items', $username) : getUserTableName('purchase_items', $username);

    // إدراج الفاتورة الرئيسية مع user_id وبيانات الدفع
    $stmt = $db->prepare("INSERT INTO `$table` (user_id, customer_id, invoice_number, date, subtotal, tax_amount, total_amount, payment_method, payment_status, paid_amount, remaining_amount, payment_date, payment_reference, payment_notes, notes, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");

    if (!$stmt) {
        ErrorHandler::logDatabaseError("INSERT INTO $table", $db->error, [
            'invoice_type' => $invoice_type,
            'customer_id' => $customer_id,
            'invoice_number' => $invoice_number
        ]);
        throw new Exception("خطأ في إعداد استعلام الفاتورة: " . $db->error);
    }

    $stmt->bind_param("iissdddssddssss", $user_id, $customer_id, $invoice_number, $date, $subtotal, $tax_amount, $total_amount, $payment_method, $payment_status, $paid_amount, $remaining_amount, $payment_date, $payment_reference, $payment_notes, $notes);

    if (!$stmt->execute()) {
        ErrorHandler::logDatabaseError("INSERT INTO $table", $stmt->error, [
            'invoice_type' => $invoice_type,
            'customer_id' => $customer_id,
            'invoice_number' => $invoice_number
        ]);
        throw new Exception("خطأ في حفظ الفاتورة: " . $stmt->error);
    }

    $invoice_id = $db->insert_id;
    $stmt->close();

    // إدراج أصناف الفاتورة مع user_id
    if ($invoice_type === 'sale') {
        $item_stmt = $db->prepare("INSERT INTO `$items_table` (user_id, sale_id, product_id, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    } else {
        $item_stmt = $db->prepare("INSERT INTO `$items_table` (user_id, purchase_id, product_id, product_name, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    }

    if (!$item_stmt) {
        throw new Exception("خطأ في إعداد استعلام الأصناف: " . $db->error);
    }

    foreach ($valid_items as $item) {
        if ($invoice_type === 'sale') {
            $item_tax_amount = ($item['quantity'] * $item['price']) * ($item['tax_rate'] / 100);
            $item_total_price = ($item['quantity'] * $item['price']) + $item_tax_amount;

            $item_stmt->bind_param("iiiidddd", $user_id, $invoice_id, $item['product_id'], $item['quantity'], $item['price'], $item['tax_rate'], $item_tax_amount, $item_total_price);
        } else {
            $item_tax_amount = ($item['quantity'] * $item['price']) * ($item['tax_rate'] / 100);
            $item_total_price = ($item['quantity'] * $item['price']) + $item_tax_amount;

            $item_stmt->bind_param("iiisidddd", $user_id, $invoice_id, $item['product_id'], $item['product_name'], $item['quantity'], $item['price'], $item['tax_rate'], $item_tax_amount, $item_total_price);
        }

        if (!$item_stmt->execute()) {
            throw new Exception("خطأ في حفظ العنصر: " . $item_stmt->error);
        }
    }

    $item_stmt->close();

    // تأكيد المعاملة
    $db->commit();

    // رسالة نجاح
    $invoice_type_ar = $invoice_type === 'sale' ? 'المبيعات' : 'المشتريات';
    $_SESSION['success'] = "تم حفظ فاتورة $invoice_type_ar بنجاح. رقم الفاتورة: $invoice_number";

    // إعادة التوجيه للصفحة الرئيسية مع معاملات طباعة POS
    $redirect_url = "index.php?auto_print_pos=1&invoice_id=$invoice_id&invoice_type=$invoice_type";
    header("Location: $redirect_url");
    exit();

} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if (isset($db) && $db->connect_error === null) {
        $db->rollback();
    }

    ErrorHandler::logError('ERROR', 'Quick invoice processing error: ' . $e->getMessage(), __FILE__, __LINE__, [
        'invoice_type' => $invoice_type ?? null,
        'customer_id' => $customer_id ?? null,
        'invoice_number' => $invoice_number ?? null,
        'user_id' => $_SESSION['user_id'] ?? null,
        'items_count' => count($valid_items ?? [])
    ]);

    $_SESSION['error'] = "خطأ في حفظ الفاتورة: " . $e->getMessage();
    header("Location: index.php");
    exit();

} finally {
    // إغلاق الاتصال
    if (isset($db)) {
        $db->close();
    }
}
?>
