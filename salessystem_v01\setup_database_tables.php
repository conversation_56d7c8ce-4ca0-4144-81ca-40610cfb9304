<?php
/**
 * إعداد جداول قاعدة البيانات وإدراج البيانات الأولية
 */

require_once __DIR__ . '/config/init.php';

// بدء الجلسة
session_start();

// التحقق من الصلاحيات (يجب أن يكون المستخدم مسجل دخول)
if (!isset($_SESSION['user_id'])) {
    echo "<div style='color: red; font-weight: bold;'>يجب تسجيل الدخول أولاً لتشغيل هذا الملف</div>";
    echo "<a href='login.php'>تسجيل الدخول</a>";
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .log-entry { 
            padding: 8px 12px; 
            margin: 5px 0; 
            border-radius: 4px; 
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .log-success { border-left-color: #28a745; background: #d4edda; }
        .log-error { border-left-color: #dc3545; background: #f8d7da; }
        .log-warning { border-left-color: #ffc107; background: #fff3cd; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-database me-2"></i>إعداد قاعدة البيانات</h4>
                    </div>
                    <div class="card-body">
                        <?php
                        echo "<div class='log-entry'><strong>بدء عملية إعداد قاعدة البيانات...</strong></div>";
                        
                        // اختبار الاتصال
                        $db = getUnifiedDB();
                        if (!$db) {
                            echo "<div class='log-entry log-error'><i class='fas fa-times me-2'></i>فشل في الاتصال بقاعدة البيانات</div>";
                            exit;
                        }
                        echo "<div class='log-entry log-success'><i class='fas fa-check me-2'></i>تم الاتصال بقاعدة البيانات بنجاح</div>";

                        // إنشاء الجداول
                        echo "<div class='log-entry'><strong>إنشاء الجداول...</strong></div>";

                        $tables_result = createMissingTables();
                        if ($tables_result['success']) {
                            echo "<div class='log-entry log-success'><i class='fas fa-check me-2'></i>تم إنشاء جميع الجداول بنجاح</div>";
                            if (!empty($tables_result['created_tables'])) {
                                echo "<div class='log-entry log-success'>الجداول المُنشأة: " . implode(', ', $tables_result['created_tables']) . "</div>";
                            }
                        } else {
                            echo "<div class='log-entry log-error'><i class='fas fa-times me-2'></i>فشل في إنشاء بعض الجداول</div>";
                            foreach ($tables_result['errors'] as $error) {
                                echo "<div class='log-entry log-error'>$error</div>";
                            }
                        }

                        // فحص الجداول المطلوبة
                        $required_tables = [
                            'users', 'admins', 'activity_log', 'system_settings', 
                            'product_categories', 'customers', 'suppliers', 'products', 
                            'sales', 'purchases', 'sale_items', 'purchase_items', 
                            'payments', 'budgets'
                        ];

                        echo "<div class='log-entry'><strong>فحص الجداول المطلوبة...</strong></div>";
                        
                        $missing_tables = [];
                        foreach ($required_tables as $table) {
                            $check_result = $db->query("SHOW TABLES LIKE '$table'");
                            if ($check_result && $check_result->num_rows > 0) {
                                echo "<div class='log-entry log-success'><i class='fas fa-check me-2'></i>جدول $table موجود</div>";
                            } else {
                                echo "<div class='log-entry log-error'><i class='fas fa-times me-2'></i>جدول $table غير موجود</div>";
                                $missing_tables[] = $table;
                            }
                        }

                        if (empty($missing_tables)) {
                            echo "<div class='log-entry log-success'><strong><i class='fas fa-check-circle me-2'></i>جميع الجداول المطلوبة موجودة!</strong></div>";
                        } else {
                            echo "<div class='log-entry log-warning'><strong><i class='fas fa-exclamation-triangle me-2'></i>الجداول المفقودة: " . implode(', ', $missing_tables) . "</strong></div>";
                        }

                        // فحص البيانات الأولية
                        echo "<div class='log-entry'><strong>فحص البيانات الأولية...</strong></div>";

                        // فحص إعدادات النظام
                        $settings_result = $db->query("SELECT COUNT(*) as count FROM system_settings");
                        if ($settings_result) {
                            $settings_count = $settings_result->fetch_assoc()['count'];
                            if ($settings_count > 0) {
                                echo "<div class='log-entry log-success'><i class='fas fa-check me-2'></i>إعدادات النظام موجودة ($settings_count إعداد)</div>";
                            } else {
                                echo "<div class='log-entry log-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد إعدادات نظام</div>";
                            }
                        }

                        // فحص فئات المنتجات
                        $categories_result = $db->query("SELECT COUNT(*) as count FROM product_categories");
                        if ($categories_result) {
                            $categories_count = $categories_result->fetch_assoc()['count'];
                            if ($categories_count > 0) {
                                echo "<div class='log-entry log-success'><i class='fas fa-check me-2'></i>فئات المنتجات موجودة ($categories_count فئة)</div>";
                            } else {
                                echo "<div class='log-entry log-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد فئات منتجات</div>";
                            }
                        }

                        // إحصائيات الجداول
                        echo "<div class='log-entry'><strong>إحصائيات الجداول:</strong></div>";
                        
                        $stats_tables = ['users', 'customers', 'suppliers', 'products', 'sales', 'purchases'];
                        foreach ($stats_tables as $table) {
                            $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
                            if ($count_result) {
                                $count = $count_result->fetch_assoc()['count'];
                                echo "<div class='log-entry'><i class='fas fa-table me-2'></i>جدول $table: $count سجل</div>";
                            }
                        }

                        // فحص وإنشاء المدير الرئيسي
                        echo "<div class='log-entry'><strong>فحص المدير الرئيسي...</strong></div>";

                        try {
                            $super_admin_result = ensureSuperAdminExists();

                            if ($super_admin_result['exists']) {
                                echo "<div class='log-entry log-success'><i class='fas fa-check me-2'></i>المدير الرئيسي موجود: " . $super_admin_result['username'] . " (" . $super_admin_result['email'] . ")</div>";
                            } elseif ($super_admin_result['created']) {
                                echo "<div class='log-entry log-success'><i class='fas fa-plus me-2'></i>تم إنشاء المدير الرئيسي بنجاح!</div>";
                                echo "<div class='log-entry log-warning'><i class='fas fa-key me-2'></i><strong>بيانات تسجيل الدخول:</strong></div>";
                                echo "<div class='log-entry log-warning'>اسم المستخدم: " . $super_admin_result['username'] . "</div>";
                                echo "<div class='log-entry log-warning'>البريد الإلكتروني: " . $super_admin_result['email'] . "</div>";
                                echo "<div class='log-entry log-warning'>كلمة المرور: " . $super_admin_result['default_password'] . "</div>";
                                echo "<div class='log-entry log-error'><i class='fas fa-exclamation-triangle me-2'></i><strong>مهم: يُرجى تغيير كلمة المرور فور تسجيل الدخول!</strong></div>";
                            } else {
                                echo "<div class='log-entry log-error'><i class='fas fa-times me-2'></i>فشل في إنشاء المدير الرئيسي: " . ($super_admin_result['error'] ?? 'خطأ غير معروف') . "</div>";
                            }
                        } catch (Exception $e) {
                            echo "<div class='log-entry log-error'><i class='fas fa-times me-2'></i>خطأ في فحص المدير الرئيسي: " . $e->getMessage() . "</div>";
                        }

                        // اختبار دالة getInvoiceSetting
                        echo "<div class='log-entry'><strong>اختبار دوال النظام...</strong></div>";

                        try {
                            $company_name = getInvoiceSetting('company_name', 'نظام المبيعات والمشتريات');
                            echo "<div class='log-entry log-success'><i class='fas fa-check me-2'></i>دالة getInvoiceSetting تعمل بنجاح: $company_name</div>";
                        } catch (Exception $e) {
                            echo "<div class='log-entry log-error'><i class='fas fa-times me-2'></i>خطأ في دالة getInvoiceSetting: " . $e->getMessage() . "</div>";
                        }

                        echo "<div class='log-entry log-success'><strong><i class='fas fa-check-circle me-2'></i>تم الانتهاء من إعداد قاعدة البيانات!</strong></div>";
                        ?>

                        <div class="mt-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="index.php" class="btn btn-primary w-100">
                                        <i class="fas fa-home me-2"></i>
                                        العودة للصفحة الرئيسية
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <button onclick="location.reload()" class="btn btn-secondary w-100">
                                        <i class="fas fa-refresh me-2"></i>
                                        إعادة تشغيل الفحص
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                                <ul class="mb-0">
                                    <li>تم إنشاء جميع الجداول المطلوبة للنظام</li>
                                    <li>تم إدراج البيانات الأولية (إعدادات النظام وفئات المنتجات)</li>
                                    <li>يمكنك الآن استخدام النظام بشكل طبيعي</li>
                                    <li>في حالة وجود أخطاء، تحقق من ملف الأخطاء: logs/error_<?php echo date('Y-m-d'); ?>.log</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
