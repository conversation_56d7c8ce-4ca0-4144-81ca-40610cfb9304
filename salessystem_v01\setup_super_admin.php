<?php
/**
 * إعداد المدير الرئيسي وحمايته
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>👑 إعداد المدير الرئيسي</h2>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    $results = [];
    $errors = [];

    // التحقق من وجود عمود is_super_admin
    $columns_query = $db->query("SHOW COLUMNS FROM admins LIKE 'is_super_admin'");
    if ($columns_query->num_rows == 0) {
        // إضافة عمود is_super_admin
        $add_column = $db->query("ALTER TABLE admins ADD COLUMN is_super_admin BOOLEAN DEFAULT FALSE AFTER status");
        if ($add_column) {
            $results[] = "✅ تم إضافة عمود is_super_admin";
        } else {
            $errors[] = "❌ فشل في إضافة عمود is_super_admin: " . $db->error;
        }
    } else {
        $results[] = "✅ عمود is_super_admin موجود بالفعل";
    }

    // جلب جميع المديرين
    $admins_query = $db->query("SELECT id, username, full_name, is_super_admin FROM admins ORDER BY id");
    if (!$admins_query) {
        throw new Exception("فشل في جلب بيانات المديرين: " . $db->error);
    }

    echo "<div style='background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>📋 المديرين الموجودين:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>مدير رئيسي</th><th>الإجراء</th></tr>";

    $super_admin_found = false;
    while ($admin = $admins_query->fetch_assoc()) {
        $is_super = $admin['is_super_admin'] ?? false;
        
        echo "<tr>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name'] ?? 'غير محدد') . "</td>";
        echo "<td>";
        
        if ($is_super) {
            echo "<span style='color: gold;'>👑 نعم</span>";
            $super_admin_found = true;
        } else {
            echo "<span style='color: gray;'>لا</span>";
        }
        
        echo "</td>";
        echo "<td>";
        
        // تحديد المدير الرئيسي بناءً على معايير
        if (!$is_super && ($admin['username'] == 'admin' || $admin['id'] == 1)) {
            $update_stmt = $db->prepare("UPDATE admins SET is_super_admin = TRUE WHERE id = ?");
            $update_stmt->bind_param("i", $admin['id']);
            if ($update_stmt->execute()) {
                echo "<span style='color: green;'>تم تعيينه كمدير رئيسي</span>";
                $results[] = "تم تعيين " . $admin['username'] . " كمدير رئيسي";
                $super_admin_found = true;
            } else {
                echo "<span style='color: red;'>فشل التحديث</span>";
                $errors[] = "فشل في تعيين " . $admin['username'] . " كمدير رئيسي";
            }
            $update_stmt->close();
        } elseif ($is_super) {
            echo "<span style='color: blue;'>مدير رئيسي حالي</span>";
        } else {
            echo "<span style='color: gray;'>مدير عادي</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }

    echo "</table>";
    echo "</div>";

    // إذا لم يوجد مدير رئيسي، تعيين الأول
    if (!$super_admin_found) {
        $first_admin = $db->query("SELECT id, username FROM admins ORDER BY id LIMIT 1");
        if ($first_admin && $first_admin->num_rows > 0) {
            $admin_data = $first_admin->fetch_assoc();
            $update_stmt = $db->prepare("UPDATE admins SET is_super_admin = TRUE WHERE id = ?");
            $update_stmt->bind_param("i", $admin_data['id']);
            if ($update_stmt->execute()) {
                $results[] = "تم تعيين " . $admin_data['username'] . " كمدير رئيسي افتراضي";
            }
            $update_stmt->close();
        }
    }

    // عرض النتائج
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>✅ النتائج:</h4>";
    foreach ($results as $result) {
        echo "<p>✅ $result</p>";
    }
    echo "</div>";

    if (!empty($errors)) {
        echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>❌ الأخطاء:</h4>";
        foreach ($errors as $error) {
            echo "<p>❌ $error</p>";
        }
        echo "</div>";
    }

    // اختبار الحماية
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🧪 اختبار الحماية:</h4>";
    
    // جلب المدير الرئيسي
    $super_admin_query = $db->query("SELECT id, username, full_name FROM admins WHERE is_super_admin = TRUE LIMIT 1");
    if ($super_admin_query && $super_admin_query->num_rows > 0) {
        $super_admin = $super_admin_query->fetch_assoc();
        echo "<p><strong>المدير الرئيسي:</strong> " . htmlspecialchars($super_admin['full_name']) . " (" . htmlspecialchars($super_admin['username']) . ")</p>";
        
        // اختبار الدوال
        echo "<p><strong>اختبار isSuperAdmin(" . $super_admin['id'] . "):</strong> " . (isSuperAdmin($super_admin['id']) ? '✅ صحيح' : '❌ خطأ') . "</p>";
        
        // اختبار canManageAdmin
        echo "<p><strong>اختبار canManageAdmin(" . $super_admin['id'] . "):</strong> ";
        if (isAdminLoggedIn()) {
            echo (canManageAdmin($super_admin['id']) ? '✅ يمكن التحكم' : '❌ محمي');
        } else {
            echo "غير مسجل دخول";
        }
        echo "</p>";
    } else {
        echo "<p style='color: red;'>⚠️ لم يتم العثور على مدير رئيسي!</p>";
    }
    echo "</div>";

    // معلومات الحماية
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🔒 معلومات الحماية:</h4>";
    echo "<ul>";
    echo "<li><strong>المدير الرئيسي محمي من:</strong> الحذف، تغيير الحالة، تعديل الصلاحيات (إلا من قبل مدير رئيسي آخر)</li>";
    echo "<li><strong>فقط المدير الرئيسي يمكنه:</strong> التحكم في المديرين الرئيسيين الآخرين</li>";
    echo "<li><strong>المديرين العاديين يمكنهم:</strong> التحكم في المديرين العاديين فقط</li>";
    echo "<li><strong>لا يمكن لأي مدير:</strong> التحكم في نفسه (حذف أو تعطيل)</li>";
    echo "</ul>";
    echo "</div>";

    // روابط الاختبار
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='admin_manage_admins.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>اختبار إدارة المديرين</a>";
    echo "<a href='test_permissions.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>اختبار الصلاحيات</a>";
    echo "<a href='admin_dashboard.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>لوحة التحكم</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
