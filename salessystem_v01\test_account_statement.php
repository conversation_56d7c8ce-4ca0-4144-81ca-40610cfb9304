<?php
/**
 * اختبار كشف الحساب المحسن
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>🧪 اختبار كشف الحساب المحسن</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f8f9fa; }
    .sale-row { background-color: #d4edda; }
    .purchase-row { background-color: #fff3cd; }
</style>";

echo "<div class='section'>";
echo "<h2>1. اختبار البيانات الأساسية</h2>";

try {
    $db = getCurrentUserDB();
    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }
    
    $username = $_SESSION['username'] ?? 'testuser';
    $user_id = $_SESSION['user_id'] ?? 1;
    
    // الحصول على أسماء الجداول
    $sales_table = getUserTableName('sales', $username);
    $purchases_table = getUserTableName('purchases', $username);
    $customers_table = getUserTableName('customers', $username);
    
    echo "<p class='info'>المستخدم: $username (ID: $user_id)</p>";
    echo "<p class='info'>جدول المبيعات: $sales_table</p>";
    echo "<p class='info'>جدول المشتريات: $purchases_table</p>";
    
    // فحص البيانات
    $sales_count = $db->query("SELECT COUNT(*) as count FROM `$sales_table` WHERE user_id = $user_id")->fetch_assoc()['count'];
    $purchases_count = $db->query("SELECT COUNT(*) as count FROM `$purchases_table` WHERE user_id = $user_id")->fetch_assoc()['count'];
    
    echo "<p class='success'>✅ عدد المبيعات: $sales_count</p>";
    echo "<p class='success'>✅ عدد المشتريات: $purchases_count</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. اختبار استعلامات كشف الحساب</h2>";

try {
    // محاكاة كود كشف الحساب
    $start_date = date('Y-m-01'); // أول يوم في الشهر
    $end_date = date('Y-m-d'); // اليوم الحالي
    $account_transactions = [];
    
    echo "<p class='info'>الفترة: $start_date إلى $end_date</p>";
    
    // اختبار استعلام المبيعات المحسن
    $sales_query = "SELECT
                    s.id,
                    s.invoice_number,
                    s.date,
                    s.total_amount,
                    COALESCE(s.subtotal, s.total_amount) as subtotal,
                    COALESCE(s.tax_amount, 0) as tax_amount,
                    s.payment_status,
                    COALESCE(c.name, 'عميل غير محدد') as customer_name,
                    'sale' as transaction_type,
                    'مبيعات' as transaction_type_ar,
                    'sales' as source_table,
                    'SALE' as type_verification
                    FROM `$sales_table` s
                    LEFT JOIN `$customers_table` c ON s.customer_id = c.id AND c.user_id = s.user_id
                    WHERE s.date BETWEEN '$start_date' AND '$end_date' AND s.user_id = $user_id
                    ORDER BY s.date ASC, s.id ASC";
    
    $sales_result = $db->query($sales_query);
    if ($sales_result) {
        $sales_found = 0;
        while ($row = $sales_result->fetch_assoc()) {
            // التأكد المطلق من نوع المعاملة
            $row['transaction_type'] = 'sale';
            $row['transaction_type_ar'] = 'مبيعات';
            $row['source_table'] = 'sales';
            $row['type_verification'] = 'SALE';
            $row['is_sale'] = true;
            $row['is_purchase'] = false;
            $account_transactions[] = $row;
            $sales_found++;
        }
        echo "<p class='success'>✅ تم جلب $sales_found مبيعة</p>";
    } else {
        echo "<p class='error'>❌ فشل في جلب المبيعات: " . $db->error . "</p>";
    }
    
    // اختبار استعلام المشتريات المحسن
    $check_purchases_table = $db->query("SHOW TABLES LIKE '$purchases_table'");
    if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
        $purchases_query = "SELECT
                           p.id,
                           p.invoice_number,
                           p.date,
                           p.total_amount,
                           COALESCE(p.subtotal, p.total_amount) as subtotal,
                           COALESCE(p.tax_amount, 0) as tax_amount,
                           p.payment_status,
                           COALESCE(c.name, p.supplier_name, 'مورد غير محدد') as customer_name,
                           'purchase' as transaction_type,
                           'مشتريات' as transaction_type_ar,
                           'purchases' as source_table,
                           'PURCHASE' as type_verification
                           FROM `$purchases_table` p
                           LEFT JOIN `$customers_table` c ON p.customer_id = c.id AND c.user_id = p.user_id
                           WHERE p.date BETWEEN '$start_date' AND '$end_date' AND p.user_id = $user_id
                           ORDER BY p.date ASC, p.id ASC";
        
        $purchases_result = $db->query($purchases_query);
        if ($purchases_result) {
            $purchases_found = 0;
            while ($row = $purchases_result->fetch_assoc()) {
                // التأكد المطلق من نوع المعاملة
                $row['transaction_type'] = 'purchase';
                $row['transaction_type_ar'] = 'مشتريات';
                $row['source_table'] = 'purchases';
                $row['type_verification'] = 'PURCHASE';
                $row['is_sale'] = false;
                $row['is_purchase'] = true;
                $account_transactions[] = $row;
                $purchases_found++;
            }
            echo "<p class='success'>✅ تم جلب $purchases_found مشترية</p>";
        } else {
            echo "<p class='error'>❌ فشل في جلب المشتريات: " . $db->error . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ جدول المشتريات غير موجود</p>";
    }
    
    echo "<p class='info'>إجمالي المعاملات: " . count($account_transactions) . "</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في الاستعلامات: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. اختبار معالجة البيانات</h2>";

if (!empty($account_transactions)) {
    // ترتيب المعاملات
    usort($account_transactions, function($a, $b) {
        $date_compare = strtotime($a['date']) - strtotime($b['date']);
        if ($date_compare == 0) {
            return $a['id'] - $b['id'];
        }
        return $date_compare;
    });
    
    // حساب الرصيد التراكمي
    $running_balance = 0;
    $sales_total = 0;
    $purchases_total = 0;
    $verification_errors = 0;
    
    foreach ($account_transactions as &$transaction) {
        // التحقق المتعدد من نوع المعاملة
        $is_sale = (
            $transaction['transaction_type'] === 'sale' ||
            $transaction['type_verification'] === 'SALE' ||
            $transaction['source_table'] === 'sales' ||
            isset($transaction['is_sale']) && $transaction['is_sale'] === true
        );
        
        $is_purchase = (
            $transaction['transaction_type'] === 'purchase' ||
            $transaction['type_verification'] === 'PURCHASE' ||
            $transaction['source_table'] === 'purchases' ||
            isset($transaction['is_purchase']) && $transaction['is_purchase'] === true
        );

        // تسجيل أي تضارب
        if ($is_sale && $is_purchase) {
            $verification_errors++;
            error_log("Transaction type conflict for ID: " . $transaction['id']);
        }
        
        if (!$is_sale && !$is_purchase) {
            $verification_errors++;
            error_log("Unknown transaction type for ID: " . $transaction['id']);
        }

        // تطبيق التغيير على الرصيد
        if ($is_sale && !$is_purchase) {
            $running_balance += floatval($transaction['total_amount']);
            $sales_total += floatval($transaction['total_amount']);
            $transaction['balance_change'] = '+' . number_format($transaction['total_amount'], 2);
        } elseif ($is_purchase && !$is_sale) {
            $running_balance -= floatval($transaction['total_amount']);
            $purchases_total += floatval($transaction['total_amount']);
            $transaction['balance_change'] = '-' . number_format($transaction['total_amount'], 2);
        }

        $transaction['running_balance'] = $running_balance;
        $transaction['verified_is_sale'] = $is_sale && !$is_purchase;
        $transaction['verified_is_purchase'] = $is_purchase && !$is_sale;
    }
    
    echo "<p class='success'>✅ تم معالجة " . count($account_transactions) . " معاملة</p>";
    echo "<p class='info'>إجمالي المبيعات: " . number_format($sales_total, 2) . " ر.س</p>";
    echo "<p class='info'>إجمالي المشتريات: " . number_format($purchases_total, 2) . " ر.س</p>";
    echo "<p class='info'>صافي الرصيد: " . number_format($running_balance, 2) . " ر.س</p>";
    
    if ($verification_errors > 0) {
        echo "<p class='error'>❌ أخطاء في التحقق: $verification_errors</p>";
    } else {
        echo "<p class='success'>✅ لا توجد أخطاء في التحقق</p>";
    }
    
} else {
    echo "<p class='warning'>⚠️ لا توجد معاملات للاختبار</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. عرض عينة من النتائج</h2>";

if (!empty($account_transactions)) {
    echo "<table>";
    echo "<tr><th>التاريخ</th><th>رقم الفاتورة</th><th>النوع</th><th>العميل/المورد</th><th>المبلغ</th><th>الرصيد</th><th>التحقق</th></tr>";
    
    $displayed = 0;
    foreach ($account_transactions as $transaction) {
        if ($displayed >= 10) break; // عرض أول 10 معاملات فقط
        
        $row_class = $transaction['verified_is_sale'] ? 'sale-row' : ($transaction['verified_is_purchase'] ? 'purchase-row' : '');
        
        echo "<tr class='$row_class'>";
        echo "<td>" . date('d/m/Y', strtotime($transaction['date'])) . "</td>";
        echo "<td>" . htmlspecialchars($transaction['invoice_number']) . "</td>";
        echo "<td>" . htmlspecialchars($transaction['transaction_type_ar']) . "</td>";
        echo "<td>" . htmlspecialchars($transaction['customer_name']) . "</td>";
        echo "<td>" . number_format($transaction['total_amount'], 2) . "</td>";
        echo "<td>" . number_format($transaction['running_balance'], 2) . "</td>";
        echo "<td>";
        if ($transaction['verified_is_sale']) {
            echo "<span class='success'>✅ مبيعات</span>";
        } elseif ($transaction['verified_is_purchase']) {
            echo "<span class='warning'>✅ مشتريات</span>";
        } else {
            echo "<span class='error'>❌ غير محدد</span>";
        }
        echo "</td>";
        echo "</tr>";
        
        $displayed++;
    }
    
    echo "</table>";
    
    if (count($account_transactions) > 10) {
        echo "<p class='info'>تم عرض أول 10 معاملات من أصل " . count($account_transactions) . " معاملة</p>";
    }
    
} else {
    echo "<p class='warning'>⚠️ لا توجد معاملات لعرضها</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. الخطوات التالية</h2>";

echo "<h3>اختبار كشف الحساب الفعلي:</h3>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب</a></li>";
echo "<li><a href='reports.php?report_type=account_statement&start_date=" . date('Y-m-01') . "&end_date=" . date('Y-m-d') . "' target='_blank'>كشف الحساب للشهر الحالي</a></li>";
echo "</ul>";

echo "<h3>أدوات أخرى:</h3>";
echo "<ul>";
echo "<li><a href='fix_account_statement_confusion.php'>تشخيص المشكلة</a></li>";
echo "<li><a href='final_system_status.php'>تقرير حالة النظام</a></li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الاختبار في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
