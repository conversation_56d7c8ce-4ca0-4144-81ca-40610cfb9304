<?php
/**
 * اختبار الرسوم البيانية
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

echo "<h2>🧪 اختبار الرسوم البيانية</h2>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // بيانات تجريبية للاختبار
    $test_sales = 150;
    $test_purchases = 120;
    $test_customers = 85;
    
    $test_today_activities = 25;
    $test_week_activities = 180;
    $test_total_activities = 1250;

    echo "<div style='background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>📊 البيانات التجريبية:</h4>";
    echo "<p><strong>المبيعات:</strong> $test_sales</p>";
    echo "<p><strong>المشتريات:</strong> $test_purchases</p>";
    echo "<p><strong>العملاء:</strong> $test_customers</p>";
    echo "<p><strong>نشاط اليوم:</strong> $test_today_activities</p>";
    echo "<p><strong>نشاط الأسبوع:</strong> $test_week_activities</p>";
    echo "<p><strong>إجمالي النشاط:</strong> $test_total_activities</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الرسوم البيانية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-6">
                <div class="test-card">
                    <h5>📊 رسم بياني للنشاط</h5>
                    <div class="chart-container">
                        <canvas id="testActivityChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="test-card">
                    <h5>📈 رسم بياني للبيانات</h5>
                    <div class="chart-container">
                        <canvas id="testDataChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h5>🔍 معلومات التشخيص</h5>
            <div id="diagnostics"></div>
        </div>
        
        <div class="text-center mt-4">
            <a href="admin_reports.php" class="btn btn-primary">العودة للتقارير</a>
            <a href="admin_dashboard.php" class="btn btn-secondary">لوحة التحكم</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const diagnostics = document.getElementById('diagnostics');
            let diagnosticInfo = [];
            
            // التحقق من تحميل Chart.js
            if (typeof Chart !== 'undefined') {
                diagnosticInfo.push('✅ Chart.js محمل بنجاح');
            } else {
                diagnosticInfo.push('❌ Chart.js غير محمل');
                return;
            }
            
            // اختبار الرسم البياني للنشاط
            const activityElement = document.getElementById('testActivityChart');
            if (activityElement) {
                diagnosticInfo.push('✅ عنصر testActivityChart موجود');
                
                try {
                    const activityCtx = activityElement.getContext('2d');
                    const activityChart = new Chart(activityCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['نشاط اليوم', 'نشاط الأسبوع', 'إجمالي النشاط'],
                            datasets: [{
                                data: [<?php echo $test_today_activities; ?>, <?php echo $test_week_activities; ?>, <?php echo $test_total_activities; ?>],
                                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                    diagnosticInfo.push('✅ رسم بياني النشاط تم إنشاؤه بنجاح');
                } catch (error) {
                    diagnosticInfo.push('❌ خطأ في إنشاء رسم بياني النشاط: ' + error.message);
                }
            } else {
                diagnosticInfo.push('❌ عنصر testActivityChart غير موجود');
            }
            
            // اختبار الرسم البياني للبيانات
            const dataElement = document.getElementById('testDataChart');
            if (dataElement) {
                diagnosticInfo.push('✅ عنصر testDataChart موجود');
                
                try {
                    const dataCtx = dataElement.getContext('2d');
                    const dataChart = new Chart(dataCtx, {
                        type: 'bar',
                        data: {
                            labels: ['المبيعات', 'المشتريات', 'العملاء'],
                            datasets: [{
                                label: 'العدد',
                                data: [<?php echo $test_sales; ?>, <?php echo $test_purchases; ?>, <?php echo $test_customers; ?>],
                                backgroundColor: ['#28a745', '#dc3545', '#007bff'],
                                borderColor: ['#1e7e34', '#c82333', '#0056b3'],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top'
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                    diagnosticInfo.push('✅ رسم بياني البيانات تم إنشاؤه بنجاح');
                } catch (error) {
                    diagnosticInfo.push('❌ خطأ في إنشاء رسم بياني البيانات: ' + error.message);
                }
            } else {
                diagnosticInfo.push('❌ عنصر testDataChart غير موجود');
            }
            
            // عرض النتائج
            diagnostics.innerHTML = diagnosticInfo.map(info => '<p>' + info + '</p>').join('');
        });
    </script>
</body>
</html>
