<?php
/**
 * صفحة اختبار الصلاحيات
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

echo "<h2>🔐 اختبار نظام الصلاحيات</h2>";

// عرض معلومات المدير الحالي
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>👤 معلومات المدير الحالي:</h4>";
echo "<p><strong>ID:</strong> " . ($_SESSION['admin_id'] ?? 'غير محدد') . "</p>";
echo "<p><strong>اسم المستخدم:</strong> " . ($_SESSION['admin_username'] ?? 'غير محدد') . "</p>";
echo "<p><strong>الاسم الكامل:</strong> " . ($_SESSION['admin_full_name'] ?? 'غير محدد') . "</p>";
echo "<p><strong>مدير رئيسي:</strong> " . (($_SESSION['admin_is_super'] ?? false) ? 'نعم' : 'لا') . "</p>";
echo "</div>";

// عرض الصلاحيات المخزنة
echo "<div style='background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>📋 الصلاحيات المخزنة في الجلسة:</h4>";
$permissions = $_SESSION['admin_permissions'] ?? [];
if (empty($permissions)) {
    echo "<p style='color: orange;'>⚠️ لا توجد صلاحيات محددة</p>";
} else {
    echo "<pre>" . print_r($permissions, true) . "</pre>";
}
echo "</div>";

// قائمة الصلاحيات للاختبار
$test_permissions = [
    'manage_users' => 'إدارة المستخدمين',
    'view_all_data' => 'عرض جميع البيانات',
    'manage_system' => 'إدارة النظام',
    'view_reports' => 'عرض التقارير',
    'manage_admins' => 'إدارة المديرين',
    'view_system_logs' => 'عرض سجل النظام',
    'export_data' => 'تصدير البيانات',
    'manage_settings' => 'إدارة الإعدادات'
];

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>🧪 اختبار الصلاحيات:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الصلاحية</th><th>الوصف</th><th>النتيجة</th><th>الحالة</th></tr>";

foreach ($test_permissions as $permission => $description) {
    $hasPermission = hasAdminPermission($permission);
    $status = $hasPermission ? '✅ مسموح' : '❌ مرفوض';
    $color = $hasPermission ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td><code>$permission</code></td>";
    echo "<td>$description</td>";
    echo "<td style='color: $color; font-weight: bold;'>$status</td>";
    echo "<td>" . ($hasPermission ? 'يمكن الوصول' : 'لا يمكن الوصول') . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// اختبار الصفحات الإدارية
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>🌐 اختبار الوصول للصفحات:</h4>";

$admin_pages = [
    'admin_dashboard.php' => ['صلاحية' => 'view_dashboard', 'وصف' => 'لوحة التحكم'],
    'admin_users.php' => ['صلاحية' => 'manage_users', 'وصف' => 'إدارة المستخدمين'],
    'admin_activity.php' => ['صلاحية' => 'view_all_data', 'وصف' => 'سجل العمليات'],
    'admin_reports.php' => ['صلاحية' => 'view_reports', 'وصف' => 'التقارير'],
    'admin_system.php' => ['صلاحية' => 'manage_system', 'وصف' => 'إعدادات النظام'],
    'admin_manage_admins.php' => ['صلاحية' => 'manage_admins', 'وصف' => 'إدارة المديرين'],
    'admin_error_logs.php' => ['صلاحية' => 'view_system_logs', 'وصف' => 'سجل الأخطاء']
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الصفحة</th><th>الوصف</th><th>الصلاحية المطلوبة</th><th>الوصول</th><th>الإجراء</th></tr>";

foreach ($admin_pages as $page => $info) {
    $permission = $info['صلاحية'];
    $description = $info['وصف'];
    $hasAccess = hasAdminPermission($permission);
    $accessStatus = $hasAccess ? '✅ مسموح' : '❌ مرفوض';
    $color = $hasAccess ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td><code>$page</code></td>";
    echo "<td>$description</td>";
    echo "<td><code>$permission</code></td>";
    echo "<td style='color: $color; font-weight: bold;'>$accessStatus</td>";
    if ($hasAccess) {
        echo "<td><a href='$page' target='_blank' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px;'>زيارة</a></td>";
    } else {
        echo "<td><span style='color: #6c757d;'>غير متاح</span></td>";
    }
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// جلب الصلاحيات من قاعدة البيانات
try {
    $db = getUnifiedDB();
    if ($db) {
        $admin_id = $_SESSION['admin_id'];
        $stmt = $db->prepare("SELECT permissions FROM admins WHERE id = ?");
        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $admin_data = $result->fetch_assoc();
        $stmt->close();
        
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>🗄️ الصلاحيات من قاعدة البيانات:</h4>";
        if ($admin_data && $admin_data['permissions']) {
            $db_permissions = json_decode($admin_data['permissions'], true);
            echo "<pre>" . print_r($db_permissions, true) . "</pre>";
            
            // مقارنة الصلاحيات
            if ($db_permissions !== $permissions) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 4px; margin-top: 10px;'>";
                echo "<strong>⚠️ تحذير:</strong> الصلاحيات في الجلسة تختلف عن قاعدة البيانات!";
                echo "</div>";
            } else {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 4px; margin-top: 10px;'>";
                echo "<strong>✅ جيد:</strong> الصلاحيات متطابقة بين الجلسة وقاعدة البيانات";
                echo "</div>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد صلاحيات في قاعدة البيانات</p>";
        }
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في قاعدة البيانات:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// أزرار الإجراءات
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='admin_manage_admins.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>إدارة المديرين</a>";
echo "<a href='admin_dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>لوحة التحكم</a>";
echo "<a href='admin_logout.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>تسجيل الخروج</a>";
echo "</div>";
?>
