<?php
/**
 * ملف اختبار النظام
 */

echo "<h1>اختبار النظام</h1>";

// اختبار 1: تحميل ملف init.php
echo "<h2>1. اختبار تحميل ملف init.php</h2>";
try {
    require_once __DIR__ . '/config/init.php';
    echo "<p style='color: green;'>✅ تم تحميل ملف init.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل ملف init.php: " . $e->getMessage() . "</p>";
    exit;
}

// اختبار 2: فحص الاتصال بقاعدة البيانات
echo "<h2>2. اختبار الاتصال بقاعدة البيانات</h2>";
try {
    $db = getUnifiedDB();
    if ($db) {
        echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
        echo "<p>نوع قاعدة البيانات: " . get_class($db) . "</p>";
        echo "<p>اسم قاعدة البيانات: " . (defined('DB_NAME') ? DB_NAME : 'u193708811_system_main') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في الاتصال بقاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار 3: فحص الجداول
echo "<h2>3. فحص الجداول</h2>";
try {
    $required_tables = ['users', 'admins', 'system_settings', 'customers', 'products'];
    foreach ($required_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ جدول $table موجود</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ جدول $table غير موجود</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص الجداول: " . $e->getMessage() . "</p>";
}

// اختبار 4: فحص الدوال
echo "<h2>4. فحص الدوال المطلوبة</h2>";
$required_functions = [
    'getUnifiedDB',
    'createAppTables', 
    'insertInitialSystemData',
    'ensureSuperAdminExists',
    'logActivity'
];

foreach ($required_functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ دالة $func موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة $func غير موجودة</p>";
    }
}

// اختبار 5: فحص الثوابت
echo "<h2>5. فحص الثوابت</h2>";
$required_constants = ['DB_HOST', 'DB_USERNAME', 'DB_PASSWORD', 'DB_NAME', 'DB_CHARSET'];

foreach ($required_constants as $const) {
    if (defined($const)) {
        echo "<p style='color: green;'>✅ ثابت $const موجود: " . constant($const) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ ثابت $const غير موجود</p>";
    }
}

// اختبار 6: فحص الملفات المطلوبة
echo "<h2>6. فحص الملفات المطلوبة</h2>";
$required_files = [
    'config/init.php',
    'config/unified_db_config.php',
    'includes/error_handler.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($required_files as $file) {
    $full_path = __DIR__ . '/' . $file;
    if (file_exists($full_path)) {
        echo "<p style='color: green;'>✅ ملف $file موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف $file غير موجود</p>";
    }
}

// اختبار 7: اختبار إنشاء الجداول
echo "<h2>7. اختبار إنشاء الجداول</h2>";
try {
    if (function_exists('createAppTables')) {
        $result = createAppTables();
        if ($result) {
            echo "<p style='color: green;'>✅ تم إنشاء الجداول بنجاح</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ فشل في إنشاء بعض الجداول</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ دالة createAppTables غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء الجداول: " . $e->getMessage() . "</p>";
}

// اختبار 8: اختبار المدير الرئيسي
echo "<h2>8. اختبار المدير الرئيسي</h2>";
try {
    if (function_exists('ensureSuperAdminExists')) {
        $admin_result = ensureSuperAdminExists();
        if (is_array($admin_result)) {
            if ($admin_result['exists']) {
                echo "<p style='color: green;'>✅ المدير الرئيسي موجود: " . $admin_result['username'] . "</p>";
            } elseif ($admin_result['created']) {
                echo "<p style='color: green;'>✅ تم إنشاء المدير الرئيسي: " . $admin_result['username'] . "</p>";
                echo "<p style='color: orange;'>⚠️ كلمة المرور: " . $admin_result['default_password'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء المدير الرئيسي</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ نتيجة غير متوقعة من دالة ensureSuperAdminExists</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ دالة ensureSuperAdminExists غير موجودة</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص المدير الرئيسي: " . $e->getMessage() . "</p>";
}

echo "<h2>انتهى الاختبار</h2>";
echo "<p><a href='index.php'>الذهاب للصفحة الرئيسية</a></p>";
echo "<p><a href='login.php'>صفحة تسجيل الدخول</a></p>";

?>
