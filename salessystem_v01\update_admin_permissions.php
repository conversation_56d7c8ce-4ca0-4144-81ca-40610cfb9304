<?php
/**
 * تحديث صلاحيات المديرين الموجودين
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>🔐 تحديث صلاحيات المديرين</h2>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    $results = [];
    $errors = [];

    // قائمة الصلاحيات الكاملة
    $all_permissions = [
        'view_dashboard',      // عرض لوحة التحكم
        'manage_users',        // إدارة المستخدمين
        'view_all_data',       // عرض جميع البيانات
        'manage_system',       // إدارة النظام
        'view_reports',        // عرض التقارير
        'manage_admins',       // إدارة المديرين
        'view_system_logs',    // عرض سجل النظام
        'export_data',         // تصدير البيانات
        'manage_settings'      // إدارة الإعدادات
    ];

    // جلب جميع المديرين
    $admins_query = $db->query("SELECT id, username, full_name, permissions FROM admins");
    if (!$admins_query) {
        throw new Exception("فشل في جلب بيانات المديرين: " . $db->error);
    }

    echo "<div style='background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>📋 المديرين الموجودين:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الصلاحيات الحالية</th><th>الإجراء</th></tr>";

    $updated_count = 0;
    while ($admin = $admins_query->fetch_assoc()) {
        $current_permissions = json_decode($admin['permissions'], true) ?? [];
        
        echo "<tr>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name'] ?? 'غير محدد') . "</td>";
        echo "<td>";
        
        if (empty($current_permissions)) {
            echo "<span style='color: red;'>لا توجد صلاحيات</span>";
            
            // إعطاء صلاحيات كاملة للمديرين بدون صلاحيات
            $new_permissions = json_encode($all_permissions);
            $update_stmt = $db->prepare("UPDATE admins SET permissions = ? WHERE id = ?");
            $update_stmt->bind_param("si", $new_permissions, $admin['id']);
            
            if ($update_stmt->execute()) {
                echo " → <span style='color: green;'>تم تحديث الصلاحيات</span>";
                $updated_count++;
                $results[] = "تم تحديث صلاحيات المدير: " . $admin['username'];
            } else {
                echo " → <span style='color: red;'>فشل التحديث</span>";
                $errors[] = "فشل في تحديث صلاحيات المدير: " . $admin['username'];
            }
            $update_stmt->close();
            
        } else {
            // التحقق من وجود الصلاحيات الأساسية
            $missing_permissions = array_diff($all_permissions, $current_permissions);
            
            if (!empty($missing_permissions)) {
                echo "<span style='color: orange;'>ناقصة: " . implode(', ', $missing_permissions) . "</span>";
                
                // إضافة الصلاحيات المفقودة
                $updated_permissions = array_unique(array_merge($current_permissions, $all_permissions));
                $new_permissions = json_encode($updated_permissions);
                $update_stmt = $db->prepare("UPDATE admins SET permissions = ? WHERE id = ?");
                $update_stmt->bind_param("si", $new_permissions, $admin['id']);
                
                if ($update_stmt->execute()) {
                    echo " → <span style='color: green;'>تم إضافة الصلاحيات المفقودة</span>";
                    $updated_count++;
                    $results[] = "تم تحديث صلاحيات المدير: " . $admin['username'];
                } else {
                    echo " → <span style='color: red;'>فشل التحديث</span>";
                    $errors[] = "فشل في تحديث صلاحيات المدير: " . $admin['username'];
                }
                $update_stmt->close();
                
            } else {
                echo "<span style='color: green;'>مكتملة (" . count($current_permissions) . " صلاحية)</span>";
            }
        }
        
        echo "</td>";
        echo "<td>";
        if (empty($current_permissions) || !empty($missing_permissions)) {
            echo "<span style='color: blue;'>تم التحديث</span>";
        } else {
            echo "<span style='color: green;'>لا يحتاج تحديث</span>";
        }
        echo "</td>";
        echo "</tr>";
    }

    echo "</table>";
    echo "</div>";

    // عرض النتائج
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>✅ النتائج:</h4>";
    echo "<p><strong>عدد المديرين المحدثين:</strong> $updated_count</p>";
    foreach ($results as $result) {
        echo "<p>✅ $result</p>";
    }
    echo "</div>";

    if (!empty($errors)) {
        echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>❌ الأخطاء:</h4>";
        foreach ($errors as $error) {
            echo "<p>❌ $error</p>";
        }
        echo "</div>";
    }

    // عرض قائمة الصلاحيات المتاحة
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>📋 قائمة الصلاحيات المتاحة:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الصلاحية</th><th>الوصف</th><th>الصفحات المرتبطة</th></tr>";

    $permissions_info = [
        'view_dashboard' => ['الوصف' => 'عرض لوحة التحكم', 'الصفحات' => 'admin_dashboard.php'],
        'manage_users' => ['الوصف' => 'إدارة المستخدمين', 'الصفحات' => 'admin_users.php'],
        'view_all_data' => ['الوصف' => 'عرض جميع البيانات', 'الصفحات' => 'admin_activity.php'],
        'manage_system' => ['الوصف' => 'إدارة النظام', 'الصفحات' => 'admin_system.php'],
        'view_reports' => ['الوصف' => 'عرض التقارير', 'الصفحات' => 'admin_reports.php, admin_financial.php'],
        'manage_admins' => ['الوصف' => 'إدارة المديرين', 'الصفحات' => 'admin_manage_admins.php'],
        'view_system_logs' => ['الوصف' => 'عرض سجل النظام', 'الصفحات' => 'admin_error_logs.php'],
        'export_data' => ['الوصف' => 'تصدير البيانات', 'الصفحات' => 'جميع صفحات التصدير'],
        'manage_settings' => ['الوصف' => 'إدارة الإعدادات', 'الصفحات' => 'صفحات الإعدادات']
    ];

    foreach ($permissions_info as $permission => $info) {
        echo "<tr>";
        echo "<td><code>$permission</code></td>";
        echo "<td>" . $info['الوصف'] . "</td>";
        echo "<td><small>" . $info['الصفحات'] . "</small></td>";
        echo "</tr>";
    }

    echo "</table>";
    echo "</div>";

    // اختبار الصلاحيات
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>🧪 اختبار الصلاحيات:</h4>";
    echo "<p>يمكنك الآن اختبار الصلاحيات باستخدام الروابط التالية:</p>";
    echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
    echo "<a href='test_permissions.php' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>اختبار الصلاحيات</a>";
    echo "<a href='admin_manage_admins.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>إدارة المديرين</a>";
    echo "<a href='admin_dashboard.php' style='background: #17a2b8; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;'>لوحة التحكم</a>";
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
