<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()

// التحقق من وضع النافذة المنبثقة
$is_modal = isset($_GET['modal']) && $_GET['modal'] == '1';

if (!$is_modal) {
    require_once __DIR__.'/includes/header.php';
}
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات: " . ($db ? $db->connect_error : "اتصال غير موجود");
    header("Location: purchases.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

if (!isset($_GET['id'])) {
    header("Location: purchases.php");
    exit();
}

$purchase_id = intval($_GET['id']);
$purchase = null;
$items = null;

try {
    // جلب بيانات الفاتورة
    $stmt = $db->prepare("SELECT * FROM purchases WHERE id = ?");
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $purchase_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $purchase = $result->fetch_assoc();
    $stmt->close(); // إغلاق الاستعلام

    if (!$purchase) {
        $_SESSION['error'] = "فاتورة المشتريات غير موجودة";
        header("Location: purchases.php");
        exit();
    }

    // تنظيف أي نتائج متبقية
    $db = resetDBConnection($db);

    // جلب عناصر الفاتورة
    $query = "SELECT * FROM purchase_items WHERE purchase_id = ?";
    $stmt = $db->prepare($query);
    if (!$stmt) {
        throw new Exception("خطأ في إعداد استعلام عناصر الفاتورة: " . $db->error);
    }

    $stmt->bind_param("i", $purchase_id);
    $stmt->execute();
    $items = $stmt->get_result();

} catch (Exception $e) {
    error_log("خطأ في عرض الفاتورة: " . $e->getMessage());
    $_SESSION['error'] = "حدث خطأ أثناء جلب بيانات الفاتورة";
    header("Location: purchases.php");
    exit();
}

if (!$is_modal) {
    displayMessages(); // عرض أي رسائل خطأ أو نجاح
}
?>

<?php if (!$is_modal): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>عرض فاتورة مشتريات</h5>
                    <div>
                        <a href="print_invoice.php?id=<?php echo $purchase_id; ?>&type=purchase" class="btn btn-sm btn-secondary" target="_blank">
                            <i class="fas fa-print"></i> طباعة
                        </a>
                        <a href="purchases.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left"></i> رجوع
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
<?php else: ?>
<!-- محتوى النافذة المنبثقة -->
<div class="modal-content-wrapper">
<?php endif; ?>
                <div class="row mb-2">
                    <div class="col-md-6">
                        <h6 class="mb-1">معلومات المورد:</h6>
                        <small>
                            <strong>الاسم:</strong> <?php echo htmlspecialchars($purchase['supplier_name']); ?>
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <h6 class="mb-1">معلومات الفاتورة:</h6>
                        <small>
                            <strong>رقم الفاتورة:</strong> <?php echo $purchase['invoice_number']; ?><br>
                            <strong>التاريخ:</strong> <?php echo $purchase['date']; ?><br>
                            <?php
                            // ترجمة حالة الدفع
                            $payment_statuses = [
                                'paid' => ['text' => 'مدفوع بالكامل', 'class' => 'bg-success'],
                                'partial' => ['text' => 'مدفوع جزئياً', 'class' => 'bg-warning'],
                                'unpaid' => ['text' => 'غير مدفوع', 'class' => 'bg-danger']
                            ];
                            $status = $payment_statuses[$purchase['payment_status'] ?? 'unpaid'] ?? ['text' => 'غير محدد', 'class' => 'bg-secondary'];
                            ?>
                            <strong>حالة الدفع:</strong> <span class="badge <?php echo $status['class']; ?>"><?php echo $status['text']; ?></span>
                        </small>
                    </div>
                </div>

                <!-- قسم معلومات الدفع -->
                <div class="row mb-2">
                    <div class="col-md-12">
                        <div class="card bg-light border-danger">
                            <div class="card-header bg-danger text-white py-1">
                                <small class="mb-0 fw-bold">
                                    <i class="fas fa-credit-card me-1"></i>
                                    معلومات الدفع
                                </small>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <small><strong>طريقة الدفع:</strong><br>
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة ائتمان',
                                            'bank_transfer' => 'تحويل بنكي',
                                            'check' => 'شيك',
                                            'installment' => 'تقسيط',
                                            'other' => 'أخرى'
                                        ];
                                        echo $payment_methods[$purchase['payment_method'] ?? 'cash'] ?? 'نقدي';
                                        ?></small>
                                    </div>
                                    <div class="col-md-6">
                                        <small><strong>المبلغ المدفوع:</strong><br>
                                        <span class="text-success fw-bold"><?php echo number_format($purchase['paid_amount'] ?? 0, 2); ?> ر.س</span></small>
                                    </div>
                                </div>
                                <?php if (!empty($purchase['payment_reference'])): ?>
                                <div class="row mt-1">
                                    <div class="col-md-12">
                                        <small><strong>مرجع الدفع:</strong> <?php echo htmlspecialchars($purchase['payment_reference']); ?></small>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 5%;">#</th>
                                <th style="width: 35%;">المنتج</th>
                                <th style="width: 10%;">كمية</th>
                                <th style="width: 15%;">سعر</th>
                                <th style="width: 10%;">ضريبة%</th>
                                <th style="width: 15%;">ق.ضريبة</th>
                                <th style="width: 15%;">مجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                if ($items && $items->num_rows > 0) {
                                    $counter = 1;
                                    while ($item = $items->fetch_assoc()):
                                        $item_subtotal = $item['quantity'] * $item['unit_price'];
                                    ?>
                                    <tr>
                                        <td><?php echo $counter++; ?></td>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo number_format($item['unit_price'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['tax_rate'], 2); ?>%</td>
                                        <td><?php echo number_format($item['tax_amount'], 2); ?> ر.س</td>
                                        <td><?php echo number_format($item['total_price'], 2); ?> ر.س</td>
                                    </tr>
                                    <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="7" class="text-center">لا توجد عناصر في هذه الفاتورة</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("خطأ في عرض عناصر الفاتورة: " . $e->getMessage());
                                echo '<tr><td colspan="7" class="text-center">حدث خطأ أثناء جلب عناصر الفاتورة</td></tr>';
                            }
                            ?>
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="5"></th>
                                <th><small>فرعي:</small></th>
                                <td><small><?php echo number_format($purchase['subtotal'], 2); ?> ر.س</small></td>
                            </tr>
                            <tr>
                                <th colspan="5"></th>
                                <th><small>ضريبة:</small></th>
                                <td><small><?php echo number_format($purchase['tax_amount'], 2); ?> ر.س</small></td>
                            </tr>
                            <tr class="table-primary">
                                <th colspan="5"></th>
                                <th><strong>إجمالي:</strong></th>
                                <td class="fw-bold"><?php echo number_format($purchase['total_amount'], 2); ?> ر.س</td>
                            </tr>
                            <?php if (($purchase['remaining_amount'] ?? 0) > 0): ?>
                            <tr class="table-danger">
                                <th colspan="5"></th>
                                <th><small>متبقي:</small></th>
                                <td class="text-danger fw-bold">
                                    <small><?php echo number_format($purchase['remaining_amount'], 2); ?> ر.س</small>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tfoot>
                    </table>
                </div>

                <?php if (!empty($purchase['notes'])): ?>
                <div class="mt-3">
                    <h6>ملاحظات:</h6>
                    <p><?php echo htmlspecialchars($purchase['notes']); ?></p>
                </div>
                <?php endif; ?>

<?php if (!$is_modal): ?>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
</div>

<!-- CSS مخصص للنوافذ المنبثقة -->
<style>
.modal-content-wrapper {
    font-size: 13px;
}

.modal-content-wrapper .table-sm th,
.modal-content-wrapper .table-sm td {
    padding: 4px 6px;
    font-size: 11px;
}

.modal-content-wrapper .card-header {
    padding: 6px 10px;
}

.modal-content-wrapper .card-body {
    padding: 8px 10px;
}

.modal-content-wrapper h6 {
    font-size: 14px;
    margin-bottom: 4px;
}

.modal-content-wrapper small {
    font-size: 11px;
}

.modal-content-wrapper .badge {
    font-size: 10px;
}
</style>
<?php endif; ?>

<?php
if (!$is_modal) {
    require_once 'includes/footer.php';
}
// إغلاق اتصال قاعدة البيانات
if (isset($db) && $db) {
    $db->close();
}
?>