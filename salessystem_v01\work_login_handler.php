<?php
/**
 * معالج تسجيل الدخول لأقسام إدارة العمل
 */

session_start();
require_once 'config/unified_db_config.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: work_management_login.php');
    exit;
}

// جلب البيانات المرسلة
$username = trim($_POST['username'] ?? '');
$password = trim($_POST['password'] ?? '');
$department = trim($_POST['department'] ?? '');

// التحقق من صحة البيانات
if (empty($username) || empty($password) || empty($department)) {
    $_SESSION['error'] = 'جميع الحقول مطلوبة';
    header('Location: work_management_login.php');
    exit;
}

// تحديد الجدول والصفحة حسب القسم
$table = '';
$redirect_page = '';
$session_prefix = '';

switch ($department) {
    case 'employees':
        $table = 'employees';
        $redirect_page = 'employees_dashboard.php';
        $session_prefix = 'employee_';
        break;
    case 'supervisors':
        $table = 'supervisors';
        $redirect_page = 'supervisors_dashboard.php';
        $session_prefix = 'supervisor_';
        break;
    case 'management':
        $table = 'admins';
        $redirect_page = 'admin_dashboard.php';
        $session_prefix = 'admin_';
        break;
    default:
        $_SESSION['error'] = 'قسم غير صحيح';
        header('Location: work_management_login.php');
        exit;
}

try {
    // البحث عن المستخدم في قاعدة البيانات
    $query = "SELECT * FROM $table WHERE username = ? AND status = 'active'";
    $stmt = $main_db->prepare($query);
    $stmt->bind_param('s', $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        
        // التحقق من كلمة المرور
        if (password_verify($password, $user['password'])) {
            // تسجيل الدخول ناجح
            $_SESSION[$session_prefix . 'logged_in'] = true;
            $_SESSION[$session_prefix . 'id'] = $user['id'];
            $_SESSION[$session_prefix . 'username'] = $user['username'];
            $_SESSION[$session_prefix . 'name'] = $user['name'] ?? $user['full_name'] ?? $username;
            $_SESSION[$session_prefix . 'department'] = $department;
            $_SESSION[$session_prefix . 'role'] = $user['role'] ?? $department;
            
            // تحديث آخر تسجيل دخول
            $update_query = "UPDATE $table SET last_login = NOW() WHERE id = ?";
            $update_stmt = $main_db->prepare($update_query);
            $update_stmt->bind_param('i', $user['id']);
            $update_stmt->execute();
            
            // تسجيل النشاط
            logActivity($session_prefix . 'login', $table, $user['id'], null, null, "تسجيل دخول $department");
            
            $_SESSION['success'] = 'تم تسجيل الدخول بنجاح';
            header('Location: ' . $redirect_page);
            exit;
            
        } else {
            $_SESSION['error'] = 'كلمة المرور غير صحيحة';
        }
    } else {
        $_SESSION['error'] = 'اسم المستخدم غير موجود أو الحساب غير نشط';
    }
    
} catch (Exception $e) {
    error_log("خطأ في تسجيل الدخول: " . $e->getMessage());
    $_SESSION['error'] = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
}

// في حالة فشل تسجيل الدخول
header('Location: work_management_login.php');
exit;

/**
 * دالة تسجيل النشاط
 */
function logActivity($action, $table, $user_id, $target_id = null, $old_data = null, $description = '') {
    global $main_db;
    
    try {
        $query = "INSERT INTO activity_log (action, table_name, user_id, target_id, old_data, description, created_at) 
                  VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $main_db->prepare($query);
        $stmt->bind_param('ssisss', $action, $table, $user_id, $target_id, $old_data, $description);
        $stmt->execute();
    } catch (Exception $e) {
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
    }
}
?>
