<?php
session_start();

// جلب رسائل الخطأ والنجاح
$error_message = $_SESSION['error'] ?? '';
$success_message = $_SESSION['success'] ?? '';

// مسح الرسائل من الجلسة
unset($_SESSION['error'], $_SESSION['success']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العمل - تسجيل الدخول</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            
            --shadow-light: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 15px 40px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 20px 50px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-x: hidden;
        }

        .main-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            animation: fadeInDown 1s ease-out;
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            padding: 0 1rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: var(--shadow-light);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 300px;
            cursor: pointer;
        }

        .login-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-heavy);
            height: 500px;
        }

        .card-header {
            padding: 2rem;
            text-align: center;
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
        }

        .card-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            transition: all 0.4s ease;
        }

        .employees-card .card-icon {
            background: var(--primary-gradient);
        }

        .supervisors-card .card-icon {
            background: var(--warning-gradient);
        }

        .management-card .card-icon {
            background: var(--danger-gradient);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .card-description {
            color: #7f8c8d;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .login-form {
            padding: 0 2rem 2rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease 0.2s;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }

        .login-card:hover .login-form {
            opacity: 1;
            transform: translateY(0);
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .form-label {
            position: absolute;
            top: 12px;
            right: 15px;
            color: #7f8c8d;
            font-size: 1rem;
            transition: all 0.3s ease;
            pointer-events: none;
            background: white;
            padding: 0 5px;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            top: -8px;
            font-size: 0.85rem;
            color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .employees-card .login-btn {
            background: var(--primary-gradient);
        }

        .supervisors-card .login-btn {
            background: var(--warning-gradient);
        }

        .management-card .login-btn {
            background: var(--danger-gradient);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .login-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .login-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .login-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }
            
            .cards-container {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .login-card {
                height: 250px;
            }
            
            .login-card:hover {
                height: 450px;
            }
        }

        /* Loading Animation */
        .loading {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-loading .loading {
            display: inline-block;
        }

        .btn-loading .btn-text {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-building me-3"></i>
                إدارة العمل
            </h1>
            <p class="page-subtitle">اختر القسم المناسب لتسجيل الدخول</p>
        </div>

        <!-- Login Cards -->
        <div class="cards-container">
            <!-- Employees Card -->
            <div class="login-card employees-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">الموظفين</h3>
                    <p class="card-description">
                        منطقة تسجيل دخول الموظفين لإدارة المهام اليومية والتقارير
                    </p>
                </div>
                <form class="login-form" action="work_login_handler.php" method="POST" onsubmit="handleLogin(event, 'employees')">
                    <input type="hidden" name="department" value="employees">
                    <div class="form-group">
                        <input type="text" name="username" class="form-control" placeholder=" " required>
                        <label class="form-label">اسم المستخدم</label>
                    </div>
                    <div class="form-group">
                        <input type="password" name="password" class="form-control" placeholder=" " required>
                        <label class="form-label">كلمة المرور</label>
                    </div>
                    <button type="submit" class="login-btn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <span class="loading"></span>
                    </button>
                </form>
            </div>

            <!-- Supervisors Card -->
            <div class="login-card supervisors-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="card-title">المشرفين</h3>
                    <p class="card-description">
                        منطقة تسجيل دخول المشرفين لمتابعة الفرق وإدارة المشاريع
                    </p>
                </div>
                <form class="login-form" action="work_login_handler.php" method="POST" onsubmit="handleLogin(event, 'supervisors')">
                    <input type="hidden" name="department" value="supervisors">
                    <div class="form-group">
                        <input type="text" name="username" class="form-control" placeholder=" " required>
                        <label class="form-label">اسم المستخدم</label>
                    </div>
                    <div class="form-group">
                        <input type="password" name="password" class="form-control" placeholder=" " required>
                        <label class="form-label">كلمة المرور</label>
                    </div>
                    <button type="submit" class="login-btn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <span class="loading"></span>
                    </button>
                </form>
            </div>

            <!-- Management Card -->
            <div class="login-card management-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 class="card-title">الإدارة</h3>
                    <p class="card-description">
                        منطقة تسجيل دخول الإدارة العليا للتحكم الكامل في النظام
                    </p>
                </div>
                <form class="login-form" action="work_login_handler.php" method="POST" onsubmit="handleLogin(event, 'management')">
                    <input type="hidden" name="department" value="management">
                    <div class="form-group">
                        <input type="text" name="username" class="form-control" placeholder=" " required>
                        <label class="form-label">اسم المستخدم</label>
                    </div>
                    <div class="form-group">
                        <input type="password" name="password" class="form-control" placeholder=" " required>
                        <label class="form-label">كلمة المرور</label>
                    </div>
                    <button type="submit" class="login-btn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <span class="loading"></span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // تعامل مع تسجيل الدخول
        function handleLogin(event, department) {
            const form = event.target;
            const username = form.querySelector('input[name="username"]').value.trim();
            const password = form.querySelector('input[name="password"]').value.trim();
            const submitBtn = form.querySelector('.login-btn');

            // التحقق من صحة البيانات
            if (!username || !password) {
                event.preventDefault();
                Swal.fire({
                    icon: 'warning',
                    title: 'بيانات ناقصة',
                    text: 'يرجى إدخال اسم المستخدم وكلمة المرور',
                    confirmButtonText: 'حسناً',
                    background: '#fff',
                    color: '#2c3e50'
                });
                return false;
            }

            // إضافة حالة التحميل
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            // السماح للنموذج بالإرسال
            return true;
        }

        // تأثيرات إضافية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // عرض رسائل الخطأ والنجاح
            <?php if (!empty($error_message)): ?>
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: '<?php echo addslashes($error_message); ?>',
                confirmButtonText: 'حاول مرة أخرى',
                background: '#fff',
                color: '#2c3e50'
            });
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
            Swal.fire({
                icon: 'success',
                title: 'نجح',
                text: '<?php echo addslashes($success_message); ?>',
                timer: 2000,
                showConfirmButton: false,
                background: '#fff',
                color: '#2c3e50'
            });
            <?php endif; ?>

            // إضافة تأثير الظهور التدريجي للكروت
            const cards = document.querySelectorAll('.login-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 * (index + 1));
            });

            // تأثير الجسيمات في الخلفية
            createParticles();
        });

        // إنشاء تأثير الجسيمات
        function createParticles() {
            const particlesContainer = document.createElement('div');
            particlesContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: -1;
            `;
            document.body.appendChild(particlesContainer);

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 4px;
                    height: 4px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation-delay: ${Math.random() * 2}s;
                `;
                particlesContainer.appendChild(particle);
            }

            // إضافة CSS للحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes float {
                    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
                    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
                }
            `;
            document.head.appendChild(style);
        }

        // تأثير التمرير السلس للكروت
        document.querySelectorAll('.login-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                if (!this.matches(':hover')) {
                    this.style.transform = 'translateY(0) scale(1)';
                }
            });
        });

        // تأثير الكتابة في الحقول
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
