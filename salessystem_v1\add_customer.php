<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header_simple.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $email = trim($_POST['email']);
    $tax_number = trim($_POST['tax_number']);
    $address = trim($_POST['address']);
    $customer_type = isset($_POST['customer_type']) ? $_POST['customer_type'] : 'customer';

    // استخدام الدالة الجديدة لإدراج البيانات مع user_id تلقائياً
    $customer_data = [
        'name' => $name,
        'phone' => $phone,
        'email' => $email,
        'tax_number' => $tax_number,
        'address' => $address,
        'customer_type' => $customer_type
    ];

    $customer_id = insertWithUserId('customers', $customer_data);

    if ($customer_id) {
        $_SESSION['success'] = "تم إضافة العميل بنجاح (ID: $customer_id)";
        header("Location: customers.php");
        exit();
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة العميل";
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">إضافة عميل جديد</div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label"><?php echo t("name");?> *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الجوال</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label"><?php echo 'النص'; ?></label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="tax_number" class="form-label"><?php echo 'النص'; ?></label>
                        <input type="text" class="form-control" id="tax_number" name="tax_number">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label"><?php echo 'النص'; ?></label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="customer_type" class="form-label">نوع العميل *</label>
                        <select class="form-control" id="customer_type" name="customer_type" required>
                            <option value="customer">عميل</option>
                            <option value="supplier">مورد</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <a href="customers.php" class="btn btn-secondary">إلغاء</a>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>