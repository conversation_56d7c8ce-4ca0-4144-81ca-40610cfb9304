<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/invoice_functions.php';
require_once __DIR__ . '/includes/header_simple.php';
redirectIfNotLoggedIn();

// جلب إعدادات المشتريات
$default_tax_rate = getInvoiceSetting('default_tax_rate', '15');
$purchase_invoice_prefix = getInvoiceSetting('purchase_invoice_prefix', 'PUR');
$currency_symbol = getInvoiceSetting('currency_symbol', 'ريال');
$decimal_places = intval(getInvoiceSetting('decimal_places', '2'));
$default_payment_method = getInvoiceSetting('default_payment_method', 'cash');
$auto_purchase_number = getInvoiceSetting('auto_purchase_number', '1') == '1';
$require_supplier = getInvoiceSetting('require_supplier', '0') == '1';
$auto_update_stock = getInvoiceSetting('auto_update_stock', '1') == '1';
$purchase_approval_required = getInvoiceSetting('purchase_approval_required', '0') == '1';

$db = getCurrentUserDB();

// الحصول على أسماء الجداول مع البادئة
$username = $_SESSION['username'];
$customers_table = getUserTableName('customers', $username);
$products_table = getUserTableName('products', $username);

// جلب قائمة المنتجات (مشتركة) والعملاء (خاصة بالمستخدم)
// التحقق من وجود عمود is_active في جدول المنتجات
$columns_check = $db->query("SHOW COLUMNS FROM products LIKE 'is_active'");
$has_is_active = $columns_check && $columns_check->num_rows > 0;

if ($has_is_active) {
    $products_query = $db->query("SELECT id, name, price, tax_rate FROM products WHERE is_active = 1 ORDER BY category, name");
} else {
    $products_query = $db->query("SELECT id, name, price, tax_rate FROM products ORDER BY name");
}

// جلب قائمة الموردين فقط (وليس العملاء)
$customers = $db->query("SELECT id, name, phone FROM `$customers_table` WHERE user_id = {$_SESSION['user_id']} AND customer_type = 'supplier' ORDER BY name");

// تخزين المنتجات في مصفوفة لاستخدامها لاحقًا
$products_array = [];
while ($product = $products_query->fetch_assoc()) {
    $products_array[] = $product;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_id = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : NULL;

    // للتأكد من أن customer_id ليس NULL إذا كان الجدول لا يسمح بقيم NULL
    if ($customer_id === NULL) {
        $customer_id = 0; // استخدام 0 كقيمة افتراضية بدلاً من NULL
    }
    $date = $_POST['date'];
    $notes = trim($_POST['notes']);

    // بيانات الدفع
    $payment_method = $_POST['payment_method'] ?? 'cash';
    $payment_status = $_POST['payment_status'] ?? 'unpaid';
    $paid_amount = floatval($_POST['paid_amount'] ?? 0);
    $payment_date = !empty($_POST['payment_date']) ? $_POST['payment_date'] : null;
    $payment_reference = trim($_POST['payment_reference'] ?? '');
    $payment_notes = trim($_POST['payment_notes'] ?? '');

    // معالجة العناصر
    $items = [];
    $product_ids = $_POST['product_id'] ?? [];
    $quantities = $_POST['quantity'] ?? [];
    $prices = $_POST['price'] ?? [];
    $tax_rates = $_POST['tax_rate'] ?? [];

    for ($i = 0; $i < count($product_ids); $i++) {
        if (!empty($product_ids[$i]) && !empty($quantities[$i])) {
            $items[] = [
                'product_id' => intval($product_ids[$i]),
                'quantity' => intval($quantities[$i]),
                'unit_price' => floatval($prices[$i]),
                'tax_rate' => floatval($tax_rates[$i])
            ];
        }
    }

    if (empty($items)) {
        $_SESSION['error'] = "يجب إضافة عنصر واحد على الأقل";
    } else {
        // حساب المجموع والضريبة
        $calculations = calculateTaxAndTotal($items);

        // إنشاء رقم الفاتورة باستخدام الإعدادات
        if ($auto_purchase_number) {
            $invoice_number = getNextInvoiceNumber('purchase');
        } else {
            $invoice_number = $purchase_invoice_prefix . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        }

        // الحصول على أسماء الجداول مع البادئة
        $purchases_table = getUserTableName('purchases', $username);
        $purchase_items_table = getUserTableName('purchase_items', $username);

        // حساب المبلغ المتبقي
        $remaining_amount = $calculations['total'] - $paid_amount;

        // إعداد بيانات الفاتورة مع بيانات الدفع
        $purchase_data = [
            'customer_id' => $customer_id,
            'invoice_number' => $invoice_number,
            'date' => $date,
            'subtotal' => $calculations['subtotal'],
            'tax_amount' => $calculations['tax_amount'],
            'total_amount' => $calculations['total'],
            'payment_method' => $payment_method,
            'payment_status' => $payment_status,
            'paid_amount' => $paid_amount,
            'remaining_amount' => $remaining_amount,
            'payment_date' => $payment_date,
            'payment_reference' => $payment_reference,
            'payment_notes' => $payment_notes,
            'notes' => $notes
        ];

        // إدراج الفاتورة باستخدام الدالة الجديدة
        $purchase_id = insertWithUserId('purchases', $purchase_data, $username);

        if ($purchase_id) {
            // إضافة العناصر
            foreach ($items as $item) {
                $product_name = '';
                foreach ($products_array as $product) {
                    if ($product['id'] == $item['product_id']) {
                        $product_name = $product['name'];
                        break;
                    }
                }

                $item_total = $item['quantity'] * $item['unit_price'];
                $item_tax = $item_total * ($item['tax_rate'] / 100);
                $total_price = $item_total + $item_tax;

                // إعداد بيانات العنصر
                $item_data = [
                    'purchase_id' => $purchase_id,
                    'product_id' => $item['product_id'],
                    'product_name' => $product_name,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'tax_rate' => $item['tax_rate'],
                    'tax_amount' => $item_tax,
                    'total_price' => $total_price
                ];

                // إدراج العنصر باستخدام الدالة الجديدة
                insertWithUserId('purchase_items', $item_data, $username);
            }

            // تسجيل العملية
            logActivity('purchase_create', 'purchases', $purchase_id, null, ['invoice_number' => $invoice_number, 'total_amount' => $calculations['total']], 'إنشاء فاتورة مشتريات جديدة');

            $_SESSION['success'] = "تم إضافة فاتورة المشتريات بنجاح";
            header("Location: purchases.php");
            exit();
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء إضافة فاتورة المشتريات";
        }
    }
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">إضافة فاتورة مشتريات</div>
            <div class="card-body">
                <form method="POST" id="purchaseForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label"><?php echo 'النص'; ?> <?php echo $require_supplier ? '<span class="text-danger">*</span>' : ''; ?></label>
                                <select class="form-select" id="customer_id" name="customer_id" <?php echo $require_supplier ? 'required' : ''; ?>>
                                    <option value="">-- اختر مورد --</option>
                                    <option value="new" class="text-success fw-bold">-- إضافة مورد جديد --</option>
                                    <?php while ($customer = $customers->fetch_assoc()): ?>
                                    <option value="<?php echo $customer['id']; ?>" <?php echo isset($_POST['customer_id']) && $_POST['customer_id'] == $customer['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                        <?php if (!empty($customer['phone'])): ?>
                                            - <?php echo htmlspecialchars($customer['phone']); ?>
                                        <?php endif; ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date" class="form-label"><?php echo 'النص'; ?></label>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                    </div>

                    <h5 class="mt-4">عناصر الفاتورة</h5>
                    <div class="table-responsive">
                        <table class="table" id="itemsTable">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الضريبة %</th>
                                    <th>المجموع</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsBody">
                                <!-- سيتم إضافة العناصر ديناميكيًا هنا -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="6">
                                        <button type="button" class="btn btn-sm btn-success" id="addItemBtn">
                                            <i class="fas fa-plus"></i> إضافة عنصر
                                        </button>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- قسم خيارات الدفع -->
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-credit-card me-2"></i>
                                        خيارات الدفع
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                                <select class="form-select" id="payment_method" name="payment_method" onchange="updatePaymentFields()">
                                                    <option value="cash" <?php echo ($_POST['payment_method'] ?? $default_payment_method) === 'cash' ? 'selected' : ''; ?>>نقدي</option>
                                                    <option value="card" <?php echo ($_POST['payment_method'] ?? '') === 'card' ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                                                    <option value="bank_transfer" <?php echo ($_POST['payment_method'] ?? '') === 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                                                    <option value="check" <?php echo ($_POST['payment_method'] ?? '') === 'check' ? 'selected' : ''; ?>>شيك</option>
                                                    <option value="installment" <?php echo ($_POST['payment_method'] ?? '') === 'installment' ? 'selected' : ''; ?>>تقسيط</option>
                                                    <option value="other" <?php echo ($_POST['payment_method'] ?? '') === 'other' ? 'selected' : ''; ?>>أخرى</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="payment_status" class="form-label">حالة الدفع</label>
                                                <select class="form-select" id="payment_status" name="payment_status" onchange="updatePaymentFields()">
                                                    <option value="unpaid" <?php echo ($_POST['payment_status'] ?? 'unpaid') === 'unpaid' ? 'selected' : ''; ?>>غير مدفوع</option>
                                                    <option value="partial" <?php echo ($_POST['payment_status'] ?? '') === 'partial' ? 'selected' : ''; ?>>مدفوع جزئياً</option>
                                                    <option value="paid" <?php echo ($_POST['payment_status'] ?? '') === 'paid' ? 'selected' : ''; ?>>مدفوع بالكامل</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="paid_amount" name="paid_amount"
                                                           step="0.01" min="0" value="<?php echo $_POST['paid_amount'] ?? '0'; ?>"
                                                           onchange="calculateRemainingAmount()">
                                                    <span class="input-group-text"><?php echo $currency_symbol; ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="mb-3">
                                                <label for="payment_date" class="form-label">تاريخ الدفع</label>
                                                <input type="date" class="form-control" id="payment_date" name="payment_date"
                                                       value="<?php echo $_POST['payment_date'] ?? date('Y-m-d'); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="payment_reference" class="form-label">مرجع الدفع</label>
                                                <input type="text" class="form-control" id="payment_reference" name="payment_reference"
                                                       placeholder="رقم الشيك، رقم التحويل، إلخ..."
                                                       value="<?php echo $_POST['payment_reference'] ?? ''; ?>">
                                                <small class="form-text text-muted">اختياري - رقم الشيك، رقم التحويل، أو أي مرجع آخر</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="payment_notes" class="form-label">ملاحظات الدفع</label>
                                                <textarea class="form-control" id="payment_notes" name="payment_notes" rows="2"
                                                          placeholder="ملاحظات إضافية حول الدفع..."><?php echo $_POST['payment_notes'] ?? ''; ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات عامة</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $_POST['notes'] ?? ''; ?></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">ملخص الفاتورة</div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <th>المجموع الفرعي:</th>
                                            <td id="subtotalCell">0.00 <?php echo $currency_symbol; ?></td>
                                        </tr>
                                        <tr>
                                            <th>الضريبة (<?php echo $default_tax_rate; ?>%):</th>
                                            <td id="taxCell">0.00 <?php echo $currency_symbol; ?></td>
                                        </tr>
                                        <tr>
                                            <th>الإجمالي:</th>
                                            <td id="totalCell">0.00 <?php echo $currency_symbol; ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button type="submit" class="btn btn-danger">حفظ الفاتورة</button>
                        <a href="purchases.php" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نافذة منبثقة لإضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel"><?php echo 'النص'; ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?php echo 'النص'; ?>"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="mb-3">
                        <label for="new_customer_name" class="form-label">اسم المورد *</label>
                        <input type="text" class="form-control" id="new_customer_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_customer_phone" class="form-label">رقم الجوال</label>
                        <input type="text" class="form-control" id="new_customer_phone">
                    </div>
                    <div class="mb-3">
                        <label for="new_customer_email" class="form-label"><?php echo 'النص'; ?></label>
                        <input type="email" class="form-control" id="new_customer_email">
                    </div>
                    <div class="mb-3">
                        <label for="new_customer_tax_number" class="form-label"><?php echo 'النص'; ?></label>
                        <input type="text" class="form-control" id="new_customer_tax_number">
                    </div>
                    <div class="mb-3">
                        <label for="new_customer_address" class="form-label"><?php echo 'النص'; ?></label>
                        <textarea class="form-control" id="new_customer_address" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo 'النص'; ?></button>
                <button type="button" class="btn btn-primary" id="saveNewCustomer"><?php echo 'النص'; ?></button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة منبثقة لإضافة منتج جديد -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel"><?php echo 'النص'; ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?php echo 'النص'; ?>"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="mb-3">
                        <label for="new_product_name" class="form-label"><?php echo 'النص'; ?> *</label>
                        <input type="text" class="form-control" id="new_product_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_product_price" class="form-label"><?php echo 'النص'; ?> *</label>
                        <input type="number" class="form-control" id="new_product_price" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_product_tax_rate" class="form-label"><?php echo 'النص'; ?> (%)</label>
                        <input type="number" class="form-control" id="new_product_tax_rate" step="0.01" min="0" value="<?php echo $default_tax_rate; ?>">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo 'النص'; ?></button>
                <button type="button" class="btn btn-primary" id="saveNewProduct"><?php echo 'النص'; ?></button>
            </div>
        </div>
    </div>
</div>

<script>
// متغير لتخزين بيانات المنتجات
const products = [
    <?php
    foreach ($products_array as $product):
        echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}},";
    endforeach;
    ?>
];

// دالة لإضافة صف عنصر جديد
function addItemRow(product = null) {
    const tbody = document.getElementById('itemsBody');
    const rowId = Date.now();

    const row = document.createElement('tr');
    row.id = `row_${rowId}`;

    // عمود المنتج
    const productCell = document.createElement('td');
    const productSelect = document.createElement('select');
    productSelect.className = 'form-select product-select';
    productSelect.name = 'product_id[]';
    productSelect.required = true;

    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '-- اختر منتج --';
    productSelect.appendChild(defaultOption);

    // إضافة خيار "إضافة منتج جديد"
    const addNewOption = document.createElement('option');
    addNewOption.value = 'new';
    addNewOption.textContent = '-- إضافة منتج جديد --';
    addNewOption.className = 'text-success fw-bold';
    productSelect.appendChild(addNewOption);

    products.forEach(p => {
        const option = document.createElement('option');
        option.value = p.id;
        option.textContent = p.name;
        option.dataset.price = p.price;
        option.dataset.taxRate = p.tax_rate;

        if (product && product.id == p.id) {
            option.selected = true;
        }

        productSelect.appendChild(option);
    });

    productSelect.addEventListener('change', function() {
        const selectedValue = this.value;
        const row = this.closest('tr');

        if (selectedValue === 'new') {
            // إعادة تعيين القيمة المحددة إلى الخيار الافتراضي
            this.value = '';

            // فتح النافذة المنبثقة لإضافة منتج جديد
            const modal = new bootstrap.Modal(document.getElementById('addProductModal'));

            // تخزين مرجع للصف الحالي في النافذة المنبثقة
            document.getElementById('addProductModal').dataset.currentRow = row.id;

            modal.show();
        } else if (selectedValue !== '') {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.dataset.price || 0;
            const taxRate = selectedOption.dataset.taxRate || 0;

            row.querySelector('.price-input').value = price;
            row.querySelector('.tax-rate-input').value = taxRate;

            calculateRowTotal(row);
            updateInvoiceSummary();
        }
    });

    productCell.appendChild(productSelect);
    row.appendChild(productCell);

    // عمود الكمية
    const quantityCell = document.createElement('td');
    const quantityInput = document.createElement('input');
    quantityInput.type = 'number';
    quantityInput.className = 'form-control quantity-input';
    quantityInput.name = 'quantity[]';
    quantityInput.min = '1';
    quantityInput.value = product ? product.quantity : '1';
    quantityInput.required = true;
    quantityInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    quantityCell.appendChild(quantityInput);
    row.appendChild(quantityCell);

    // عمود السعر
    const priceCell = document.createElement('td');
    const priceInput = document.createElement('input');
    priceInput.type = 'number';
    priceInput.className = 'form-control price-input';
    priceInput.name = 'price[]';
    priceInput.step = '0.01';
    priceInput.min = '0';
    priceInput.value = product ? product.unit_price : '0';
    priceInput.required = true;
    priceInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    priceCell.appendChild(priceInput);
    row.appendChild(priceCell);

    // عمود الضريبة
    const taxCell = document.createElement('td');
    const taxInput = document.createElement('input');
    taxInput.type = 'number';
    taxInput.className = 'form-control tax-rate-input';
    taxInput.name = 'tax_rate[]';
    taxInput.step = '0.01';
    taxInput.min = '0';
    taxInput.value = product ? product.tax_rate : '0';
    taxInput.required = true;
    taxInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    taxCell.appendChild(taxInput);
    row.appendChild(taxCell);

    // عمود المجموع
    const totalCell = document.createElement('td');
    totalCell.className = 'row-total';
    totalCell.textContent = '0.00 ر.س';
    row.appendChild(totalCell);

    // عمود الإجراءات
    const actionsCell = document.createElement('td');
    const deleteBtn = document.createElement('button');
    deleteBtn.type = 'button';
    deleteBtn.className = 'btn btn-sm btn-danger';
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
    deleteBtn.addEventListener('click', function() {
        row.remove();
        updateInvoiceSummary();
    });
    actionsCell.appendChild(deleteBtn);
    row.appendChild(actionsCell);

    tbody.appendChild(row);

    // إذا تم تمرير منتج، احسب المجموع للصف
    if (product) {
        calculateRowTotal(row);
    }

    return row;
}

// دالة لحساب مجموع الصف
function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.price-input').value) || 0;
    const taxRate = parseFloat(row.querySelector('.tax-rate-input').value) || 0;

    const subtotal = quantity * price;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    row.querySelector('.row-total').textContent = total.toFixed(2) + ' ر.س';
}

// دالة لتحديث ملخص الفاتورة
function updateInvoiceSummary() {
    let subtotal = 0;
    let taxAmount = 0;

    document.querySelectorAll('#itemsBody tr').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(row.querySelector('.price-input').value) || 0;
        const taxRate = parseFloat(row.querySelector('.tax-rate-input').value) || 0;

        const rowSubtotal = quantity * price;
        const rowTax = rowSubtotal * (taxRate / 100);

        subtotal += rowSubtotal;
        taxAmount += rowTax;
    });

    const total = subtotal + taxAmount;

    document.getElementById('subtotalCell').textContent = subtotal.toFixed(2) + ' ر.س';
    document.getElementById('taxCell').textContent = taxAmount.toFixed(2) + ' ر.س';
    document.getElementById('totalCell').textContent = total.toFixed(2) + ' ر.س';

    // تحديث حقول الدفع تلقائياً
    updatePaymentFields();
}

// دالة لتحديث حقول الدفع بناءً على حالة الدفع
function updatePaymentFields() {
    const paymentStatus = document.getElementById('payment_status').value;
    const paidAmountField = document.getElementById('paid_amount');
    const paymentDateField = document.getElementById('payment_date');
    const totalAmount = parseFloat(document.getElementById('totalCell').textContent.replace(' ر.س', '')) || 0;

    if (paymentStatus === 'paid') {
        // إذا كان مدفوع بالكامل، اجعل المبلغ المدفوع = الإجمالي
        paidAmountField.value = totalAmount.toFixed(2);
        paymentDateField.required = true;
    } else if (paymentStatus === 'unpaid') {
        // إذا كان غير مدفوع، اجعل المبلغ المدفوع = 0
        paidAmountField.value = '0.00';
        paymentDateField.required = false;
    } else if (paymentStatus === 'partial') {
        // إذا كان مدفوع جزئياً، اتركه كما هو أو اجعله نصف المبلغ
        if (parseFloat(paidAmountField.value) === 0) {
            paidAmountField.value = (totalAmount / 2).toFixed(2);
        }
        paymentDateField.required = true;
    }

    calculateRemainingAmount();
}

// دالة لحساب المبلغ المتبقي
function calculateRemainingAmount() {
    const totalAmount = parseFloat(document.getElementById('totalCell').textContent.replace(' ر.س', '')) || 0;
    const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
    const remainingAmount = totalAmount - paidAmount;

    // عرض المبلغ المتبقي في ملخص الفاتورة
    let remainingRow = document.getElementById('remainingRow');
    if (!remainingRow) {
        // إنشاء صف المبلغ المتبقي إذا لم يكن موجود
        const summaryTable = document.querySelector('#totalCell').closest('table');
        remainingRow = document.createElement('tr');
        remainingRow.id = 'remainingRow';
        remainingRow.innerHTML = '<th>المبلغ المتبقي:</th><td id="remainingCell">0.00 ر.س</td>';
        summaryTable.appendChild(remainingRow);
    }

    document.getElementById('remainingCell').textContent = remainingAmount.toFixed(2) + ' ر.س';

    // تغيير لون المبلغ المتبقي
    const remainingCell = document.getElementById('remainingCell');
    if (remainingAmount > 0) {
        remainingCell.className = 'text-danger fw-bold';
    } else {
        remainingCell.className = 'text-success fw-bold';
    }
}

// دالة لإضافة منتج جديد إلى قاعدة البيانات
async function saveNewProduct() {
    const productName = document.getElementById('new_product_name').value.trim();
    const productPrice = parseFloat(document.getElementById('new_product_price').value) || 0;
    const productTaxRate = parseFloat(document.getElementById('new_product_tax_rate').value) || 0;

    if (!productName) {
        alert('يرجى إدخال اسم المنتج');
        return;
    }

    try {
        // إرسال بيانات المنتج الجديد إلى الخادم
        const formData = new FormData();
        formData.append('name', productName);
        formData.append('price', productPrice);
        formData.append('tax_rate', productTaxRate);
        formData.append('action', 'add_product');

        const response = await fetch('ajax_handler.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // إضافة المنتج الجديد إلى مصفوفة المنتجات
            const newProduct = {
                id: result.product_id,
                name: productName,
                price: productPrice,
                tax_rate: productTaxRate
            };

            products.push(newProduct);

            // تحديث جميع القوائم المنسدلة للمنتجات
            updateProductDropdowns(newProduct);

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            modal.hide();

            // إعادة تعيين نموذج إضافة المنتج
            document.getElementById('addProductForm').reset();

            // عرض رسالة نجاح
            alert('تم إضافة المنتج بنجاح');
        } else {
            alert('حدث خطأ أثناء إضافة المنتج: ' + (result.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('Error saving product:', error);
        alert('حدث خطأ أثناء إضافة المنتج');
    }
}

// دالة لتحديث القوائم المنسدلة للمنتجات بعد إضافة منتج جديد
function updateProductDropdowns(newProduct) {
    // الحصول على الصف الحالي من النافذة المنبثقة
    const currentRowId = document.getElementById('addProductModal').dataset.currentRow;
    const currentRow = document.getElementById(currentRowId);

    // تحديث جميع القوائم المنسدلة للمنتجات
    document.querySelectorAll('.product-select').forEach(select => {
        // التحقق مما إذا كان المنتج موجود بالفعل في القائمة
        let exists = false;
        for (let i = 0; i < select.options.length; i++) {
            if (select.options[i].value == newProduct.id) {
                exists = true;
                break;
            }
        }

        // إذا لم يكن المنتج موجودًا، أضفه إلى القائمة
        if (!exists) {
            const option = document.createElement('option');
            option.value = newProduct.id;
            option.textContent = newProduct.name;
            option.dataset.price = newProduct.price;
            option.dataset.taxRate = newProduct.tax_rate;

            // إضافة الخيار قبل الخيار الأخير (إضافة منتج جديد)
            select.insertBefore(option, select.options[select.options.length - 1]);
        }
    });

    // إذا كان الصف الحالي موجودًا، حدد المنتج الجديد وحدث الحقول
    if (currentRow) {
        const select = currentRow.querySelector('.product-select');
        select.value = newProduct.id;

        // تحديث حقول السعر والضريبة
        currentRow.querySelector('.price-input').value = newProduct.price;
        currentRow.querySelector('.tax-rate-input').value = newProduct.tax_rate;

        // حساب المجموع
        calculateRowTotal(currentRow);
        updateInvoiceSummary();
    }
}

// دالة لإضافة عميل جديد إلى قاعدة البيانات
async function saveNewCustomer() {
    const customerName = document.getElementById('new_customer_name').value.trim();
    const customerPhone = document.getElementById('new_customer_phone').value.trim();
    const customerEmail = document.getElementById('new_customer_email').value.trim();
    const customerTaxNumber = document.getElementById('new_customer_tax_number').value.trim();
    const customerAddress = document.getElementById('new_customer_address').value.trim();

    if (!customerName) {
        alert('يرجى إدخال اسم المورد');
        return;
    }

    if (customerName.length < 2) {
        alert('اسم المورد يجب أن يكون أكثر من حرف واحد');
        return;
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (customerEmail && !isValidEmail(customerEmail)) {
        alert('البريد الإلكتروني غير صحيح');
        return;
    }

    try {
        // إرسال بيانات العميل الجديد إلى الخادم
        const formData = new FormData();
        formData.append('name', customerName);
        formData.append('phone', customerPhone);
        formData.append('email', customerEmail);
        formData.append('tax_number', customerTaxNumber);
        formData.append('address', customerAddress);
        formData.append('customer_type', 'supplier'); // تحديد نوع المورد
        formData.append('action', 'add_customer');

        const response = await fetch('ajax_handler.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const result = await response.json();

        if (result.success) {
            // إضافة العميل الجديد إلى القائمة المنسدلة
            const customerSelect = document.getElementById('customer_id');
            const option = document.createElement('option');
            option.value = result.customer_id;
            option.textContent = customerName;
            option.selected = true;

            // إضافة الخيار قبل الخيار الأخير (إضافة عميل جديد)
            customerSelect.insertBefore(option, customerSelect.options[1]);

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCustomerModal'));
            modal.hide();

            // إعادة تعيين نموذج إضافة العميل
            document.getElementById('addCustomerForm').reset();

            // عرض رسالة نجاح
            alert('تم إضافة العميل بنجاح');
        } else {
            console.error('Server error:', result);
            alert('حدث خطأ أثناء إضافة العميل: ' + (result.message || 'خطأ غير معروف'));

            // في حالة الخطأ، يمكن استخدام ملف التشخيص
            if (result.message && result.message.includes('جدول العملاء غير موجود')) {
                if (confirm('يبدو أن جدول العملاء غير موجود. هل تريد الانتقال لإنشاء الجداول؟')) {
                    window.open('check_tables.php', '_blank');
                }
            }
        }
    } catch (error) {
        console.error('Error saving customer:', error);
        alert('حدث خطأ في الشبكة أثناء إضافة العميل. يرجى المحاولة مرة أخرى.');
    }
}



// إضافة أول صف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    addItemRow();

    // معالجة زر إضافة عنصر
    document.getElementById('addItemBtn').addEventListener('click', function() {
        addItemRow();
    });

    // تحديث الملخص عند أي تغيير
    document.getElementById('itemsBody').addEventListener('input', function() {
        updateInvoiceSummary();
    });

    // معالجة زر حفظ المنتج الجديد
    document.getElementById('saveNewProduct').addEventListener('click', saveNewProduct);

    // معالجة زر حفظ العميل الجديد
    document.getElementById('saveNewCustomer').addEventListener('click', saveNewCustomer);

    // معالجة تغيير قائمة العملاء
    document.getElementById('customer_id').addEventListener('change', function() {
        if (this.value === 'new') {
            // إعادة تعيين القيمة المحددة إلى الخيار الافتراضي
            this.value = '';

            // فتح النافذة المنبثقة لإضافة عميل جديد
            const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
            modal.show();
        }
    });
});

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>

<?php require_once 'includes/footer.php'; ?>