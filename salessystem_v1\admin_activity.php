<?php
/**
 * صفحة سجل العمليات والأنشطة
 */
require_once __DIR__ . '/config/simple_db_config.php';
session_start();
// التحقق من تسجيل دخول المدير
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header("Location: work_management_login.php");
    exit();
}

// التحقق من الصلاحيات

// التحقق من الصلاحيات

try {
    $db = getSimpleDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }
// معاملات البحث والفلترة
$search = $_GET[
    'search'
] ?? '';
$user_type_filter = $_GET[
    'user_type'
] ?? '';
$action_filter = $_GET[
    'action'
] ?? '';
$date_from = $_GET[
    'date_from'
] ?? '';
$date_to = $_GET[
    'date_to'
] ?? '';
$page = max(1, intval($_GET[
    'page'
] ?? 1));
$per_page = 50;
$offset = ($page - 1) * $per_page;
// بناء شروط البحث
$where_conditions = [];
$params = [];
$param_types = '';
if (!empty($search)) {
    $where_conditions[] = "(al.action LIKE ? OR al.description LIKE ? OR u.full_name LIKE ? OR a.full_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'ssss';
}
if (!empty($user_type_filter)) {
    $where_conditions[] = "al.user_type = ?";
    $params[] = $user_type_filter;
    $param_types .= 's';
}
if (!empty($action_filter)) {
    $where_conditions[] = "al.action LIKE ?";
    $params[] = "%$action_filter%";
    $param_types .= 's';
}
if (!empty($date_from)) {
    $where_conditions[] = "DATE(al.created_at) >= ?";
    $params[] = $date_from;
    $param_types .= 's';
}
if (!empty($date_to)) {
    $where_conditions[] = "DATE(al.created_at) <= ?";
    $params[] = $date_to;
    $param_types .= 's';
}
$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
// استعلام العد الإجمالي
$count_query = "SELECT COUNT(*) as total FROM activity_log al
                LEFT JOIN users u ON al.user_id = u.id AND al.user_type = 'user'
                LEFT JOIN admins a ON al.user_id = a.id AND al.user_type = 'admin'
                $where_clause";
    if (!empty($params)) {
        $count_stmt = $db->prepare($count_query);
        if (!$count_stmt) {
            error_log("Database error: " . $db->error . " Query: " . $count_query);
            throw new Exception("خطأ في إعداد استعلام العد");
        }
        $count_stmt->bind_param($param_types, ...$params);
        $count_stmt->execute();
        $total_records = $count_stmt->get_result()->fetch_assoc()[
    'total'
];
        $count_stmt->close();
    }
else {
        $count_result = $db->query($count_query);
        if (!$count_result) {
            error_log("Database error: " . $db->error . " Query: " . $count_query);
            throw new Exception("خطأ في تنفيذ استعلام العد");
        }
        $total_records = $count_result->fetch_assoc()[
    'total'
];
    }
$total_pages = ceil($total_records / $per_page);
// استعلام البيانات الرئيسي
$query = "SELECT al.*,
          CASE
              WHEN al.user_type = 'admin' THEN a.full_name
              ELSE u.full_name
          END as user_name,
          CASE
              WHEN al.user_type = 'admin' THEN a.username
              ELSE u.username
          END as username
          FROM activity_log al
          LEFT JOIN users u ON al.user_id = u.id AND al.user_type = 'user'
          LEFT JOIN admins a ON al.user_id = a.id AND al.user_type = 'admin'
          $where_clause
          ORDER BY al.created_at DESC
          LIMIT $per_page OFFSET $offset";
    if (!empty($params)) {
        $stmt = $db->prepare($query);
        if (!$stmt) {
            error_log("Database error: " . $db->error . " Query: " . $query);
            throw new Exception("خطأ في إعداد استعلام البيانات");
        }
        $stmt->bind_param($param_types, ...$params);
        $stmt->execute();
        $activities_result = $stmt->get_result();
    }
else {
        $activities_result = $db->query($query);
        if (!$activities_result) {
            error_log("Database error: " . $db->error . " Query: " . $query);
            throw new Exception("خطأ في تنفيذ استعلام البيانات");
        }
    }
    // جلب إحصائيات سريعة
    $stats_query = "SELECT
        COUNT(*) as total_activities,
        COUNT(CASE WHEN user_type = 'user' THEN 1 END) as user_activities,
        COUNT(CASE WHEN user_type = 'admin' THEN 1 END) as admin_activities,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities
        FROM activity_log";
    $stats_result = $db->query($stats_query);
    if (!$stats_result) {
        error_log("Database error: " . $db->error . " Query: " . $stats_query);
        $stats = [
    'total_activities' => 0, 'user_activities' => 0, 'admin_activities' => 0, 'today_activities' => 0];
    }
else {
        $stats = $stats_result->fetch_assoc();
    }
}
catch (Exception $e) {
    ErrorHandler::logError('ERROR',
    'Admin activity page error: ' . $e->getMessage(), __FILE__, __LINE__);
    $_SESSION[
    'error'
] = 'حدث خطأ في تحميل سجل الأنشطة: ' . $e->getMessage();
    // قيم افتراضية في حالة الخطأ
    $stats = [
    'total_activities' => 0, 'user_activities' => 0, 'admin_activities' => 0, 'today_activities' => 0];
    $total_records = 0;
    $total_pages = 0;
    $activities_result = null;
}
require_once __DIR__ . '/includes/admin_header_new.php';
?>
<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-2 fw-bold gradient-text">
                        <i class="fas fa-history me-3"></i>
                        سجل العمليات والأنشطة
                    </h1>
                    <p class="text-muted mb-0">مراقبة وتتبع جميع أنشطة المستخدمين والمديرين</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="modern-btn modern-btn-success" onclick="exportData('excel',
    'admin_export.php?type=activity')">
                        <i class="fas fa-download"></i>
                        <span>تصدير Excel</span>
                    </button>
                    <button type="button" class="modern-btn modern-btn-outline" onclick="location.reload()">
                        <i class="fas fa-sync-alt"></i>
                        <span>تحديث</span>
                    </button>
                </div>
            </div>
            <!-- إحصائيات سريعة متطورة -->
            <div class="row g-4 mb-5">
                <!-- إجمالي العمليات -->
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">إجمالي العمليات</div>
                                <div class="luxury-value"><?php echo number_format($stats['total_activities']); ?></div>
                                <div class="luxury-change">
                                    <i class="fas fa-list me-1"></i>
                                    جميع الأنشطة
                                </div>
                            </div>
                            <div class="luxury-icon bg-primary">
                                <i class="fas fa-list"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- عمليات المستخدمين -->
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">عمليات المستخدمين</div>
                                <div class="luxury-value text-success"><?php echo number_format($stats['user_activities']); ?></div>
                                <div class="luxury-change">
                                    <i class="fas fa-users me-1"></i>
                                    أنشطة المستخدمين
                                </div>
                            </div>
                            <div class="luxury-icon bg-success">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- عمليات المديرين -->
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">عمليات المديرين</div>
                                <div class="luxury-value text-warning"><?php echo number_format($stats['admin_activities']); ?></div>
                                <div class="luxury-change">
                                    <i class="fas fa-user-shield me-1"></i>
                                    أنشطة المديرين
                                </div>
                            </div>
                            <div class="luxury-icon bg-warning">
                                <i class="fas fa-user-shield"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- عمليات اليوم -->
                <div class="col-xl-3 col-md-6">
                    <div class="luxury-stats-card card-glow">
                        <div class="luxury-content">
                            <div class="luxury-info">
                                <div class="luxury-label">عمليات اليوم</div>
                                <div class="luxury-value text-info"><?php echo number_format($stats['today_activities']); ?></div>
                                <div class="luxury-change">
                                    <i class="fas fa-calendar-day me-1"></i>
                                    أنشطة اليوم
                                </div>
                            </div>
                            <div class="luxury-icon bg-info">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- فلاتر البحث المتطورة -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث والتصفية
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="modern-form-label">البحث</label>
                            <input type="text" class="modern-form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($search); ?>"
                                   placeholder="العملية، الوصف، أو اسم المستخدم">
                        </div>
                        <div class="col-md-2">
                            <label for="user_type" class="modern-form-label">نوع المستخدم</label>
                            <select class="modern-form-control" id="user_type" name="user_type">
                                <option value="">الكل</option>
                                <option value="user" <?php echo $user_type_filter === 'user' ? 'selected' : ''; ?>>مستخدم</option>
                                <option value="admin" <?php echo $user_type_filter === 'admin' ? 'selected' : ''; ?>>مدير</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="action" class="modern-form-label">نوع العملية</label>
                            <input type="text" class="modern-form-control" id="action" name="action"
                                   value="<?php echo htmlspecialchars($action_filter); ?>"
                                   placeholder="مثل: login, create">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="modern-form-label">من تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_from" name="date_from"
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="modern-form-label">إلى تاريخ</label>
                            <input type="date" class="modern-form-control" id="date_to" name="date_to"
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="submit" class="modern-btn modern-btn-primary">
                                <i class="fas fa-search"></i>
                                <span>بحث</span>
                            </button>
                        </div>
</form>
                </div>
            </div>
            <!-- جدول العمليات المتطور -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-list me-2"></i>سجل العمليات
                        </h5>
                        <span class="modern-badge modern-badge-primary">إجمالي: <?php echo number_format($total_records); ?> عملية</span>
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="table-responsive">
                        <table class="modern-table table mb-0">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المستخدم</th>
                                    <th>النوع</th>
                                    <th>العملية</th>
                                    <th>الجدول</th>
                                    <th>الوصف</th>
                                    <th>IP Address</th>
</tr>
                            </thead>
                            <tbody>
                                <?php if ($activities_result && $activities_result->num_rows > 0): ?>
<?php while ($activity = $activities_result->fetch_assoc()): ?>
                                    <tr>
                                        <td>
                                            <small><?php echo date('Y-m-d H:i:s', strtotime($activity[
    'created_at'
])); ?></small>
                                        </td>
                                        <td>
                                            <div><?php echo htmlspecialchars($activity[
    'user_name'
] ?? 'غير محدد'); ?></div>
                                            <?php if (!empty($activity[
    'username'
])): ?>
                                                <small class="text-muted">@<?php echo htmlspecialchars($activity[
    'username'
]); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $activity[
    'user_type'
] === 'admin' ? 'bg-danger' : 'bg-primary'; ?>">
                                                <i class="fas <?php echo $activity[
    'user_type'
] === 'admin' ? 'fa-user-shield' : 'fa-user'; ?> me-1"></i>
                                                <?php echo $activity[
    'user_type'
] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $action_icons = [
    'user_login' => 'fas fa-sign-in-alt text-success',
    'user_logout' => 'fas fa-sign-out-alt text-warning',
    'create' => 'fas fa-plus text-success',
    'update' => 'fas fa-edit text-primary',
    'delete' => 'fas fa-trash text-danger',
    'view' => 'fas fa-eye text-info'
];
                                            $icon = $action_icons[$activity[
    'action'
]] ?? 'fas fa-circle text-secondary';
                                            ?>
                                            <i class="<?php echo $icon; ?> me-1"></i>
                                            <code><?php echo htmlspecialchars($activity[
    'action'
]); ?></code>
                                        </td>
                                        <td>
                                            <?php if (!empty($activity[
    'table_name'
])): ?>
                                                <span class="modern-badge modern-badge-secondary"><?php echo htmlspecialchars($activity[
    'table_name'
]); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($activity[
    'description'
] ?? 'لا يوجد وصف'); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo htmlspecialchars($activity['ip_address'] ?? '-'); ?></small>
                                        </td>
</tr>
                                    <?php endwhile; ?>
<?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-history fa-3x mb-3"></i>
                                                <h5>لا توجد أنشطة</h5>
                                                <p>لم يتم العثور على أنشطة مطابقة لمعايير البحث</p>
                                            </div>
                                        </td>
</tr>
                                <?php endif; ?>
                            </tbody>
</table>
                    </div>
                    <!-- التنقل بين الصفحات -->
                    <?php if ($total_pages > 1): ?>
<?php
                    // بناء معاملات الاستعلام بدون معامل page
                    $query_params = $_GET;
                    unset($query_params['page']);
                    $query_string = !empty($query_params) ? '&' . http_build_query($query_params) : '';
                    ?>
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page-1; ?>
<?php echo $query_string; ?>">السابق</a>
                            </li>
                            <?php endif; ?>
<?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>
<?php echo $query_string; ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
<?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page+1; ?>
<?php echo $query_string; ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main></div>
<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>