<?php
/**
 * صفحة عرض تفاصيل الفاتورة للمدير
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

global $main_db;

$user_id = intval($_GET['user_id'] ?? 0);
$invoice_type = $_GET['type'] ?? 'sales';
$invoice_id = intval($_GET['id'] ?? 0);

// جلب معلومات المستخدم
$user_info = null;
if ($user_id > 0) {
    $stmt = $main_db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $user_info = $stmt->get_result()->fetch_assoc();
    $stmt->close();
}

if (!$user_info) {
    $_SESSION['error'] = 'المستخدم غير موجود';
    header("Location: admin_users.php");
    exit();
}

// الاتصال بقاعدة بيانات المستخدم
$user_db_name = "sales_system_user_" . $user_id;
$user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);

if ($user_db->connect_error) {
    $_SESSION['error'] = 'لا يمكن الوصول لقاعدة بيانات المستخدم';
    header("Location: admin_users.php");
    exit();
}

// جلب تفاصيل الفاتورة
$invoice = null;
$invoice_items = [];

if ($invoice_type === 'sales') {
    $stmt = $user_db->prepare("SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email, c.address as customer_address 
                               FROM sales s 
                               LEFT JOIN customers c ON s.customer_id = c.id 
                               WHERE s.id = ?");
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $invoice = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    if ($invoice) {
        // جلب عناصر الفاتورة (إذا كان هناك جدول منفصل للعناصر)
        $items_result = $user_db->query("SHOW TABLES LIKE 'sales_items'");
        if ($items_result->num_rows > 0) {
            $stmt = $user_db->prepare("SELECT si.*, p.name as product_name 
                                       FROM sales_items si 
                                       LEFT JOIN products p ON si.product_id = p.id 
                                       WHERE si.sales_id = ?");
            $stmt->bind_param("i", $invoice_id);
            $stmt->execute();
            $items_result = $stmt->get_result();
            while ($item = $items_result->fetch_assoc()) {
                $invoice_items[] = $item;
            }
            $stmt->close();
        }
    }
} else {
    $stmt = $user_db->prepare("SELECT * FROM purchases WHERE id = ?");
    $stmt->bind_param("i", $invoice_id);
    $stmt->execute();
    $invoice = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    if ($invoice) {
        // جلب عناصر المشتريات (إذا كان هناك جدول منفصل للعناصر)
        $items_result = $user_db->query("SHOW TABLES LIKE 'purchase_items'");
        if ($items_result->num_rows > 0) {
            $stmt = $user_db->prepare("SELECT pi.*, p.name as product_name 
                                       FROM purchase_items pi 
                                       LEFT JOIN products p ON pi.product_id = p.id 
                                       WHERE pi.purchase_id = ?");
            $stmt->bind_param("i", $invoice_id);
            $stmt->execute();
            $items_result = $stmt->get_result();
            while ($item = $items_result->fetch_assoc()) {
                $invoice_items[] = $item;
            }
            $stmt->close();
        }
    }
}

if (!$invoice) {
    $_SESSION['error'] = 'الفاتورة غير موجودة';
    header("Location: admin_invoice_details.php?user_id=" . $user_id);
    exit();
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_financial_reports.php">
                            <i class="fas fa-chart-line me-2"></i>التقارير المالية
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-file-invoice me-2 text-primary"></i>
                    تفاصيل فاتورة <?php echo $invoice_type === 'sales' ? 'المبيعات' : 'المشتريات'; ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="admin_invoice_details.php?user_id=<?php echo $user_id; ?>&type=<?php echo $invoice_type; ?>" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>العودة للفواتير
                    </a>
                    <button type="button" class="btn btn-sm btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>طباعة
                    </button>
                </div>
            </div>

            <!-- تفاصيل الفاتورة -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-file-invoice me-2"></i>معلومات الفاتورة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th>رقم الفاتورة:</th>
                                            <td><strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong></td>
                                        </tr>
                                        <tr>
                                            <th>التاريخ:</th>
                                            <td><?php echo date('Y-m-d', strtotime($invoice['date'])); ?></td>
                                        </tr>
                                        <?php if ($invoice_type === 'sales'): ?>
                                        <tr>
                                            <th>العميل:</th>
                                            <td><?php echo htmlspecialchars($invoice['customer_name'] ?? 'غير محدد'); ?></td>
                                        </tr>
                                        <tr>
                                            <th>هاتف العميل:</th>
                                            <td><?php echo htmlspecialchars($invoice['customer_phone'] ?? '-'); ?></td>
                                        </tr>
                                        <?php else: ?>
                                        <tr>
                                            <th>المورد:</th>
                                            <td><?php echo htmlspecialchars($invoice['supplier_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <th>هاتف المورد:</th>
                                            <td><?php echo htmlspecialchars($invoice['supplier_phone'] ?? '-'); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th>المبلغ الفرعي:</th>
                                            <td><?php echo number_format($invoice['subtotal'], 2); ?> ر.س</td>
                                        </tr>
                                        <tr>
                                            <th>الضريبة:</th>
                                            <td><?php echo number_format($invoice['tax_amount'], 2); ?> ر.س</td>
                                        </tr>
                                        <tr>
                                            <th>الخصم:</th>
                                            <td><?php echo number_format($invoice['discount_amount'] ?? 0, 2); ?> ر.س</td>
                                        </tr>
                                        <tr>
                                            <th><strong>الإجمالي:</strong></th>
                                            <td><strong class="text-success"><?php echo number_format($invoice['total_amount'], 2); ?> ر.س</strong></td>
                                        </tr>
                                        <tr>
                                            <th>حالة الدفع:</th>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                switch ($invoice['payment_status']) {
                                                    case 'paid':
                                                        $status_class = 'bg-success';
                                                        $status_text = 'مدفوع';
                                                        break;
                                                    case 'partial':
                                                        $status_class = 'bg-warning';
                                                        $status_text = 'مدفوع جزئياً';
                                                        break;
                                                    default:
                                                        $status_class = 'bg-danger';
                                                        $status_text = 'غير مدفوع';
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>طريقة الدفع:</th>
                                            <td><?php echo htmlspecialchars($invoice['payment_method'] ?? 'نقدي'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <?php if (!empty($invoice['notes'])): ?>
                            <div class="mt-3">
                                <h6>ملاحظات:</h6>
                                <p class="text-muted"><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- عناصر الفاتورة -->
                    <?php if (!empty($invoice_items)): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>عناصر الفاتورة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($invoice_items as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['product_name'] ?? 'منتج غير محدد'); ?></td>
                                            <td><?php echo number_format($item['quantity']); ?></td>
                                            <td><?php echo number_format($item['unit_price'], 2); ?> ر.س</td>
                                            <td><?php echo number_format($item['quantity'] * $item['unit_price'], 2); ?> ر.س</td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-lg-4">
                    <!-- معلومات المستخدم -->
                    <div class="card shadow mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-user me-2"></i>معلومات المستخدم
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <th>الاسم:</th>
                                    <td><?php echo htmlspecialchars($user_info['full_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>البريد:</th>
                                    <td><?php echo htmlspecialchars($user_info['email']); ?></td>
                                </tr>
                                <tr>
                                    <th>الهاتف:</th>
                                    <td><?php echo htmlspecialchars($user_info['phone'] ?? '-'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if ($invoice_type === 'sales' && !empty($invoice['customer_name'])): ?>
                    <!-- معلومات العميل -->
                    <div class="card shadow mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-user-friends me-2"></i>معلومات العميل
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <th>الاسم:</th>
                                    <td><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>الهاتف:</th>
                                    <td><?php echo htmlspecialchars($invoice['customer_phone'] ?? '-'); ?></td>
                                </tr>
                                <?php if (!empty($invoice['customer_email'])): ?>
                                <tr>
                                    <th>البريد:</th>
                                    <td><?php echo htmlspecialchars($invoice['customer_email']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($invoice['customer_address'])): ?>
                                <tr>
                                    <th>العنوان:</th>
                                    <td><?php echo htmlspecialchars($invoice['customer_address']); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- إجراءات سريعة -->
                    <div class="card shadow">
                        <div class="card-header bg-warning text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-tools me-2"></i>إجراءات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="admin_invoice_print.php?user_id=<?php echo $user_id; ?>&type=<?php echo $invoice_type; ?>&id=<?php echo $invoice_id; ?>" 
                                   class="btn btn-primary btn-sm" target="_blank">
                                    <i class="fas fa-print me-1"></i>طباعة الفاتورة
                                </a>
                                <a href="admin_invoice_details.php?user_id=<?php echo $user_id; ?>&type=<?php echo $invoice_type; ?>" 
                                   class="btn btn-secondary btn-sm">
                                    <i class="fas fa-list me-1"></i>جميع الفواتير
                                </a>
                                <a href="admin_user_details.php?id=<?php echo $user_id; ?>" 
                                   class="btn btn-info btn-sm">
                                    <i class="fas fa-user me-1"></i>ملف المستخدم
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>


<?php 
$user_db->close();
require_once __DIR__ . '/includes/admin_footer.php'; 
?>
