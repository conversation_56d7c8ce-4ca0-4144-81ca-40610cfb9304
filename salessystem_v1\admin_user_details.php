<?php
/**
 * صفحة تفاصيل المستخدم للمدير
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

$user_id = intval($_GET['id'] ?? 0);
if ($user_id <= 0) {
    $_SESSION['error'] = 'معرف المستخدم غير صحيح';
    header("Location: admin_users.php");
    exit();
}

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // جلب بيانات المستخدم
    $user_query = "SELECT * FROM users WHERE id = ?";
    $user_stmt = $db->prepare($user_query);
    $user_stmt->bind_param("i", $user_id);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    $user = $user_result->fetch_assoc();

    if (!$user) {
        $_SESSION['error'] = 'المستخدم غير موجود';
        header("Location: admin_users.php");
        exit();
    }

    // جلب إحصائيات المستخدم
    $stats = [];
    
    // إحصائيات المبيعات
    $sales_query = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE user_id = ?";
    $sales_stmt = $db->prepare($sales_query);
    $sales_stmt->bind_param("i", $user_id);
    $sales_stmt->execute();
    $stats['sales'] = $sales_stmt->get_result()->fetch_assoc();

    // إحصائيات المشتريات
    $purchases_query = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM purchases WHERE user_id = ?";
    $purchases_stmt = $db->prepare($purchases_query);
    $purchases_stmt->bind_param("i", $user_id);
    $purchases_stmt->execute();
    $stats['purchases'] = $purchases_stmt->get_result()->fetch_assoc();

    // إحصائيات العملاء
    $customers_query = "SELECT COUNT(*) as count FROM customers WHERE user_id = ?";
    $customers_stmt = $db->prepare($customers_query);
    $customers_stmt->bind_param("i", $user_id);
    $customers_stmt->execute();
    $stats['customers'] = $customers_stmt->get_result()->fetch_assoc();

    // آخر الأنشطة
    $activity_query = "SELECT * FROM activity_log WHERE user_id = ? ORDER BY created_at DESC LIMIT 10";
    $activity_stmt = $db->prepare($activity_query);
    $activity_stmt->bind_param("i", $user_id);
    $activity_stmt->execute();
    $activities = $activity_stmt->get_result();

} catch (Exception $e) {
    $_SESSION['error'] = 'حدث خطأ في النظام: ' . $e->getMessage();
    header("Location: admin_users.php");
    exit();
}

require_once __DIR__ . '/includes/admin_header_new.php';
?>

<div class="admin-layout">
    <!-- الشريط الجانبي -->
    <nav class="modern-sidebar d-none d-lg-block">
        <div class="sidebar-section">
            <div class="sidebar-section-title">الإدارة الرئيسية</div>
            <a class="sidebar-nav-link" href="admin_dashboard.php">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a class="sidebar-nav-link active" href="admin_users.php">
                <i class="fas fa-users"></i>
                <span>إدارة المستخدمين</span>
            </a>
            <a class="sidebar-nav-link" href="admin_activity.php">
                <i class="fas fa-history"></i>
                <span>سجل العمليات</span>
            </a>
        </div>
        <div class="sidebar-section">
            <div class="sidebar-section-title">التقارير والإحصائيات</div>
            <a class="sidebar-nav-link" href="admin_reports.php">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير الشاملة</span>
            </a>
            <a class="sidebar-nav-link" href="admin_financial.php">
                <i class="fas fa-file-invoice-dollar"></i>
                <span>التقارير المالية</span>
            </a>
        </div>
        <div class="sidebar-section">
            <div class="sidebar-section-title">إدارة النظام</div>
            <a class="sidebar-nav-link" href="admin_error_logs.php">
                <i class="fas fa-exclamation-triangle"></i>
                <span>سجل الأخطاء</span>
            </a>
            <a class="sidebar-nav-link" href="admin_system.php">
                <i class="fas fa-cogs"></i>
                <span>إعدادات النظام</span>
            </a>
        </div>
        <div class="sidebar-section">
            <div class="sidebar-section-title">إدارة المديرين</div>
            <a class="sidebar-nav-link" href="admin_manage_admins.php">
                <i class="fas fa-user-shield"></i>
                <span>إدارة المديرين</span>
            </a>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="admin-content">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h2 mb-2 fw-bold gradient-text">
                    <i class="fas fa-user me-3"></i>
                    تفاصيل المستخدم
                </h1>
                <p class="text-muted mb-0">عرض تفاصيل وإحصائيات المستخدم</p>
            </div>
            <div class="d-flex gap-2">
                <a href="admin_users.php" class="modern-btn modern-btn-outline">
                    <i class="fas fa-arrow-left"></i>
                    <span>العودة</span>
                </a>
                <a href="admin_financial.php?user_id=<?php echo $user['id']; ?>" class="modern-btn modern-btn-primary">
                    <i class="fas fa-chart-line"></i>
                    <span>التقرير المالي</span>
                </a>
            </div>
        </div>

        <!-- معلومات المستخدم -->
        <div class="row g-4 mb-4">
            <div class="col-md-4">
                <div class="modern-card">
                    <div class="modern-card-body text-center">
                        <div class="avatar-lg mx-auto mb-3">
                            <div class="avatar-initial bg-primary rounded-circle">
                                <?php echo strtoupper(substr($user['full_name'] ?? $user['username'], 0, 1)); ?>
                            </div>
                        </div>
                        <h5 class="mb-1"><?php echo htmlspecialchars($user['full_name'] ?? 'غير محدد'); ?></h5>
                        <p class="text-muted mb-2">@<?php echo htmlspecialchars($user['username']); ?></p>
                        <span class="badge <?php echo $user['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?>">
                            <i class="fas <?php echo $user['status'] === 'active' ? 'fa-check' : 'fa-times'; ?> me-1"></i>
                            <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-info-circle me-2"></i>
                            المعلومات الشخصية
                        </h5>
                    </div>
                    <div class="modern-card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-muted">البريد الإلكتروني</label>
                                <p class="mb-0"><?php echo htmlspecialchars($user['email'] ?? 'غير محدد'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">رقم الهاتف</label>
                                <p class="mb-0"><?php echo htmlspecialchars($user['phone'] ?? 'غير محدد'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">تاريخ التسجيل</label>
                                <p class="mb-0"><?php echo date('Y-m-d H:i:s', strtotime($user['created_at'])); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">آخر دخول</label>
                                <p class="mb-0">
                                    <?php echo $user['last_login'] ? date('Y-m-d H:i:s', strtotime($user['last_login'])) : 'لم يسجل دخول'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-1">
                            <div class="stats-label">إجمالي المبيعات</div>
                            <div class="stats-value text-success"><?php echo number_format($stats['sales']['total'], 2); ?> ريال</div>
                            <div class="stats-change text-muted">
                                <i class="fas fa-file-invoice me-1"></i>
                                <?php echo $stats['sales']['count']; ?> فاتورة
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-1">
                            <div class="stats-label">إجمالي المشتريات</div>
                            <div class="stats-value text-danger"><?php echo number_format($stats['purchases']['total'], 2); ?> ريال</div>
                            <div class="stats-change text-muted">
                                <i class="fas fa-file-invoice me-1"></i>
                                <?php echo $stats['purchases']['count']; ?> فاتورة
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-1">
                            <div class="stats-label">صافي الربح</div>
                            <div class="stats-value <?php echo ($stats['sales']['total'] - $stats['purchases']['total']) >= 0 ? 'text-success' : 'text-danger'; ?>">
                                <?php echo number_format($stats['sales']['total'] - $stats['purchases']['total'], 2); ?> ريال
                            </div>
                            <div class="stats-change text-muted">
                                <i class="fas fa-coins me-1"></i>
                                <?php echo ($stats['sales']['total'] - $stats['purchases']['total']) >= 0 ? 'ربح' : 'خسارة'; ?>
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-1">
                            <div class="stats-label">العملاء</div>
                            <div class="stats-value text-info"><?php echo $stats['customers']['count']; ?></div>
                            <div class="stats-change text-muted">
                                <i class="fas fa-user-friends me-1"></i>
                                عميل مسجل
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-user-friends"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخر الأنشطة -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="mb-0 fw-bold">
                    <i class="fas fa-history me-2"></i>
                    آخر الأنشطة
                </h5>
            </div>
            <div class="modern-card-body p-0">
                <div class="table-responsive">
                    <table class="modern-table table mb-0">
                        <thead>
                            <tr>
                                <th>النشاط</th>
                                <th>الوصف</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($activities && $activities->num_rows > 0): ?>
                                <?php while ($activity = $activities->fetch_assoc()): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($activity['action']); ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($activity['description'] ?? 'لا يوجد وصف'); ?></td>
                                        <td>
                                            <small class="text-muted"><?php echo date('Y-m-d H:i:s', strtotime($activity['created_at'])); ?></small>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="3" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-history fa-3x mb-3"></i>
                                            <h5>لا توجد أنشطة</h5>
                                            <p>لم يتم تسجيل أي أنشطة لهذا المستخدم</p>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>
</div>


<?php require_once __DIR__ . '/includes/admin_footer_new.php'; ?>
