<?php
/**
 * معالج العمليات AJAX للنظام
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/invoice_functions.php';
redirectIfNotLoggedIn();

// التأكد من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// الحصول على نوع العملية
$action = $_POST['action'] ?? '';

// الحصول على معلومات المستخدم
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];
$db = getCurrentUserDB();

if (!$db) {
    echo json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات']);
    exit();
}

// معالجة العمليات المختلفة
switch ($action) {
    case 'add_customer':
        handleAddCustomer();
        break;
        
    case 'add_product':
        handleAddProduct();
        break;
        
    case 'get_customers':
        handleGetCustomers();
        break;
        
    case 'get_products':
        handleGetProducts();
        break;
        
    case 'quick_sale':
        handleQuickSale();
        break;
        
    case 'quick_purchase':
        handleQuickPurchase();
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'عملية غير مدعومة']);
        break;
}

// دالة إضافة عميل جديد
function handleAddCustomer() {
    global $username, $user_id;
    
    $name = trim($_POST['name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $tax_number = trim($_POST['tax_number'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $customer_type = $_POST['customer_type'] ?? 'customer';
    
    if (empty($name)) {
        $entity_name = $customer_type === 'supplier' ? 'المورد' : 'العميل';
        echo json_encode(['success' => false, 'message' => "اسم $entity_name مطلوب"]);
        return;
    }
    
    $customer_data = [
        'name' => $name,
        'phone' => $phone,
        'email' => $email,
        'tax_number' => $tax_number,
        'address' => $address,
        'customer_type' => $customer_type
    ];
    
    $customer_id = insertWithUserId('customers', $customer_data, $username);
    
    if ($customer_id) {
        $entity_name = $customer_type === 'supplier' ? 'المورد' : 'العميل';
        echo json_encode([
            'success' => true,
            'customer_id' => $customer_id,
            'message' => "تم إضافة $entity_name بنجاح"
        ]);
    } else {
        $entity_name = $customer_type === 'supplier' ? 'المورد' : 'العميل';
        echo json_encode(['success' => false, 'message' => "فشل في إضافة $entity_name"]);
    }
}

// دالة إضافة منتج جديد
function handleAddProduct() {
    global $username, $user_id;
    
    $name = trim($_POST['name'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $tax_rate = floatval($_POST['tax_rate'] ?? 15);
    $category = trim($_POST['category'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        echo json_encode(['success' => false, 'message' => 'اسم المنتج مطلوب']);
        return;
    }
    
    $product_data = [
        'name' => $name,
        'price' => $price,
        'tax_rate' => $tax_rate,
        'category' => $category,
        'description' => $description
    ];
    
    $product_id = insertWithUserId('products', $product_data, $username);
    
    if ($product_id) {
        echo json_encode([
            'success' => true, 
            'product_id' => $product_id,
            'message' => 'تم إضافة المنتج بنجاح'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في إضافة المنتج']);
    }
}

// دالة جلب العملاء
function handleGetCustomers() {
    global $db, $username, $user_id;
    
    $customers_table = getUserTableName('customers', $username);
    $customers = $db->query("SELECT id, name, phone, email FROM `$customers_table` WHERE user_id = $user_id ORDER BY name");
    
    $customers_list = [];
    if ($customers) {
        while ($customer = $customers->fetch_assoc()) {
            $customers_list[] = $customer;
        }
    }
    
    echo json_encode(['success' => true, 'customers' => $customers_list]);
}

// دالة جلب المنتجات
function handleGetProducts() {
    global $db, $username, $user_id;
    
    $products_table = getUserTableName('products', $username);
    $products = $db->query("SELECT id, name, price, tax_rate FROM `$products_table` WHERE user_id = $user_id ORDER BY name");
    
    $products_list = [];
    if ($products) {
        while ($product = $products->fetch_assoc()) {
            $products_list[] = $product;
        }
    }
    
    echo json_encode(['success' => true, 'products' => $products_list]);
}

// دالة المبيعات السريعة
function handleQuickSale() {
    global $username, $user_id;
    
    $customer_id = intval($_POST['customer_id'] ?? 0);
    $product_id = intval($_POST['product_id'] ?? 0);
    $quantity = floatval($_POST['quantity'] ?? 1);
    $price = floatval($_POST['price'] ?? 0);
    $tax_rate = floatval($_POST['tax_rate'] ?? 15);
    
    if ($product_id <= 0 || $quantity <= 0 || $price <= 0) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        return;
    }
    
    // حساب المبالغ
    $subtotal = $quantity * $price;
    $tax_amount = $subtotal * ($tax_rate / 100);
    $total_amount = $subtotal + $tax_amount;
    
    // إنشاء فاتورة المبيعات باستخدام الدالة المحسنة
    $invoice_number = getNextInvoiceNumber('sales');
    
    $sale_data = [
        'customer_id' => $customer_id > 0 ? $customer_id : null,
        'invoice_number' => $invoice_number,
        'date' => date('Y-m-d'),
        'subtotal' => $subtotal,
        'tax_amount' => $tax_amount,
        'total_amount' => $total_amount,
        'payment_status' => 'paid',
        'notes' => 'مبيعات سريعة'
    ];
    
    $sale_id = insertWithUserId('sales', $sale_data, $username);
    
    if ($sale_id) {
        // إضافة عناصر الفاتورة
        $item_data = [
            'sale_id' => $sale_id,
            'product_id' => $product_id,
            'quantity' => $quantity,
            'unit_price' => $price,
            'tax_rate' => $tax_rate,
            'tax_amount' => $tax_amount,
            'total_price' => $total_amount
        ];
        
        $item_id = insertWithUserId('sale_items', $item_data, $username);
        
        if ($item_id) {
            echo json_encode([
                'success' => true, 
                'sale_id' => $sale_id,
                'invoice_number' => $invoice_number,
                'message' => 'تم إنشاء فاتورة المبيعات بنجاح'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إضافة عناصر الفاتورة']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في إنشاء فاتورة المبيعات']);
    }
}

// دالة المشتريات السريعة
function handleQuickPurchase() {
    global $username, $user_id;
    
    $customer_id = intval($_POST['customer_id'] ?? 0);
    $product_id = intval($_POST['product_id'] ?? 0);
    $quantity = floatval($_POST['quantity'] ?? 1);
    $price = floatval($_POST['price'] ?? 0);
    $tax_rate = floatval($_POST['tax_rate'] ?? 15);
    
    if ($product_id <= 0 || $quantity <= 0 || $price <= 0) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        return;
    }
    
    // حساب المبالغ
    $subtotal = $quantity * $price;
    $tax_amount = $subtotal * ($tax_rate / 100);
    $total_amount = $subtotal + $tax_amount;
    
    // إنشاء فاتورة المشتريات باستخدام الدالة المحسنة
    $invoice_number = getNextInvoiceNumber('purchase');
    
    $purchase_data = [
        'customer_id' => $customer_id > 0 ? $customer_id : null,
        'invoice_number' => $invoice_number,
        'date' => date('Y-m-d'),
        'subtotal' => $subtotal,
        'tax_amount' => $tax_amount,
        'total_amount' => $total_amount,
        'payment_status' => 'paid',
        'notes' => 'مشتريات سريعة'
    ];
    
    $purchase_id = insertWithUserId('purchases', $purchase_data, $username);
    
    if ($purchase_id) {
        // إضافة عناصر الفاتورة
        $item_data = [
            'purchase_id' => $purchase_id,
            'product_id' => $product_id,
            'quantity' => $quantity,
            'unit_price' => $price,
            'tax_rate' => $tax_rate,
            'tax_amount' => $tax_amount,
            'total_price' => $total_amount
        ];
        
        $item_id = insertWithUserId('purchase_items', $item_data, $username);
        
        if ($item_id) {
            echo json_encode([
                'success' => true, 
                'purchase_id' => $purchase_id,
                'invoice_number' => $invoice_number,
                'message' => 'تم إنشاء فاتورة المشتريات بنجاح'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إضافة عناصر الفاتورة']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في إنشاء فاتورة المشتريات']);
    }
}
?>
