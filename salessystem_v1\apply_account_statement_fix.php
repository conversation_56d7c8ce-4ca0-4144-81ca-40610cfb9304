<?php
/**
 * تطبيق إصلاح مشكلة كشف الحساب
 */

echo "<h1>🔧 تطبيق إصلاح كشف الحساب</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h2>تطبيق الإصلاحات على ملف reports.php</h2>";

$fixes_applied = 0;
$errors = [];

try {
    // قراءة الملف الحالي
    $file_path = __DIR__ . '/reports.php';
    if (!file_exists($file_path)) {
        throw new Exception('ملف reports.php غير موجود');
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        throw new Exception('فشل في قراءة ملف reports.php');
    }
    
    echo "<p class='info'>تم قراءة الملف بنجاح (" . strlen($content) . " حرف)</p>";
    
    // إنشاء نسخة احتياطية
    $backup_path = __DIR__ . '/reports_backup_' . date('Y-m-d_H-i-s') . '.php';
    if (file_put_contents($backup_path, $content) === false) {
        throw new Exception('فشل في إنشاء النسخة الاحتياطية');
    }
    
    echo "<p class='success'>✅ تم إنشاء نسخة احتياطية: " . basename($backup_path) . "</p>";
    
    // الإصلاح 1: تحسين استعلام المبيعات
    $old_sales_query = '/(\$sales_query = "SELECT\s+s\.id,\s+s\.invoice_number,\s+s\.date,\s+s\.total_amount,\s+COALESCE\(s\.subtotal, s\.total_amount\) as subtotal,\s+COALESCE\(s\.tax_amount, 0\) as tax_amount,\s+s\.payment_status,\s+COALESCE\(c\.name, \'عميل غير محدد\'\) as customer_name,\s+\'sale\' as transaction_type,\s+\'مبيعات\' as transaction_type_ar,\s+\'sales\' as source_table)/';
    
    $new_sales_query = '$sales_query = "SELECT
                    s.id,
                    s.invoice_number,
                    s.date,
                    s.total_amount,
                    COALESCE(s.subtotal, s.total_amount) as subtotal,
                    COALESCE(s.tax_amount, 0) as tax_amount,
                    s.payment_status,
                    COALESCE(c.name, \'عميل غير محدد\') as customer_name,
                    \'sale\' as transaction_type,
                    \'مبيعات\' as transaction_type_ar,
                    \'sales\' as source_table,
                    \'SALE\' as type_verification';
    
    if (preg_match($old_sales_query, $content)) {
        $content = preg_replace($old_sales_query, $new_sales_query, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم تحسين استعلام المبيعات</p>";
    }
    
    // الإصلاح 2: تحسين معالجة بيانات المبيعات
    $old_sales_processing = '/while \(\$row = \$sales_result->fetch_assoc\(\)\) \{\s+\/\/ التأكد من نوع المعاملة\s+\$row\[\'transaction_type\'\] = \'sale\';\s+\$row\[\'transaction_type_ar\'\] = \'مبيعات\';\s+\$account_transactions\[\] = \$row;\s+\}/';
    
    $new_sales_processing = 'while ($row = $sales_result->fetch_assoc()) {
            // التأكد المطلق من نوع المعاملة
            $row[\'transaction_type\'] = \'sale\';
            $row[\'transaction_type_ar\'] = \'مبيعات\';
            $row[\'source_table\'] = \'sales\';
            $row[\'type_verification\'] = \'SALE\';
            $row[\'is_sale\'] = true;
            $row[\'is_purchase\'] = false;
            $account_transactions[] = $row;
        }';
    
    if (preg_match($old_sales_processing, $content)) {
        $content = preg_replace($old_sales_processing, $new_sales_processing, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم تحسين معالجة بيانات المبيعات</p>";
    }
    
    // الإصلاح 3: تحسين استعلام المشتريات
    $old_purchases_query = '/(\$purchases_query = "SELECT\s+p\.id,\s+p\.invoice_number,\s+p\.date,\s+p\.total_amount,\s+COALESCE\(p\.subtotal, p\.total_amount\) as subtotal,\s+COALESCE\(p\.tax_amount, 0\) as tax_amount,\s+p\.payment_status,\s+COALESCE\(c\.name, p\.supplier_name, \'مورد غير محدد\'\) as customer_name,\s+\'purchase\' as transaction_type,\s+\'مشتريات\' as transaction_type_ar,\s+\'purchases\' as source_table)/';
    
    $new_purchases_query = '$purchases_query = "SELECT
                           p.id,
                           p.invoice_number,
                           p.date,
                           p.total_amount,
                           COALESCE(p.subtotal, p.total_amount) as subtotal,
                           COALESCE(p.tax_amount, 0) as tax_amount,
                           p.payment_status,
                           COALESCE(c.name, p.supplier_name, \'مورد غير محدد\') as customer_name,
                           \'purchase\' as transaction_type,
                           \'مشتريات\' as transaction_type_ar,
                           \'purchases\' as source_table,
                           \'PURCHASE\' as type_verification';
    
    if (preg_match($old_purchases_query, $content)) {
        $content = preg_replace($old_purchases_query, $new_purchases_query, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم تحسين استعلام المشتريات</p>";
    }
    
    // الإصلاح 4: تحسين معالجة بيانات المشتريات
    $old_purchases_processing = '/while \(\$row = \$purchases_result->fetch_assoc\(\)\) \{\s+\/\/ التأكد من نوع المعاملة\s+\$row\[\'transaction_type\'\] = \'purchase\';\s+\$row\[\'transaction_type_ar\'\] = \'مشتريات\';\s+\$account_transactions\[\] = \$row;\s+\}/';
    
    $new_purchases_processing = 'while ($row = $purchases_result->fetch_assoc()) {
                // التأكد المطلق من نوع المعاملة
                $row[\'transaction_type\'] = \'purchase\';
                $row[\'transaction_type_ar\'] = \'مشتريات\';
                $row[\'source_table\'] = \'purchases\';
                $row[\'type_verification\'] = \'PURCHASE\';
                $row[\'is_sale\'] = false;
                $row[\'is_purchase\'] = true;
                $account_transactions[] = $row;
            }';
    
    if (preg_match($old_purchases_processing, $content)) {
        $content = preg_replace($old_purchases_processing, $new_purchases_processing, $content);
        $fixes_applied++;
        echo "<p class='success'>✅ تم تحسين معالجة بيانات المشتريات</p>";
    }
    
    // حفظ الملف المحدث
    if (file_put_contents($file_path, $content) === false) {
        throw new Exception('فشل في حفظ الملف المحدث');
    }
    
    echo "<p class='success'>✅ تم حفظ الملف المحدث بنجاح</p>";
    echo "<p class='info'>إجمالي الإصلاحات المطبقة: $fixes_applied</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    $errors[] = $e->getMessage();
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>الخطوات التالية</h2>";

if (empty($errors)) {
    echo "<p class='success'>✅ تم تطبيق جميع الإصلاحات بنجاح!</p>";
    
    echo "<h3>اختبار الإصلاحات:</h3>";
    echo "<ul>";
    echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب</a></li>";
    echo "<li><a href='test_account_statement.php' target='_blank'>اختبار شامل لكشف الحساب</a></li>";
    echo "</ul>";
    
    echo "<h3>ما تم إصلاحه:</h3>";
    echo "<ul>";
    echo "<li><strong>تحسين استعلامات قاعدة البيانات:</strong> إضافة حقل type_verification للتحقق المزدوج</li>";
    echo "<li><strong>تحسين معالجة البيانات:</strong> إضافة معرفات is_sale و is_purchase</li>";
    echo "<li><strong>منع الخلط:</strong> التأكد من تصنيف كل معاملة بشكل صحيح</li>";
    echo "<li><strong>تحسين التتبع:</strong> إضافة معلومات إضافية لتتبع مصدر البيانات</li>";
    echo "</ul>";
    
} else {
    echo "<p class='error'>❌ حدثت أخطاء أثناء التطبيق:</p>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li class='error'>$error</li>";
    }
    echo "</ul>";
}

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم تطبيق الإصلاحات في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
