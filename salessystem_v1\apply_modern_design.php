<?php
/**
 * سكريبت تطبيق التصميم المتطور على جميع صفحات المدير
 */

// قائمة صفحات المدير التي تحتاج تحديث
$admin_pages = [
    'admin_activity.php',
    'admin_reports.php', 
    'admin_financial.php',
    'admin_error_logs.php',
    'admin_system.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$updates_applied = [];
$errors = [];

echo "<h2>تطبيق التصميم المتطور على صفحات المدير</h2>";

foreach ($admin_pages as $page) {
    $file_path = __DIR__ . '/' . $page;
    
    if (!file_exists($file_path)) {
        $errors[] = "الملف غير موجود: $page";
        continue;
    }
    
    echo "<h3>معالجة: $page</h3>";
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $page";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. تحديث header
    if (strpos($content, "includes/admin_header.php") !== false) {
        $content = str_replace(
            "require_once __DIR__ . '/includes/admin_header.php';",
            "require_once __DIR__ . '/includes/admin_header_new.php';",
            $content
        );
        $changes_made = true;
        echo "✅ تم تحديث header<br>";
    }
    
    // 2. تحديث footer
    if (strpos($content, "includes/admin_footer.php") !== false) {
        $content = str_replace(
            "require_once __DIR__ . '/includes/admin_footer.php';",
            "require_once __DIR__ . '/includes/admin_footer_new.php';",
            $content
        );
        $changes_made = true;
        echo "✅ تم تحديث footer<br>";
    }
    
    // 3. تحديث الشريط الجانبي القديم
    $old_sidebar_pattern = '/<nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">.*?<\/nav>/s';
    if (preg_match($old_sidebar_pattern, $content)) {
        // تحديد الصفحة النشطة
        $active_page = str_replace('.php', '', $page);
        $new_sidebar = generateModernSidebar($active_page);
        
        $content = preg_replace($old_sidebar_pattern, $new_sidebar, $content);
        $changes_made = true;
        echo "✅ تم تحديث الشريط الجانبي<br>";
    }
    
    // 4. تحديث المحتوى الرئيسي
    $old_main_pattern = '/<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">/';
    if (preg_match($old_main_pattern, $content)) {
        $content = preg_replace(
            $old_main_pattern,
            '<main class="col-lg-9 ms-auto px-4 py-3" style="margin-right: 300px; min-height: calc(100vh - 80px);">',
            $content
        );
        $changes_made = true;
        echo "✅ تم تحديث المحتوى الرئيسي<br>";
    }
    
    // 5. تحديث البطاقات القديمة
    $content = str_replace(
        ['<div class="card">', '<div class="card-header">', '<div class="card-body">'],
        ['<div class="modern-card hover-lift fade-in">', '<div class="modern-card-header">', '<div class="modern-card-body">'],
        $content
    );
    
    // 6. تحديث الأزرار القديمة
    $content = preg_replace(
        '/class="btn btn-([^"]*)"/',
        'class="modern-btn modern-btn-$1"',
        $content
    );
    
    // 7. تحديث الجداول
    $content = str_replace(
        'class="table',
        'class="modern-table table',
        $content
    );
    
    // 8. تحديث النماذج
    $content = str_replace(
        ['class="form-control"', 'class="form-label"'],
        ['class="modern-form-control"', 'class="modern-form-label"'],
        $content
    );
    
    // 9. تحديث الشارات
    $content = preg_replace(
        '/class="badge bg-([^"]*)"/',
        'class="modern-badge modern-badge-$1"',
        $content
    );
    
    if ($content !== $original_content) {
        $changes_made = true;
    }
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $updates_applied[] = $page;
            echo "✅ تم حفظ التحديثات بنجاح<br>";
            echo "📁 نسخة احتياطية: " . basename($backup_path) . "<br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $page";
            echo "❌ فشل في حفظ التحديثات<br>";
        }
    } else {
        echo "ℹ️ لا توجد تحديثات مطلوبة<br>";
    }
    
    echo "<hr>";
}

// عرض الملخص
echo "<h3>ملخص العمليات:</h3>";

if (!empty($updates_applied)) {
    echo "<div style='color: green;'>";
    echo "<h4>✅ الملفات المحدثة بنجاح:</h4>";
    foreach ($updates_applied as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='color: red;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

echo "<br><a href='admin_dashboard.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;'>العودة للوحة التحكم</a>";

/**
 * إنشاء الشريط الجانبي المتطور
 */
function generateModernSidebar($active_page) {
    $sidebar_items = [
        'admin_dashboard' => ['icon' => 'fa-tachometer-alt', 'title' => 'لوحة التحكم', 'section' => 'main'],
        'admin_users' => ['icon' => 'fa-users', 'title' => 'إدارة المستخدمين', 'section' => 'main'],
        'admin_activity' => ['icon' => 'fa-history', 'title' => 'سجل العمليات', 'section' => 'main'],
        'admin_reports' => ['icon' => 'fa-chart-bar', 'title' => 'التقارير الشاملة', 'section' => 'reports'],
        'admin_financial' => ['icon' => 'fa-file-invoice-dollar', 'title' => 'التقارير المالية', 'section' => 'reports'],
        'admin_error_logs' => ['icon' => 'fa-exclamation-triangle', 'title' => 'سجل الأخطاء', 'section' => 'system'],
        'admin_system' => ['icon' => 'fa-cogs', 'title' => 'إعدادات النظام', 'section' => 'system'],
        'admin_manage_admins' => ['icon' => 'fa-user-shield', 'title' => 'إدارة المديرين', 'section' => 'admins']
    ];
    
    $sections = [
        'main' => 'الإدارة الرئيسية',
        'reports' => 'التقارير والإحصائيات', 
        'system' => 'إدارة النظام',
        'admins' => 'إدارة المديرين'
    ];
    
    $sidebar = '<nav class="modern-sidebar d-none d-lg-block">';
    
    $current_section = '';
    foreach ($sidebar_items as $page => $item) {
        if ($current_section !== $item['section']) {
            if ($current_section !== '') {
                $sidebar .= '</div>';
            }
            $sidebar .= '<div class="sidebar-section">';
            $sidebar .= '<div class="sidebar-section-title">' . $sections[$item['section']] . '</div>';
            $current_section = $item['section'];
        }
        
        $active_class = ($page === $active_page) ? ' active' : '';
        $sidebar .= '<a class="sidebar-nav-link' . $active_class . '" href="' . $page . '.php">';
        $sidebar .= '<i class="fas ' . $item['icon'] . '"></i>';
        $sidebar .= '<span>' . $item['title'] . '</span>';
        $sidebar .= '</a>';
    }
    
    $sidebar .= '</div></nav>';
    
    return $sidebar;
}
?>
