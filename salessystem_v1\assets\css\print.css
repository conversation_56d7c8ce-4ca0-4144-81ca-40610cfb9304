/**
 * ملف CSS محسن للطباعة - جميع مطبوعات المشروع
 * تم تحسينه ليكون ملائماً للطباعة على الورق
 */

/* إعدادات الطباعة العامة */
@media print {
    /* إعدادات الصفحة */
    @page {
        size: A4;
        margin: 1cm 1.5cm;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    /* إعدادات الجسم الأساسية */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    html, body {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        color: #000 !important;
        font-family: 'Arial', 'Tahoma', sans-serif !important;
        font-size: 11pt !important;
        line-height: 1.4 !important;
        direction: rtl !important;
        text-align: right !important;
    }

    /* إخفاء العناصر غير المطلوبة */
    .no-print,
    .navbar,
    .sidebar,
    .breadcrumb,
    .btn-toolbar,
    .floating-buttons,
    .pagination,
    .alert,
    .modal,
    .dropdown,
    .btn:not(.print-keep),
    .form-control,
    .form-select,
    .card-header .btn,
    .table .btn-group,
    .back-to-top,
    .search-box,
    .filter-section,
    nav,
    footer,
    .main-header,
    .main-sidebar,
    .content-header,
    .card-footer,
    script,
    noscript,
    .btn-group,
    .fas.fa-eye,
    .fas.fa-print,
    .fas.fa-edit,
    .fas.fa-trash,
    .btn-info,
    .btn-secondary,
    .btn-warning,
    .btn-danger,
    .btn-outline-info,
    .btn-outline-secondary,
    .btn-sm,
    canvas,
    .chart-container,
    #salesChart,
    #profitLossChart,
    #salesChartContainer,
    #profitLossChartContainer,
    #topProductsChartContainer,
    #topCustomersChartContainer {
        display: none !important;
        visibility: hidden !important;
    }

    /* إظهار المحتوى المحدد للطباعة فقط */
    #printableContent {
        display: block !important;
        visibility: visible !important;
    }

    /* إخفاء المحتوى الأصلي عند وجود محتوى طباعة محدد */
    body:has(#printableContent:not(:empty)) #reportContent > *:not(#printableContent) {
        display: none !important;
    }

    /* تنسيق الحاوي الرئيسي */
    .container,
    .container-fluid {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .row {
        margin: 0 !important;
    }

    .col,
    .col-md-12,
    [class*="col-"] {
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* تنسيق البطاقات */
    .card {
        border: none !important;
        box-shadow: none !important;
        margin-bottom: 15px !important;
        page-break-inside: avoid;
        background: white !important;
    }

    .card-header {
        background: white !important;
        color: #000 !important;
        border-bottom: 2px solid #000 !important;
        padding: 10px 0 !important;
        margin-bottom: 15px !important;
        text-align: center !important;
    }

    .card-header h1,
    .card-header h2,
    .card-header h3,
    .card-header h4,
    .card-header h5,
    .card-header h6 {
        color: #000 !important;
        margin: 0 !important;
        font-weight: bold !important;
    }

    .card-body {
        padding: 0 !important;
        background: white !important;
    }

    /* تنسيق الجداول */
    .table-responsive {
        overflow: visible !important;
        border: none !important;
    }

    .table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin-bottom: 15px !important;
        font-size: 10pt !important;
        page-break-inside: auto;
    }

    .table thead {
        display: table-header-group !important;
    }

    .table tbody {
        display: table-row-group !important;
    }

    .table tfoot {
        display: table-footer-group !important;
    }

    .table th {
        background: #f0f0f0 !important;
        color: #000 !important;
        border: 1px solid #000 !important;
        padding: 8px 6px !important;
        text-align: center !important;
        font-weight: bold !important;
        font-size: 10pt !important;
        page-break-inside: avoid;
    }

    .table td {
        border: 1px solid #000 !important;
        padding: 6px 4px !important;
        text-align: center !important;
        font-size: 9pt !important;
        line-height: 1.3 !important;
        page-break-inside: avoid;
    }

    .table tr {
        page-break-inside: avoid;
    }

    /* تنسيق الصفوف المخططة */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05) !important;
    }

    /* تنسيق صفوف الإجماليات */
    .table tfoot th,
    .table tfoot td,
    .total-row th,
    .total-row td {
        background: #e0e0e0 !important;
        color: #000 !important;
        font-weight: bold !important;
        border: 2px solid #000 !important;
    }

    /* تنسيق العناوين */
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        margin: 10px 0 5px 0 !important;
        page-break-after: avoid;
        font-weight: bold !important;
    }

    h1 { font-size: 16pt !important; }
    h2 { font-size: 14pt !important; }
    h3 { font-size: 12pt !important; }
    h4 { font-size: 11pt !important; }
    h5 { font-size: 10pt !important; }
    h6 { font-size: 9pt !important; }

    /* تنسيق الفقرات */
    p {
        margin: 5px 0 !important;
        orphans: 3;
        widows: 3;
        line-height: 1.3 !important;
    }

    /* تنسيق الروابط */
    a {
        color: #000 !important;
        text-decoration: none !important;
    }

    a[href]:after {
        content: none !important;
    }

    /* تنسيق الشارات والتسميات */
    .badge,
    .label {
        background: white !important;
        color: #000 !important;
        border: 1px solid #000 !important;
        padding: 2px 4px !important;
        font-size: 8pt !important;
        border-radius: 0 !important;
    }

    /* تنسيق الألوان للطباعة */
    .text-primary,
    .text-success,
    .text-danger,
    .text-warning,
    .text-info,
    .text-secondary {
        color: #000 !important;
    }

    .bg-primary,
    .bg-success,
    .bg-danger,
    .bg-warning,
    .bg-info,
    .bg-secondary,
    .bg-light,
    .bg-dark {
        background: white !important;
        color: #000 !important;
    }

    /* تنسيق الأيقونات */
    .fas,
    .far,
    .fab,
    .fa {
        font-size: 8pt !important;
        color: #000 !important;
    }

    /* تنسيق خاص للفواتير */
    .invoice-box {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: none !important;
    }

    .invoice-header {
        text-align: center !important;
        margin-bottom: 20px !important;
        padding-bottom: 15px !important;
        border-bottom: 2px solid #000 !important;
    }

    .company-info,
    .invoice-info,
    .client-info {
        margin-bottom: 15px !important;
        padding: 10px !important;
        border: 1px solid #000 !important;
    }

    .company-info h2,
    .invoice-info h2 {
        font-size: 14pt !important;
        margin-bottom: 10px !important;
    }

    /* تنسيق كشف الحساب */
    .account-statement-header {
        text-align: center !important;
        margin-bottom: 20px !important;
        padding: 15px !important;
        border: 2px solid #000 !important;
    }

    .balance-summary {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 15px !important;
        padding: 10px !important;
        border: 1px solid #000 !important;
    }

    .balance-item {
        text-align: center !important;
        flex: 1 !important;
        padding: 5px !important;
        border-right: 1px solid #000 !important;
    }

    .balance-item:last-child {
        border-right: none !important;
    }

    /* تنسيق التقارير */
    .report-summary {
        margin-bottom: 20px !important;
        padding: 15px !important;
        border: 1px solid #000 !important;
    }

    .report-period {
        text-align: center !important;
        font-weight: bold !important;
        margin-bottom: 10px !important;
        padding: 5px !important;
        border-bottom: 1px solid #000 !important;
    }

    /* تنسيق الملاحظات */
    .notes {
        margin-top: 20px !important;
        padding: 10px !important;
        border: 1px solid #000 !important;
        page-break-inside: avoid;
    }

    .notes h4 {
        margin-bottom: 10px !important;
        border-bottom: 1px solid #000 !important;
        padding-bottom: 5px !important;
    }

    /* تنسيق التوقيعات */
    .signatures {
        margin-top: 30px !important;
        display: flex !important;
        justify-content: space-between !important;
        page-break-inside: avoid;
    }

    .signature-box {
        width: 30% !important;
        text-align: center !important;
        border-top: 1px solid #000 !important;
        padding-top: 10px !important;
    }

    /* تنسيق معلومات الطباعة */
    .print-info {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        text-align: center !important;
        font-size: 8pt !important;
        padding: 5px !important;
        border-top: 1px solid #000 !important;
        background: white !important;
    }

    /* منع كسر الصفحة */
    .page-break-avoid {
        page-break-inside: avoid !important;
    }

    .page-break-before {
        page-break-before: always !important;
    }

    .page-break-after {
        page-break-after: always !important;
    }

    /* تحسينات للجداول الكبيرة */
    .large-table {
        font-size: 8pt !important;
    }

    .large-table th,
    .large-table td {
        padding: 3px 2px !important;
    }

    /* تنسيق خاص للأرقام */
    .number {
        text-align: left !important;
        direction: ltr !important;
    }

    /* تنسيق التواريخ */
    .date {
        white-space: nowrap !important;
    }

    /* تحسينات للطباعة الملونة */
    .print-color .table th {
        background: #f0f0f0 !important;
    }

    .print-color .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05) !important;
    }

    /* تنسيق خاص للشعار */
    .company-logo {
        max-width: 100px !important;
        max-height: 50px !important;
        margin-bottom: 10px !important;
    }
}
