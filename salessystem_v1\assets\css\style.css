/**
 * Custom styles for the Sales & Purchases System
 * Enhanced Modern UI Version
 */

:root {
    --primary-color: #0d6efd;
    --primary-hover: #0b5ed7;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --body-bg: #f5f8fa;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    --transition-speed: 0.3s;
}

body {
    background-color: var(--body-bg);
    transition: background-color var(--transition-speed);
}

/* Language-specific styles */
html[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

html[dir="ltr"] .ms-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Language switcher styles */
.language-switcher .dropdown-item.active {
    background-color: var(--primary-color);
    color: white;
}

/* Dashboard cards */
.card {
    border-radius: 0.5rem;
    border: none;
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed);
    margin-bottom: 1.5rem;
}

.card-header {
    border-bottom: none;
    background-color: white;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-body {
    padding: 1.5rem;
}

.dashboard-card {
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.06);
}

.dashboard-card .card-header {
    position: relative;
}

.dashboard-card .card-header i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
    vertical-align: middle;
}

.dashboard-card .card-body {
    position: relative;
    z-index: 1;
}

.dashboard-card .card-body h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-card .bg-icon {
    position: absolute;
    bottom: -20px;
    right: 10px;
    font-size: 5rem;
    opacity: 0.1;
    transform: rotate(-15deg);
    transition: all var(--transition-speed);
}

.dashboard-card:hover .bg-icon {
    transform: rotate(0deg) scale(1.1);
    opacity: 0.15;
}

/* Tables */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table th {
    font-weight: 600;
    padding: 1rem;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr {
    transition: background-color var(--transition-speed);
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.table-responsive {
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    background-color: white;
    margin-bottom: 1.5rem;
}

/* Forms */
.form-control, .form-select {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    transition: all var(--transition-speed);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all var(--transition-speed);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.2);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

.btn-outline-light:hover .text-white {
    color: var(--primary-color) !important;
}

/* Navbar Styles */
.navbar {
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all var(--transition-speed);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-nav .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
}

.navbar-dark .navbar-nav .nav-link i {
    margin-right: 0.5rem;
}

/* Badge Styles */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 0.375rem;
}

/* Status Indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.success {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(25, 135, 84, 0.2);
}

.status-indicator.warning {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

.status-indicator.danger {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

/* Search Box */
.search-box {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-box .form-control {
    padding-left: 2.5rem;
    border-radius: 2rem;
    background-color: #f8f9fa;
}

.search-box .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

/* Pagination */
.pagination {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.pagination .page-link {
    border: none;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: 0.375rem;
    color: var(--primary-color);
    transition: all var(--transition-speed);
}

.pagination .page-link:hover {
    background-color: rgba(13, 110, 253, 0.1);
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Print styles - محسن للطباعة على الورق */
@media print {
    /* إعدادات الصفحة */
    @page {
        size: A4;
        margin: 1cm 1.5cm;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    /* إخفاء العناصر غير المطلوبة */
    .no-print,
    .navbar,
    .sidebar,
    .breadcrumb,
    .btn-toolbar,
    .floating-buttons,
    .pagination,
    .alert,
    .modal,
    .dropdown,
    .btn:not(.print-keep),
    .form-control,
    .form-select,
    .card-header .btn,
    .table .btn-group,
    .back-to-top,
    .search-box,
    .filter-section,
    nav:not(.print-keep),
    footer:not(.print-keep),
    .main-header,
    .main-sidebar,
    .content-header,
    .card-footer {
        display: none !important;
        visibility: hidden !important;
    }

    /* إعدادات الجسم الأساسية */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    html, body {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        color: #000 !important;
        font-family: 'Arial', 'Tahoma', sans-serif !important;
        font-size: 11pt !important;
        line-height: 1.4 !important;
        direction: rtl !important;
        text-align: right !important;
    }

    /* تنسيق الحاوي */
    .container,
    .container-fluid {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .row {
        margin: 0 !important;
    }

    .col,
    .col-md-12,
    [class*="col-"] {
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* تنسيق البطاقات */
    .card {
        border: none !important;
        box-shadow: none !important;
        margin-bottom: 15px !important;
        page-break-inside: avoid;
        background: white !important;
    }

    .card-header {
        background: white !important;
        color: #000 !important;
        border-bottom: 2px solid #000 !important;
        padding: 10px 0 !important;
        margin-bottom: 15px !important;
        text-align: center !important;
    }

    .card-header h1,
    .card-header h2,
    .card-header h3,
    .card-header h4,
    .card-header h5,
    .card-header h6 {
        color: #000 !important;
        margin: 0 !important;
        font-weight: bold !important;
    }

    .card-body {
        padding: 0 !important;
        background: white !important;
    }

    /* تنسيق الجداول */
    .table-responsive {
        overflow: visible !important;
        border: none !important;
    }

    .table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin-bottom: 15px !important;
        font-size: 10pt !important;
        page-break-inside: auto;
    }

    .table thead {
        display: table-header-group !important;
    }

    .table tbody {
        display: table-row-group !important;
    }

    .table tfoot {
        display: table-footer-group !important;
    }

    .table th {
        background: #f0f0f0 !important;
        color: #000 !important;
        border: 1px solid #000 !important;
        padding: 8px 6px !important;
        text-align: center !important;
        font-weight: bold !important;
        font-size: 10pt !important;
        page-break-inside: avoid;
    }

    .table td {
        border: 1px solid #000 !important;
        padding: 6px 4px !important;
        text-align: center !important;
        font-size: 9pt !important;
        line-height: 1.3 !important;
        page-break-inside: avoid;
    }

    .table tr {
        page-break-inside: avoid;
    }

    /* تنسيق الصفوف المخططة */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05) !important;
    }

    /* تنسيق صفوف الإجماليات */
    .table tfoot th,
    .table tfoot td,
    .total-row th,
    .total-row td {
        background: #e0e0e0 !important;
        color: #000 !important;
        font-weight: bold !important;
        border: 2px solid #000 !important;
    }

    /* تنسيق العناوين */
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        margin: 10px 0 5px 0 !important;
        page-break-after: avoid;
        font-weight: bold !important;
    }

    h1 { font-size: 16pt !important; }
    h2 { font-size: 14pt !important; }
    h3 { font-size: 12pt !important; }
    h4 { font-size: 11pt !important; }
    h5 { font-size: 10pt !important; }
    h6 { font-size: 9pt !important; }

    /* تنسيق الفقرات */
    p {
        margin: 5px 0 !important;
        orphans: 3;
        widows: 3;
        line-height: 1.3 !important;
    }

    /* تنسيق الروابط */
    a {
        color: #000 !important;
        text-decoration: none !important;
    }

    a[href]:after {
        content: none !important;
    }

    /* تنسيق الشارات */
    .badge,
    .label {
        background: white !important;
        color: #000 !important;
        border: 1px solid #000 !important;
        padding: 2px 4px !important;
        font-size: 8pt !important;
        border-radius: 0 !important;
    }

    /* تنسيق الألوان للطباعة */
    .text-primary,
    .text-success,
    .text-danger,
    .text-warning,
    .text-info,
    .text-secondary {
        color: #000 !important;
    }

    .bg-primary,
    .bg-success,
    .bg-danger,
    .bg-warning,
    .bg-info,
    .bg-secondary,
    .bg-light,
    .bg-dark {
        background: white !important;
        color: #000 !important;
    }

    /* تنسيق الأيقونات */
    .fas,
    .far,
    .fab,
    .fa {
        font-size: 8pt !important;
        color: #000 !important;
    }

    /* تنسيق رأس الفاتورة */
    .invoice-header {
        text-align: center !important;
        margin-bottom: 20px !important;
        padding-bottom: 15px !important;
        border-bottom: 2px solid #000 !important;
    }

    /* تنسيق تذييل الفاتورة */
    .invoice-footer {
        margin-top: 20px !important;
        padding-top: 15px !important;
        border-top: 1px solid #000 !important;
        text-align: center !important;
        font-size: 9pt !important;
    }

    /* منع كسر الصفحة */
    .page-break-avoid {
        page-break-inside: avoid !important;
    }

    .page-break-before {
        page-break-before: always !important;
    }

    .page-break-after {
        page-break-after: always !important;
    }

    /* تنسيق خاص للأرقام */
    .number {
        text-align: left !important;
        direction: ltr !important;
    }

    /* تنسيق التواريخ */
    .date {
        white-space: nowrap !important;
    }

    /* تنسيق معلومات الطباعة */
    .print-info {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        text-align: center !important;
        font-size: 8pt !important;
        padding: 5px !important;
        border-top: 1px solid #000 !important;
        background: white !important;
    }
}

/* RTL specific adjustments */
html[dir="rtl"] .dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

html[dir="rtl"] .navbar-dark .navbar-nav .nav-link i {
    margin-right: 0;
    margin-left: 0.5rem;
}

html[dir="rtl"] .search-box .search-icon {
    left: auto;
    right: 1rem;
}

html[dir="rtl"] .status-indicator {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* LTR specific adjustments */
html[dir="ltr"] .dropdown-menu-end {
    left: auto !important;
    right: 0 !important;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Icon Box Styles */
.icon-box {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.bg-primary-light {
    background-color: rgba(13, 110, 253, 0.1);
}

.bg-success-light {
    background-color: rgba(25, 135, 84, 0.1);
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-info-light {
    background-color: rgba(13, 202, 240, 0.1);
}

.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.1);
}

/* Breadcrumb Styles */
.breadcrumb {
    background-color: white;
    border-radius: 0.5rem;
    padding: 0.75rem 1.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-speed);
}

.breadcrumb-item a:hover {
    color: var(--primary-hover);
}

.breadcrumb-item.active {
    color: var(--secondary-color);
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .navbar-dark .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
    }
}

@media (max-width: 768px) {
    .language-switcher {
        margin-left: 0;
        margin-top: 10px;
    }

    .table th, .table td {
        padding: 0.75rem;
    }

    .btn {
        padding: 0.375rem 1rem;
    }

    .dashboard-card .card-body h3 {
        font-size: 1.5rem;
    }

    .dashboard-card .bg-icon {
        font-size: 4rem;
    }

    .back-to-top {
        width: 35px;
        height: 35px;
        bottom: 15px;
        right: 15px;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .table-responsive {
        border-radius: 0.375rem;
    }

    .card {
        border-radius: 0.375rem;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .card-body {
        padding: 0.75rem 1rem;
    }
}

/* Inline Edit Form Styles */
#editFormSection {
    transition: all 0.3s ease-in-out;
    position: relative;
    z-index: 100;
}

#editFormSection .card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-width: 2px;
}

.inline-content-wrapper {
    background: white;
    border-radius: 0.5rem;
    padding: 0;
}

.inline-content-wrapper .form-control,
.inline-content-wrapper .form-select {
    border: 1px solid #e0e6ed;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.inline-content-wrapper .form-control:focus,
.inline-content-wrapper .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

.inline-content-wrapper .card {
    border: 1px solid #e0e6ed;
    margin-bottom: 1rem;
}

.inline-content-wrapper .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.inline-content-wrapper .table {
    margin-bottom: 0;
}

.inline-content-wrapper .table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.inline-content-wrapper .table td {
    border-top: 1px solid #dee2e6;
}

.inline-content-wrapper .btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.inline-content-wrapper .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Edit Form Animation */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-30px);
    }
}

.edit-form-enter {
    animation: slideInUp 0.3s ease-out;
}

.edit-form-exit {
    animation: slideOutDown 0.3s ease-in;
}

/* نموذج التعديل المستقل */
.edit-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    backdrop-filter: blur(5px);
}

.edit-form-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: slideInScale 0.3s ease-out;
}

.edit-form-header {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
    color: #212529;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.edit-form-header.purchase-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.edit-form-header h4 {
    margin: 0;
    font-weight: 600;
    flex: 1;
}

.edit-form-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: #f8f9fa;
}

/* تأثير الظهور */
@keyframes slideInScale {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* تأثير التركيز على النموذج */
.table-dimmed {
    opacity: 0.3 !important;
    pointer-events: none !important;
    transition: opacity 0.3s ease-in-out !important;
}

.stats-dimmed {
    opacity: 0.3 !important;
    pointer-events: none !important;
    transition: opacity 0.3s ease-in-out !important;
}

/* تصميم متجاوب للنموذج المستقل */
@media (max-width: 768px) {
    .edit-form-overlay {
        padding: 10px;
    }

    .edit-form-container {
        max-width: 95vw;
        max-height: 95vh;
        margin: 0;
    }

    .edit-form-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .edit-form-header h4 {
        font-size: 1.1rem;
        margin: 0;
    }

    .edit-form-body {
        padding: 10px;
    }
}

/* تحسينات شاملة للوضع الداكن */
[data-theme="dark"] {
    color-scheme: dark;
}

[data-theme="dark"] body {
    background-color: #1a202c;
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card {
    background: linear-gradient(145deg, rgba(26, 32, 44, 0.95) 0%, rgba(45, 55, 72, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card:hover {
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.8), 0 6px 12px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .dashboard-card .card-header {
    background: rgba(45, 55, 72, 0.9);
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card .card-body h3 {
    color: #f7fafc;
}

[data-theme="dark"] .dashboard-card .bg-icon {
    opacity: 0.05;
}

[data-theme="dark"] .floating-buttons {
    background: rgba(26, 32, 44, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .floating-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .floating-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

[data-theme="dark"] .quick-stats {
    background: rgba(26, 32, 44, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .quick-stats .stat-item {
    background: rgba(45, 55, 72, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .quick-stats .stat-value {
    color: #f7fafc;
}

[data-theme="dark"] .quick-stats .stat-label {
    color: #e2e8f0;
}

[data-theme="dark"] .recent-activities {
    background: rgba(26, 32, 44, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .activity-item {
    background: rgba(45, 55, 72, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .activity-item:hover {
    background: rgba(55, 65, 81, 0.9);
}

[data-theme="dark"] .activity-time {
    color: #a0aec0;
}

[data-theme="dark"] .edit-form {
    background: rgba(26, 32, 44, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .edit-form-header {
    background: rgba(45, 55, 72, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .edit-form-body {
    background: rgba(26, 32, 44, 0.95);
    color: #f7fafc;
}

[data-theme="dark"] .sortable th {
    color: #f7fafc;
}

[data-theme="dark"] .sortable th:hover {
    background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .language-switcher .dropdown-item {
    color: #f7fafc;
}

[data-theme="dark"] .language-switcher .dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
}

[data-theme="dark"] .language-switcher .dropdown-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

/* تحسينات إضافية للعناوين في الوضع الداكن */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
    color: #f7fafc !important;
}

[data-theme="dark"] .h1,
[data-theme="dark"] .h2,
[data-theme="dark"] .h3,
[data-theme="dark"] .h4,
[data-theme="dark"] .h5,
[data-theme="dark"] .h6 {
    color: #f7fafc !important;
}

/* تحسينات شاملة للجداول في الوضع الداكن */
[data-theme="dark"] table {
    color: #f7fafc !important;
    background: #1a202c;
}

[data-theme="dark"] table thead th {
    background: rgba(45, 55, 72, 0.9) !important;
    color: #f7fafc !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] table tbody {
    background: #1a202c !important;
    color: #f7fafc !important;
}

[data-theme="dark"] table tbody tr {
    background: transparent !important;
    color: #f7fafc !important;
}

[data-theme="dark"] table tbody tr td {
    background: transparent !important;
    color: #f7fafc !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] table tbody tr:nth-child(odd) {
    background: rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] table tbody tr:nth-child(odd) td {
    background: rgba(255, 255, 255, 0.05) !important;
    color: #f7fafc !important;
}

[data-theme="dark"] table tbody tr:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] table tbody tr:hover td {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #f7fafc !important;
}

/* تحسينات للجداول المرتبة */
[data-theme="dark"] .sortable th {
    color: #f7fafc !important;
    background: rgba(45, 55, 72, 0.9) !important;
}

[data-theme="dark"] .sortable th:hover {
    background: rgba(55, 65, 81, 0.9) !important;
    color: #f7fafc !important;
}

/* تحسينات للنصوص */
[data-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}

[data-theme="dark"] .text-dark {
    color: #f7fafc !important;
}

[data-theme="dark"] .text-secondary {
    color: #e2e8f0 !important;
}

[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div {
    color: #f7fafc;
}

[data-theme="dark"] .card p,
[data-theme="dark"] .card span,
[data-theme="dark"] .card div {
    color: #f7fafc !important;
}

/* تحسينات إضافية للظلال في الوضع الداكن */
[data-theme="dark"] .floating-buttons {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.8), 0 4px 12px rgba(0, 0, 0, 0.6) !important;
}

[data-theme="dark"] .floating-btn:hover {
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4), 0 6px 15px rgba(0, 0, 0, 0.7) !important;
}

[data-theme="dark"] .quick-stats {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.7), 0 3px 10px rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] .recent-activities {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.7), 0 3px 10px rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] .activity-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6), 0 2px 6px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .edit-form {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8), 0 5px 15px rgba(0, 0, 0, 0.6) !important;
}
