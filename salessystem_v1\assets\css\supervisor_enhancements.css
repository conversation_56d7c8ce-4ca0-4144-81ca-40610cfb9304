/* تحسينات إضافية للمشرفين */

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* تحسينات البطاقات */
.stats-card {
    animation: fadeInScale 0.5s ease-out;
}

.modern-card {
    animation: slideInRight 0.6s ease-out;
}

/* تحسينات الجداول */
.modern-table tbody tr {
    transition: all 0.3s ease;
}

.modern-table tbody tr:hover {
    background: linear-gradient(90deg, var(--primary-color)05, transparent);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* تحسينات الأزرار */
.modern-btn {
    position: relative;
    overflow: hidden;
}

.modern-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

/* تحسينات الشريط الجانبي */
.sidebar-nav-link {
    position: relative;
    overflow: hidden;
}

.sidebar-nav-link::before {
    content: "";
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--primary-color)10, transparent);
    transition: right 0.3s ease;
}

.sidebar-nav-link:hover::before {
    right: 100%;
}

/* تحسينات الإحصائيات */
.stats-value {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات الشارات */
.modern-badge {
    position: relative;
    overflow: hidden;
}

.modern-badge::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.4s;
}

.modern-badge:hover::before {
    left: 100%;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .stats-value {
        font-size: 1.5rem;
    }
    
    .modern-card-header {
        padding: 1rem;
    }
    
    .modern-card-body {
        padding: 1rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .modern-sidebar,
    .floating-btn,
    #quickActionsMenu,
    .modern-btn {
        display: none !important;
    }
    
    .supervisor-content {
        margin-right: 0 !important;
    }
    
    .stats-card,
    .modern-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}