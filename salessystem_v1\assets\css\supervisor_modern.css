/* تصميم عصري وخفيف لقسم المشرفين */

:root {
    --primary-color: #6366f1;
    --primary-light: #a5b4fc;
    --primary-dark: #4f46e5;
    --secondary-color: #f1f5f9;
    --accent-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* الخط العربي الحديث */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--text-primary);
    line-height: 1.6;
}

/* التخطيط الرئيسي */
.supervisor-layout {
    display: flex;
    min-height: 100vh;
    background: var(--bg-light);
}

/* الشريط الجانبي العصري */
.modern-sidebar {
    width: 280px;
    background: var(--bg-white);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.sidebar-brand {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-brand i {
    font-size: 1.5rem;
    color: var(--primary-light);
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-section {
    margin-bottom: 1.5rem;
}

.sidebar-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-muted);
    padding: 0 1.5rem 0.5rem;
    margin-bottom: 0.5rem;
}

.sidebar-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
    position: relative;
}

.sidebar-nav-link:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
    border-right-color: var(--primary-light);
}

.sidebar-nav-link.active {
    background: linear-gradient(90deg, var(--primary-color)10, transparent);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
    font-weight: 500;
}

.sidebar-nav-link i {
    width: 1.25rem;
    text-align: center;
    font-size: 1rem;
}

/* المحتوى الرئيسي */
.supervisor-content {
    flex: 1;
    margin-right: 280px;
    padding: 2rem;
    background: var(--bg-light);
}

/* رأس الصفحة */
.page-header {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title i {
    color: var(--primary-color);
    font-size: 1.75rem;
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

/* البطاقات العصرية */
.modern-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.modern-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-light);
    display: flex;
    justify-content: between;
    align-items: center;
}

.modern-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-card-body {
    padding: 1.5rem;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.stats-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-4px);
}

.stats-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.stats-info {
    flex: 1;
}

.stats-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stats-change {
    font-size: 0.75rem;
    color: var(--accent-color);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--primary-color)20, var(--primary-light)20);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.25rem;
}

/* الأزرار العصرية */
.modern-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    white-space: nowrap;
}

.modern-btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.modern-btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.modern-btn-secondary {
    background: var(--secondary-color);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.modern-btn-secondary:hover {
    background: var(--border-color);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.modern-btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.modern-btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* الجداول العصرية */
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table th {
    background: var(--bg-light);
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.875rem;
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.modern-table tbody tr:hover {
    background: var(--bg-light);
}

/* الشارات العصرية */
.modern-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    white-space: nowrap;
}

.modern-badge-primary {
    background: var(--primary-color)20;
    color: var(--primary-color);
}

.modern-badge-success {
    background: var(--accent-color)20;
    color: var(--accent-color);
}

.modern-badge-warning {
    background: var(--warning-color)20;
    color: var(--warning-color);
}

.modern-badge-danger {
    background: var(--danger-color)20;
    color: var(--danger-color);
}

/* التنبيهات العصرية */
.modern-alert {
    padding: 1rem 1.5rem;
    border-radius: var(--radius-md);
    border: 1px solid;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modern-alert-success {
    background: var(--accent-color)10;
    border-color: var(--accent-color)30;
    color: var(--accent-color);
}

.modern-alert-warning {
    background: var(--warning-color)10;
    border-color: var(--warning-color)30;
    color: var(--warning-color);
}

.modern-alert-danger {
    background: var(--danger-color)10;
    border-color: var(--danger-color)30;
    color: var(--danger-color);
}

/* الزر العائم */
.floating-btn {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.floating-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* التجاوب */
@media (max-width: 1024px) {
    .modern-sidebar {
        transform: translateX(-100%);
    }
    
    .supervisor-content {
        margin-right: 0;
    }
    
    .modern-sidebar.show {
        transform: translateX(0);
    }
}

@media (max-width: 768px) {
    .supervisor-content {
        padding: 1rem;
    }
    
    .page-header {
        padding: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .stats-value {
        font-size: 1.5rem;
    }
}
