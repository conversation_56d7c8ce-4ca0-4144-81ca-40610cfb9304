<?php
/**
 * إصلاح تلقائي لجميع الأخطاء في ملفات PHP
 */

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = [
    'admin_activity.php',
    'admin_reports.php',
    'admin_financial.php',
    'admin_users.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$fixed_files = [];
$errors = [];

echo "<h2>🔧 إصلاح تلقائي لجميع الأخطاء في ملفات PHP</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>🎯 الإصلاحات المطبقة:</h4>";
echo "<ul>";
echo "<li>✅ إصلاح تنسيق المصفوفات</li>";
echo "<li>✅ إصلاح المتغيرات المكسورة</li>";
echo "<li>✅ إصلاح الأقواس غير المتطابقة</li>";
echo "<li>✅ تنظيف الكود وإزالة المسافات الزائدة</li>";
echo "<li>✅ إصلاح HTML المكسور</li>";
echo "</ul>";
echo "</div>";

foreach ($files_to_fix as $file) {
    $file_path = __DIR__ . '/' . $file;
    
    echo "<h3>🔧 إصلاح: $file</h3>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    if (!file_exists($file_path)) {
        echo "<span style='color: orange;'>⚠️ الملف غير موجود</span><br>";
        echo "</div>";
        continue;
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $file";
        echo "<span style='color: red;'>❌ فشل في قراءة الملف</span><br>";
        echo "</div>";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. إصلاح المصفوفات المكسورة
    $array_fixes = [
        // إصلاح $_SESSION المكسورة
        '/\$_SESSION\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => '$_SESSION[\'$1\']',
        '/\$_GET\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => '$_GET[\'$1\']',
        '/\$_POST\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => '$_POST[\'$1\']',
        
        // إصلاح المصفوفات العادية
        '/\$([a-zA-Z_][a-zA-Z0-9_]*)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => '$$$1[\'$2\']',
        
        // إصلاح الأقواس المكسورة في المصفوفات
        '/\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => '[\'$1\']',
    ];
    
    foreach ($array_fixes as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $content = $new_content;
            $changes_made = true;
            echo "<span style='color: green;'>✅ إصلاح مصفوفات</span><br>";
        }
    }
    
    // 2. إصلاح HTML المكسور في الشريط الجانبي
    if (strpos($content, '<nav class="modern-sidebar d-none d-lg-block"><div class="sidebar-section">') !== false) {
        $sidebar_html = '<nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>';
        
        $content = preg_replace(
            '/<nav class="modern-sidebar d-none d-lg-block">.*?<\/nav>/s',
            $sidebar_html,
            $content
        );
        $changes_made = true;
        echo "<span style='color: green;'>✅ إصلاح الشريط الجانبي</span><br>";
    }
    
    // 3. إصلاح مشاكل PHP العامة
    $php_fixes = [
        // إصلاح الفواصل المنقوطة المفقودة
        '/\?>\s*<\?php/' => "?>\n<?php",
        
        // إصلاح المسافات الزائدة
        '/\s+\n/' => "\n",
        '/\n{3,}/' => "\n\n",
        
        // إصلاح الأقواس المكسورة
        '/\(\s*\n\s*\'/' => "(\'",
        '/\'\s*\n\s*\)/' => "\')",
        
        // إصلاح echo المكسورة
        '/echo\s+number_format\(\$([a-zA-Z_][a-zA-Z0-9_]*)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]\);/' => 'echo number_format($$$1[\'$2\']);',
    ];
    
    foreach ($php_fixes as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $content = $new_content;
            $changes_made = true;
        }
    }
    
    // 4. إصلاح مشاكل HTML
    $html_fixes = [
        // إصلاح الأقواس المكسورة في HTML
        '/\?>\s*<\?php echo htmlspecialchars\(\$([a-zA-Z_][a-zA-Z0-9_]*)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]\);/' => '<?php echo htmlspecialchars($$$1[\'$2\']); ?>',
        
        // إصلاح الروابط المكسورة
        '/href="\?page=<\?php echo \$page-1;/' => 'href="?page=<?php echo $page-1;',
        '/href="\?page=<\?php echo \$page\+1;/' => 'href="?page=<?php echo $page+1;',
        
        // إصلاح الفئات المكسورة
        '/class="<\?php echo \$([a-zA-Z_][a-zA-Z0-9_]*)\[\s*\n\s*\'([^\']+)\'\s*\n\s*\]/' => 'class="<?php echo $$$1[\'$2\']',
    ];
    
    foreach ($html_fixes as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $content = $new_content;
            $changes_made = true;
        }
    }
    
    // 5. تنظيف نهائي
    $content = trim($content);
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.auto_fix_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $fixed_files[] = $file;
            echo "<span style='color: green; font-weight: bold;'>✅ تم الإصلاح بنجاح</span><br>";
            echo "<span style='color: blue;'>📁 نسخة احتياطية: " . basename($backup_path) . "</span><br>";
            
            // إحصائيات
            $old_lines = substr_count($original_content, "\n");
            $new_lines = substr_count($content, "\n");
            echo "<span style='color: gray;'>📊 الأسطر: $old_lines → $new_lines</span><br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $file";
            echo "<span style='color: red;'>❌ فشل في حفظ الملف</span><br>";
        }
    } else {
        echo "<span style='color: blue;'>ℹ️ لا توجد تغييرات مطلوبة</span><br>";
    }
    
    echo "</div>";
}

// عرض الملخص
echo "<h3>📋 ملخص الإصلاح التلقائي</h3>";

if (!empty($fixed_files)) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>✅ الملفات المُصلحة بنجاح:</h4>";
    foreach ($fixed_files as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #ffebee; color: #d32f2f; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

// رسالة النتيجة
if (count($fixed_files) === count($files_to_fix)) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>🎉 تم إصلاح جميع الملفات بنجاح!</h3>";
    echo "<p>جميع الأخطاء تم إصلاحها تلقائياً.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3e0; color: #f57c00; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>⚠️ تم إصلاح " . count($fixed_files) . " من " . count($files_to_fix) . " ملفات</h3>";
    echo "<p>بعض الملفات قد تحتاج إصلاح يدوي.</p>";
    echo "</div>";
}

// روابط الاختبار
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار الملفات المُصلحة:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;'>";
foreach ($fixed_files as $file) {
    echo "<a href='$file' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 8px; font-size: 14px; margin: 5px;'>$file</a>";
}
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='comprehensive_error_scanner.php' style='background: #2196f3; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🔍 فحص شامل</a>";
echo "<a href='final_check.php' style='background: #ff9800; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>✅ فحص نهائي</a>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🏠 لوحة التحكم</a>";
echo "</div>";
?>
