<?php
/**
 * إصلاح تلقائي لجميع المشاكل المكتشفة في النظام
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>🔧 إصلاح تلقائي لجميع المشاكل</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
</style>";

$fixed_issues = [];
$failed_fixes = [];

// 1. إصلاح قاعدة البيانات
echo "<div class='section'>";
echo "<h2>1. إصلاح قاعدة البيانات</h2>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }
    
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات: نجح</p>";
    
    // إنشاء الجداول المفقودة
    if (function_exists('createAppTables')) {
        $result = createAppTables();
        if ($result) {
            echo "<p class='success'>✅ تم إنشاء/فحص جميع الجداول</p>";
            $fixed_issues[] = "إنشاء الجداول المفقودة";
        } else {
            echo "<p class='error'>❌ فشل في إنشاء بعض الجداول</p>";
            $failed_fixes[] = "إنشاء الجداول";
        }
    }
    
    // التأكد من وجود المدير الرئيسي
    if (function_exists('ensureSuperAdminExists')) {
        $admin_result = ensureSuperAdminExists();
        if (is_array($admin_result)) {
            if ($admin_result['exists']) {
                echo "<p class='success'>✅ المدير الرئيسي موجود</p>";
            } elseif ($admin_result['created']) {
                echo "<p class='success'>✅ تم إنشاء المدير الرئيسي</p>";
                echo "<p class='warning'>⚠️ اسم المستخدم: {$admin_result['username']}</p>";
                echo "<p class='warning'>⚠️ كلمة المرور: {$admin_result['default_password']}</p>";
                $fixed_issues[] = "إنشاء المدير الرئيسي";
            }
        }
    }
    
    // إضافة إعدادات النظام الافتراضية
    if (function_exists('getSystemSettings') && function_exists('updateSystemSetting')) {
        $settings = getSystemSettings();
        if (empty($settings)) {
            $default_settings = [
                'company_name' => ['نظام المبيعات والمشتريات', 'text', 'اسم الشركة'],
                'company_address' => ['', 'text', 'عنوان الشركة'],
                'company_phone' => ['', 'text', 'هاتف الشركة'],
                'company_email' => ['', 'text', 'بريد الشركة الإلكتروني'],
                'default_currency' => ['ر.س', 'text', 'العملة الافتراضية'],
                'default_tax_rate' => ['15', 'number', 'نسبة الضريبة الافتراضية'],
                'auto_print_pos' => ['1', 'boolean', 'طباعة POS تلقائياً'],
                'system_language' => ['ar', 'text', 'لغة النظام'],
                'items_per_page' => ['20', 'number', 'عدد العناصر في الصفحة']
            ];
            
            $settings_added = 0;
            foreach ($default_settings as $key => $data) {
                if (updateSystemSetting($key, $data[0], $data[1], $data[2])) {
                    $settings_added++;
                }
            }
            
            echo "<p class='success'>✅ تم إضافة $settings_added إعداد افتراضي للنظام</p>";
            $fixed_issues[] = "إضافة إعدادات النظام الافتراضية";
        } else {
            echo "<p class='info'>ℹ️ إعدادات النظام موجودة (" . count($settings) . " إعداد)</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في إصلاح قاعدة البيانات: " . $e->getMessage() . "</p>";
    $failed_fixes[] = "إصلاح قاعدة البيانات: " . $e->getMessage();
}

echo "</div>";

// 2. إنشاء المجلدات المفقودة
echo "<div class='section'>";
echo "<h2>2. إنشاء المجلدات المفقودة</h2>";

$required_directories = [
    'logs' => 'مجلد السجلات',
    'uploads' => 'مجلد الرفع',
    'backups' => 'مجلد النسخ الاحتياطية',
    'assets/css' => 'مجلد ملفات CSS',
    'assets/js' => 'مجلد ملفات JavaScript',
    'assets/images' => 'مجلد الصور'
];

foreach ($required_directories as $dir => $description) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p class='success'>✅ تم إنشاء $description</p>";
            $fixed_issues[] = "إنشاء مجلد: $dir";
        } else {
            echo "<p class='error'>❌ فشل في إنشاء $description</p>";
            $failed_fixes[] = "إنشاء مجلد: $dir";
        }
    } else {
        echo "<p class='info'>ℹ️ $description: موجود</p>";
    }
}

echo "</div>";

// 3. إنشاء ملفات الحماية
echo "<div class='section'>";
echo "<h2>3. إنشاء ملفات الحماية</h2>";

$protection_files = [
    'logs/.htaccess' => "Order Deny,Allow\nDeny from all",
    'config/.htaccess' => "Order Deny,Allow\nDeny from all",
    'backups/.htaccess' => "Order Deny,Allow\nDeny from all",
    'uploads/.htaccess' => "Options -Indexes\nOptions -ExecCGI\nAddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi"
];

foreach ($protection_files as $file => $content) {
    if (!file_exists($file)) {
        if (file_put_contents($file, $content)) {
            echo "<p class='success'>✅ تم إنشاء ملف الحماية: $file</p>";
            $fixed_issues[] = "إنشاء ملف حماية: $file";
        } else {
            echo "<p class='error'>❌ فشل في إنشاء ملف الحماية: $file</p>";
            $failed_fixes[] = "إنشاء ملف حماية: $file";
        }
    } else {
        echo "<p class='info'>ℹ️ ملف الحماية موجود: $file</p>";
    }
}

echo "</div>";

// 4. إضافة بيانات تجريبية
echo "<div class='section'>";
echo "<h2>4. إضافة بيانات تجريبية</h2>";

try {
    $db = getUnifiedDB();
    
    // التحقق من وجود مستخدم تجريبي
    $user_check = $db->query("SELECT id FROM users WHERE username = 'testuser' LIMIT 1");
    if (!$user_check || $user_check->num_rows == 0) {
        // إنشاء مستخدم تجريبي
        $username = 'testuser';
        $email = '<EMAIL>';
        $password = password_hash('123456', PASSWORD_DEFAULT);
        $full_name = 'مستخدم تجريبي';
        
        $stmt = $db->prepare("INSERT INTO users (username, email, password, full_name, status) VALUES (?, ?, ?, ?, 'active')");
        $stmt->bind_param('ssss', $username, $email, $password, $full_name);
        
        if ($stmt->execute()) {
            $user_id = $db->insert_id;
            echo "<p class='success'>✅ تم إنشاء المستخدم التجريبي (ID: $user_id)</p>";
            $fixed_issues[] = "إنشاء مستخدم تجريبي";
            
            // إضافة عملاء تجريبيين
            $customers_data = [
                ['عميل تجريبي 1', '0501234567', '<EMAIL>', 'customer'],
                ['مورد تجريبي 1', '0509876543', '<EMAIL>', 'supplier']
            ];
            
            foreach ($customers_data as $customer_data) {
                $stmt = $db->prepare("INSERT INTO customers (user_id, name, phone, email, customer_type) VALUES (?, ?, ?, ?, ?)");
                $stmt->bind_param('issss', $user_id, $customer_data[0], $customer_data[1], $customer_data[2], $customer_data[3]);
                
                if ($stmt->execute()) {
                    echo "<p class='success'>✅ تم إضافة {$customer_data[0]}</p>";
                }
            }
            
            $fixed_issues[] = "إضافة عملاء تجريبيين";
        }
    } else {
        echo "<p class='info'>ℹ️ المستخدم التجريبي موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في إضافة البيانات التجريبية: " . $e->getMessage() . "</p>";
    $failed_fixes[] = "إضافة بيانات تجريبية: " . $e->getMessage();
}

echo "</div>";

// 5. ملخص الإصلاحات
echo "<div class='section'>";
echo "<h2>5. ملخص الإصلاحات</h2>";

echo "<h3>📊 الإحصائيات:</h3>";
echo "<p><strong>إجمالي المشاكل المُصلحة:</strong> " . count($fixed_issues) . "</p>";
echo "<p><strong>إجمالي الإصلاحات الفاشلة:</strong> " . count($failed_fixes) . "</p>";

if (!empty($fixed_issues)) {
    echo "<h3 class='success'>✅ المشاكل المُصلحة:</h3>";
    echo "<ul>";
    foreach ($fixed_issues as $issue) {
        echo "<li class='success'>$issue</li>";
    }
    echo "</ul>";
}

if (!empty($failed_fixes)) {
    echo "<h3 class='error'>❌ الإصلاحات الفاشلة:</h3>";
    echo "<ul>";
    foreach ($failed_fixes as $fix) {
        echo "<li class='error'>$fix</li>";
    }
    echo "</ul>";
}

if (empty($failed_fixes)) {
    echo "<h3 class='success'>🎉 تم إصلاح جميع المشاكل بنجاح!</h3>";
    echo "<p class='success'>النظام الآن جاهز للاستخدام بشكل كامل.</p>";
} else {
    echo "<h3 class='warning'>⚠️ تم إصلاح معظم المشاكل</h3>";
    echo "<p class='warning'>بعض الإصلاحات فشلت وتحتاج إلى تدخل يدوي.</p>";
}

echo "</div>";

// 6. الخطوات التالية
echo "<div class='section'>";
echo "<h2>6. الخطوات التالية</h2>";

echo "<h3>🔄 إعادة فحص النظام:</h3>";
echo "<p><a href='comprehensive_system_check.php'>تشغيل الفحص الشامل مرة أخرى</a></p>";

echo "<h3>🧪 اختبار النظام:</h3>";
echo "<ul>";
echo "<li><a href='test_functions.php'>اختبار الدوال</a></li>";
echo "<li><a href='test_system.php'>اختبار النظام الشامل</a></li>";
echo "<li><a href='admin_login.php'>تسجيل دخول المدير</a></li>";
echo "<li><a href='login.php'>تسجيل دخول المستخدم</a></li>";
echo "</ul>";

echo "<h3>🎯 الوصول للنظام:</h3>";
echo "<ul>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='admin_dashboard.php'>لوحة تحكم المدير</a></li>";
echo "<li><a href='admin_system.php'>إعدادات النظام</a></li>";
echo "<li><a href='admin_manage_admins.php'>إدارة المديرين</a></li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الإصلاح في: " . date('Y-m-d H:i:s') . " | ";
echo "الذاكرة المستخدمة: " . number_format(memory_get_usage(true) / 1024 / 1024, 2) . " MB";
echo "</p>";

?>
