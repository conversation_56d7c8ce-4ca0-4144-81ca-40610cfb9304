<?php
/**
 * فحص بيانات المدير في قاعدة البيانات
 */

require_once __DIR__ . '/config/init.php';

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    echo "<h2>🔍 فحص بيانات المدير في قاعدة البيانات</h2>";

    // التحقق من وجود جدول المديرين
    $check_table = $db->query("SHOW TABLES LIKE 'admins'");
    if ($check_table->num_rows == 0) {
        echo "<p style='color: red;'>❌ جدول المديرين غير موجود!</p>";
        echo "<p><a href='admin_fix_database.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء الجداول</a></p>";
        exit;
    }

    echo "<p style='color: green;'>✅ جدول المديرين موجود</p>";

    // جلب جميع المديرين
    $admins_query = "SELECT id, username, full_name, email, phone, status, created_at, last_login FROM admins ORDER BY id";
    $admins_result = $db->query($admins_query);

    if (!$admins_result) {
        throw new Exception("خطأ في استعلام المديرين: " . $db->error);
    }

    if ($admins_result->num_rows == 0) {
        echo "<p style='color: orange;'>⚠️ لا يوجد مديرين في قاعدة البيانات</p>";
        echo "<p><a href='add_admin_user.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إضافة مدير</a></p>";
    } else {
        echo "<h3>📋 قائمة المديرين الموجودين:</h3>";
        echo "<div style='background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<thead>";
        echo "<tr style='background: #f8f9fa; border-bottom: 2px solid #dee2e6;'>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>ID</th>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>اسم المستخدم</th>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>الاسم الكامل</th>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>البريد الإلكتروني</th>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>الهاتف</th>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>الحالة</th>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>تاريخ الإنشاء</th>";
        echo "<th style='padding: 12px; text-align: right; border: 1px solid #dee2e6;'>آخر دخول</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";

        while ($admin = $admins_result->fetch_assoc()) {
            $status_color = $admin['status'] === 'active' ? 'green' : 'red';
            $status_text = $admin['status'] === 'active' ? 'نشط' : 'غير نشط';
            
            echo "<tr style='border-bottom: 1px solid #dee2e6;'>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>" . $admin['id'] . "</td>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6; font-weight: bold; color: #007bff;'>" . htmlspecialchars($admin['username']) . "</td>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>" . htmlspecialchars($admin['full_name'] ?? '-') . "</td>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>" . htmlspecialchars($admin['email'] ?? '-') . "</td>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>" . htmlspecialchars($admin['phone'] ?? '-') . "</td>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6; color: $status_color; font-weight: bold;'>$status_text</td>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>" . date('Y-m-d H:i', strtotime($admin['created_at'])) . "</td>";
            echo "<td style='padding: 12px; border: 1px solid #dee2e6;'>" . ($admin['last_login'] ? date('Y-m-d H:i', strtotime($admin['last_login'])) : 'لم يسجل دخول') . "</td>";
            echo "</tr>";
        }

        echo "</tbody>";
        echo "</table>";
        echo "</div>";

        // عرض بيانات الدخول بوضوح
        echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0;'>";
        echo "<h3 style='margin-bottom: 20px;'>🔐 بيانات الدخول للمدير:</h3>";
        echo "<div style='background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;'>";
        echo "<div style='font-size: 18px; margin: 10px 0;'>";
        echo "<strong>🌐 رابط تسجيل الدخول:</strong><br>";
        echo "<a href='admin_login.php' style='color: #ffd700; text-decoration: none; font-weight: bold;'>http://localhost:808/salessystem_v2/admin_login.php</a>";
        echo "</div>";
        echo "<div style='font-size: 18px; margin: 10px 0;'>";
        echo "<strong>👤 اسم المستخدم:</strong> <span style='background: rgba(255,255,255,0.2); padding: 5px 10px; border-radius: 5px; font-family: monospace;'>admin</span>";
        echo "</div>";
        echo "<div style='font-size: 18px; margin: 10px 0;'>";
        echo "<strong>🔑 كلمة المرور:</strong> <span style='background: rgba(255,255,255,0.2); padding: 5px 10px; border-radius: 5px; font-family: monospace;'>admin123</span>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }

    // فحص المستخدمين أيضاً
    echo "<hr style='margin: 30px 0;'>";
    echo "<h3>👥 فحص المستخدمين العاديين:</h3>";

    // التحقق من وجود جدول المستخدمين أولاً
    $check_users_table = $db->query("SHOW TABLES LIKE 'users'");
    if ($check_users_table->num_rows == 0) {
        echo "<p style='color: orange;'>⚠️ جدول المستخدمين غير موجود</p>";
        $users_result = null;
    } else {
        // التحقق من الأعمدة الموجودة في جدول المستخدمين
        $columns_query = $db->query("SHOW COLUMNS FROM users");
        $available_columns = [];
        while ($column = $columns_query->fetch_assoc()) {
            $available_columns[] = $column['Field'];
        }

        // بناء الاستعلام بناءً على الأعمدة المتاحة
        $select_columns = ['id', 'username'];
        if (in_array('full_name', $available_columns)) $select_columns[] = 'full_name';
        if (in_array('email', $available_columns)) $select_columns[] = 'email';
        if (in_array('phone', $available_columns)) $select_columns[] = 'phone';
        if (in_array('status', $available_columns)) $select_columns[] = 'status';
        elseif (in_array('is_active', $available_columns)) $select_columns[] = 'is_active';
        if (in_array('created_at', $available_columns)) $select_columns[] = 'created_at';
        if (in_array('last_login', $available_columns)) $select_columns[] = 'last_login';

        $users_query = "SELECT " . implode(', ', $select_columns) . " FROM users ORDER BY id LIMIT 5";
        $users_result = $db->query($users_query);
    }

    if ($users_result && $users_result->num_rows > 0) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 10px;'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<thead>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 10px; text-align: right;'>اسم المستخدم</th>";
        echo "<th style='padding: 10px; text-align: right;'>الاسم الكامل</th>";
        echo "<th style='padding: 10px; text-align: right;'>الحالة</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";

        while ($user = $users_result->fetch_assoc()) {
            // تحديد الحالة بناءً على العمود المتاح
            if (isset($user['status'])) {
                $status_color = $user['status'] === 'active' ? 'green' : 'red';
                $status_text = $user['status'] === 'active' ? 'نشط' : 'غير نشط';
            } elseif (isset($user['is_active'])) {
                $status_color = $user['is_active'] ? 'green' : 'red';
                $status_text = $user['is_active'] ? 'نشط' : 'غير نشط';
            } else {
                $status_color = 'gray';
                $status_text = 'غير محدد';
            }

            echo "<tr>";
            echo "<td style='padding: 10px; font-weight: bold;'>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($user['full_name'] ?? '-') . "</td>";
            echo "<td style='padding: 10px; color: $status_color; font-weight: bold;'>$status_text</td>";
            echo "</tr>";
        }

        echo "</tbody>";
        echo "</table>";
        echo "</div>";

        // عرض بيانات دخول المستخدم التجريبي
        echo "<div style='background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 15px; margin: 20px 0;'>";
        echo "<h4>👤 بيانات دخول المستخدم التجريبي:</h4>";
        echo "<div style='background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;'>";
        echo "<div style='margin: 8px 0;'><strong>🌐 رابط:</strong> <a href='login.php' style='color: #ffd700;'>login.php</a></div>";
        echo "<div style='margin: 8px 0;'><strong>👤 اسم المستخدم:</strong> <span style='background: rgba(255,255,255,0.2); padding: 3px 8px; border-radius: 3px;'>user</span></div>";
        echo "<div style='margin: 8px 0;'><strong>🔑 كلمة المرور:</strong> <span style='background: rgba(255,255,255,0.2); padding: 3px 8px; border-radius: 3px;'>user123</span></div>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا يوجد مستخدمين في النظام</p>";
    }

    // روابط سريعة
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h3>🔗 روابط سريعة:</h3>";
    echo "<a href='admin_login.php' style='background: #dc3545; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px; display: inline-block;'>🔐 دخول المدير</a>";
    echo "<a href='login.php' style='background: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px; display: inline-block;'>👤 دخول المستخدم</a>";
    echo "<a href='system_info.php' style='background: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px; display: inline-block;'>📊 معلومات النظام</a>";
    echo "<a href='add_admin_user.php' style='background: #6f42c1; color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; margin: 5px; display: inline-block;'>➕ إضافة مدير</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    ErrorHandler::logError('ERROR', 'Check admin error: ' . $e->getMessage(), __FILE__, __LINE__);
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

h2, h3 {
    color: #333;
    margin: 20px 0;
}

table {
    font-size: 14px;
}

a {
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>
