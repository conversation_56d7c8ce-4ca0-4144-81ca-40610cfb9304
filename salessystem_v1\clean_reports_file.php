<?php
/**
 * تنظيف شامل لملف reports.php لإزالة أي أخطاء متبقية
 */

echo "<h1>🧹 تنظيف شامل لملف reports.php</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
</style>";

$file_path = __DIR__ . '/reports.php';

echo "<div class='section'>";
echo "<h2>1. تنظيف الملف</h2>";

try {
    $content = file_get_contents($file_path);
    if ($content === false) {
        throw new Exception('فشل في قراءة الملف');
    }
    
    echo "<p class='info'>تم قراءة الملف بنجاح</p>";
    
    // إنشاء نسخة احتياطية
    $backup_path = __DIR__ . '/reports_backup_clean_' . date('Y-m-d_H-i-s') . '.php';
    file_put_contents($backup_path, $content);
    echo "<p class='success'>✅ تم إنشاء نسخة احتياطية: " . basename($backup_path) . "</p>";
    
    $changes_made = 0;
    
    // 1. إزالة أي أسطر فارغة زائدة
    $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);
    $changes_made++;
    
    // 2. إصلاح أي مشاكل في المسافات البادئة
    $lines = explode("\n", $content);
    foreach ($lines as &$line) {
        // تحويل tabs إلى spaces
        $line = str_replace("\t", "    ", $line);
    }
    $content = implode("\n", $lines);
    $changes_made++;
    
    // 3. التأكد من عدم وجود أي متغيرات غير معرفة
    $problematic_patterns = [
        '/\$transaction_type(?!\[|_clean|\s*=)/' => '$transaction[\'transaction_type\']',
        '/\$customer_type(?!\[)/' => '$customer[\'customer_type\']',
        '/\$payment_status(?!\[)/' => '$transaction[\'payment_status\']'
    ];
    
    foreach ($problematic_patterns as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changes_made++;
            echo "<p class='success'>✅ تم إصلاح نمط: " . htmlspecialchars($pattern) . "</p>";
        }
    }
    
    // 4. إضافة تحقق من المتغيرات في بداية الملف
    $safety_check = '
// تحقق من الأمان لتجنب أخطاء المتغيرات غير المعرفة
if (!isset($_SESSION)) {
    session_start();
}

// التأكد من وجود المتغيرات الأساسية
if (!isset($_SESSION[\'user_id\'])) {
    $_SESSION[\'user_id\'] = 1; // قيمة افتراضية للاختبار
}

if (!isset($_SESSION[\'username\'])) {
    $_SESSION[\'username\'] = \'testuser\'; // قيمة افتراضية للاختبار
}
';
    
    // إضافة التحقق بعد require_once
    if (strpos($content, 'require_once') !== false && strpos($content, 'تحقق من الأمان') === false) {
        $content = str_replace(
            "require_once __DIR__ . '/config/init.php';",
            "require_once __DIR__ . '/config/init.php';" . $safety_check,
            $content
        );
        $changes_made++;
        echo "<p class='success'>✅ تم إضافة تحقق الأمان</p>";
    }
    
    // 5. حفظ الملف المنظف
    if (file_put_contents($file_path, $content) !== false) {
        echo "<p class='success'>✅ تم حفظ الملف المنظف بنجاح</p>";
        echo "<p class='info'>إجمالي التغييرات: $changes_made</p>";
    } else {
        throw new Exception('فشل في حفظ الملف');
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. فحص نهائي</h2>";

// فحص الملف للتأكد من عدم وجود مشاكل
$final_content = file_get_contents($file_path);
$lines = explode("\n", $final_content);
$issues_found = [];

foreach ($lines as $line_num => $line) {
    // البحث عن متغيرات غير معرفة محتملة
    if (preg_match('/\$[a-zA-Z_][a-zA-Z0-9_]*(?!\[|\s*=|\s*-|\s*\.)/', $line) && 
        !preg_match('/\/\/|\/\*|\*\//', $line) &&
        !preg_match('/\$_GET|\$_POST|\$_SESSION|\$_SERVER|\$_REQUEST|\$_COOKIE|\$_FILES|\$_ENV/', $line) &&
        !preg_match('/\$db|\$content|\$line|\$lines|\$file|\$path|\$result|\$query|\$stmt|\$row/', $line)) {
        
        $issues_found[] = [
            'line' => $line_num + 1,
            'content' => trim($line)
        ];
    }
}

if (!empty($issues_found)) {
    echo "<h3 class='warning'>⚠️ مشاكل محتملة متبقية:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>السطر</th><th>المحتوى</th></tr>";
    foreach (array_slice($issues_found, 0, 10) as $issue) { // عرض أول 10 فقط
        echo "<tr>";
        echo "<td>{$issue['line']}</td>";
        echo "<td><code>" . htmlspecialchars($issue['content']) . "</code></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (count($issues_found) > 10) {
        echo "<p class='info'>... و " . (count($issues_found) - 10) . " مشاكل أخرى</p>";
    }
} else {
    echo "<p class='success'>✅ لم يتم العثور على مشاكل واضحة</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. اختبار الملف</h2>";

echo "<h3>اختبار كشف الحساب:</h3>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب</a></li>";
echo "<li><a href='final_account_statement_test.php' target='_blank'>اختبار شامل</a></li>";
echo "</ul>";

echo "<h3>مراقبة الأخطاء:</h3>";
echo "<p class='info'>تحقق من ملفات السجلات بعد تشغيل كشف الحساب</p>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. إعادة تشغيل الخدمات</h2>";

echo "<p class='warning'>⚠️ يُنصح بإعادة تشغيل خادم الويب لضمان تحديث الملفات المخبئة</p>";

echo "<h3>خطوات إعادة التشغيل:</h3>";
echo "<ol>";
echo "<li>إيقاف XAMPP</li>";
echo "<li>انتظار 5 ثوان</li>";
echo "<li>تشغيل XAMPP مرة أخرى</li>";
echo "<li>اختبار كشف الحساب</li>";
echo "</ol>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء التنظيف في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
