<?php
/**
 * إصلاح شامل لجميع مشاكل admin_users.php
 */

$file_path = __DIR__ . '/admin_users.php';

echo "<h2>🔧 إصلاح شامل لملف admin_users.php</h2>";

if (!file_exists($file_path)) {
    echo "<div style='color: red;'>❌ الملف غير موجود</div>";
    exit;
}

$content = file_get_contents($file_path);
if ($content === false) {
    echo "<div style='color: red;'>❌ فشل في قراءة الملف</div>";
    exit;
}

// إنشاء نسخة احتياطية
$backup_path = $file_path . '.complete_fix_backup.' . date('Y-m-d-H-i-s');
copy($file_path, $backup_path);

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>🔧 الإصلاحات المطبقة:</h4>";

$fixes_applied = [];

// 1. إصلاح مشاكل PHP الأساسية
$php_fixes = [
    // إصلاح التعليقات والتنسيق
    ' // التحقق من الصلاحيات if (!hasAdminPermission(\'manage_users\')) {' => 
    "\n// التحقق من الصلاحيات\nif (!hasAdminPermission('manage_users')) {",
    
    ' try {' => "\ntry {",
    ' $db = getUnifiedDB();' => '    $db = getUnifiedDB();',
    ' if (!$db) {' => '    if (!$db) {',
    ' throw new Exception("فشل الاتصال بقاعدة البيانات");' => '        throw new Exception("فشل الاتصال بقاعدة البيانات");',
    
    // إصلاح switch case
    ' case \'toggle_status\': if ($user_id > 0) {' => 
    "        case 'toggle_status':\n            if (\$user_id > 0) {",
    
    ' case \'reset_password\': if ($user_id > 0) {' => 
    "        case 'reset_password':\n            if (\$user_id > 0) {",
    
    ' case \'delete_user\': if ($user_id > 0) {' => 
    "        case 'delete_user':\n            if (\$user_id > 0) {",
    
    ' case \'bulk_action\': $selected_users = $_POST[\'selected_users\'] ?? [];' => 
    "        case 'bulk_action':\n            \$selected_users = \$_POST['selected_users'] ?? [];",
    
    // إصلاح المصفوفات
    ' // حذف بيانات المستخدم من الجداول المختلفة $tables_to_clean = [' => 
    "                // حذف بيانات المستخدم من الجداول المختلفة\n                \$tables_to_clean = [",
    
    // إصلاح الدوال المكسورة
    ' $clean_stmt->' => '                    $clean_stmt->',
    'bind_param("i", $user_id);' => 'bind_param("i", $user_id);',
    'execute();' => 'execute();',
    'close();' => 'close();',
    
    // إصلاح المتغيرات
    ' // جلب المستخدمين مع الإحصائيات والترقيم' => 
    "\n// جلب المستخدمين مع الإحصائيات والترقيم",
    
    '$search = $_GET[\'search\'] ?? \'\';' => '$search = $_GET[\'search\'] ?? \'\';',
    ' $status_filter = $_GET[\'status\'] ?? \'\';' => '$status_filter = $_GET[\'status\'] ?? \'\';',
    ' $page = max(1, intval($_GET[\'page\'] ?? 1));' => '$page = max(1, intval($_GET[\'page\'] ?? 1));',
    ' $per_page = 20;' => '$per_page = 20;',
    ' $offset = ($page - 1) * $per_page;' => '$offset = ($page - 1) * $per_page;',
    ' $where_conditions = [];' => '$where_conditions = [];',
    ' $params = [];' => '$params = [];',
    ' $param_types = \'\';' => '$param_types = \'\';',
    
    // إصلاح الشروط
    ' if (!empty($search)) {' => 'if (!empty($search)) {',
    ' $where_conditions[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ?)";' => 
    '    $where_conditions[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ?)";',
    ' $search_param = "%$search%";' => '    $search_param = "%$search%";',
    ' $params[] = $search_param;' => '    $params[] = $search_param;',
    ' $param_types .= \'sss\';' => '    $param_types .= \'sss\';',
    
    ' if ($status_filter !== \'\') {' => 'if ($status_filter !== \'\') {',
    ' $where_conditions[] = "status = ?";' => '    $where_conditions[] = "status = ?";',
    ' $params[] = $status_filter === \'1\' ? \'active\' : \'inactive\';' => 
    '    $params[] = $status_filter === \'1\' ? \'active\' : \'inactive\';',
    ' $param_types .= \'s\';' => '    $param_types .= \'s\';',
    
    ' $where_clause = !empty($where_conditions) ? \'WHERE \' . implode(\' AND \', $where_conditions) : \'\';' => 
    '$where_clause = !empty($where_conditions) ? \'WHERE \' . implode(\' AND \', $where_conditions) : \'\';',
    
    // إصلاح الاستعلامات
    ' // استعلام العد الإجمالي $count_query = "SELECT COUNT(*) as total FROM users $where_clause";' => 
    "\n// استعلام العد الإجمالي\n\$count_query = \"SELECT COUNT(*) as total FROM users \$where_clause\";",
    
    ' if (!empty($params)) {' => 'if (!empty($params)) {',
    ' $count_stmt = $db->prepare($count_query);' => '    $count_stmt = $db->prepare($count_query);',
    ' if (!$count_stmt) {' => '    if (!$count_stmt) {',
    
    // إصلاح else
    '}' . "\n" . 'else {' => "}\nelse {",
    
    // إصلاح الاستعلام الرئيسي
    ' // استعلام البيانات مع الترقيم (تصحيح اسم العمود) $query = "SELECT id, username, full_name, email, phone, status, last_login, created_at FROM users $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";' => 
    "\n// استعلام البيانات مع الترقيم\n\$query = \"SELECT id, username, full_name, email, phone, status, last_login, created_at FROM users \$where_clause ORDER BY created_at DESC LIMIT \$per_page OFFSET \$offset\";",
];

foreach ($php_fixes as $wrong => $correct) {
    if (strpos($content, $wrong) !== false) {
        $content = str_replace($wrong, $correct, $content);
        $fixes_applied[] = "إصلاح PHP: " . substr($wrong, 0, 50) . "...";
    }
}

// 2. إصلاح JavaScript
$js_fixes = [
    ' // تصدير بيانات المستخدمين function exportUsers() {' => 
    "\n// تصدير بيانات المستخدمين\nfunction exportUsers() {",
    
    ' const selectedUsers = document.querySelectorAll(\'input[name="selected_users[]"]:checked\');' => 
    '    const selectedUsers = document.querySelectorAll(\'input[name="selected_users[]"]:checked\');',
    
    ' let url = \'admin_export.php?type=users\';' => 
    '    let url = \'admin_export.php?type=users\';',
    
    ' if (selectedUsers.length >' => '    if (selectedUsers.length >',
    
    ' const userIds = Array.from(selectedUsers).map(cb =>' => 
    '        const userIds = Array.from(selectedUsers).map(cb =>',
    
    ' cb.value);' => '            cb.value);',
    
    ' url += \'&user_ids=\' + userIds.join(\',\');' => 
    '        url += \'&user_ids=\' + userIds.join(\',\');',
    
    ' window.open(url, \'_blank\');' => '    window.open(url, \'_blank\');',
    
    ' // طباعة قائمة المستخدمين function printUsers() {' => 
    "\n// طباعة قائمة المستخدمين\nfunction printUsers() {",
    
    ' window.print();' => '    window.print();',
    
    ' // العمليات الجماعية function bulkAction() {' => 
    "\n// العمليات الجماعية\nfunction bulkAction() {",
    
    ' const checkboxes = document.querySelectorAll(\'input[name="selected_users[]"]:checked\');' => 
    '    const checkboxes = document.querySelectorAll(\'input[name="selected_users[]"]:checked\');',
    
    ' const action = document.getElementById(\'bulk_action\').value;' => 
    '    const action = document.getElementById(\'bulk_action\').value;',
    
    ' if (checkboxes.length === 0) {' => '    if (checkboxes.length === 0) {',
    
    ' alert(\'يرجى اختيار مستخدم واحد على الأقل\');' => 
    '        alert(\'يرجى اختيار مستخدم واحد على الأقل\');',
    
    ' return;' => '        return;',
    
    ' if (!action) {' => '    if (!action) {',
    
    ' alert(\'يرجى اختيار عملية للتنفيذ\');' => 
    '        alert(\'يرجى اختيار عملية للتنفيذ\');',
    
    ' if (confirm(\'هل أنت متأكد من تنفيذ هذه العملية على \' + checkboxes.length + \' مستخدم؟\')) {' => 
    '    if (confirm(\'هل أنت متأكد من تنفيذ هذه العملية على \' + checkboxes.length + \' مستخدم؟\')) {',
    
    ' document.getElementById(\'bulk_form\').submit();' => 
    '        document.getElementById(\'bulk_form\').submit();',
    
    ' // إعادة تعيين كلمة مرور المستخدم function resetUserPassword(userId) {' => 
    "\n// إعادة تعيين كلمة مرور المستخدم\nfunction resetUserPassword(userId) {",
    
    ' if (confirm(\'هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟\')) {' => 
    '    if (confirm(\'هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟\')) {',
    
    ' const form = document.createElement(\'form\');' => 
    '        const form = document.createElement(\'form\');',
    
    ' form.method = \'POST\';' => '        form.method = \'POST\';',
    
    ' form.innerHTML = `' => '        form.innerHTML = `',
];

foreach ($js_fixes as $wrong => $correct) {
    if (strpos($content, $wrong) !== false) {
        $content = str_replace($wrong, $correct, $content);
        $fixes_applied[] = "إصلاح JavaScript: " . substr($wrong, 0, 50) . "...";
    }
}

// 3. إصلاح مشاكل عامة
$general_fixes = [
    // إصلاح الأقواس المكسورة
    '$db->' . "\n" . 'error' => '$db->error',
    '$stmt->' . "\n" . 'error' => '$stmt->error',
    '$e->' . "\n" . 'getMessage()' => '$e->getMessage()',
    
    // إصلاح المسافات الزائدة
    "\n\n\n" => "\n\n",
    "  \n" => "\n",
    " \n" => "\n",
];

foreach ($general_fixes as $wrong => $correct) {
    $content = str_replace($wrong, $correct, $content);
}

$fixes_applied[] = "تنظيف المسافات الزائدة";

echo "<ul>";
foreach ($fixes_applied as $fix) {
    echo "<li>✅ $fix</li>";
}
echo "</ul>";
echo "</div>";

// حفظ الملف المُصلح
if (file_put_contents($file_path, $content) !== false) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>🎉 تم إصلاح الملف بنجاح!</h3>";
    echo "<p>📁 نسخة احتياطية: " . basename($backup_path) . "</p>";
    echo "<p>🔧 عدد الإصلاحات: " . count($fixes_applied) . "</p>";
    echo "</div>";
} else {
    echo "<div style='background: #ffebee; color: #d32f2f; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>❌ فشل في حفظ الملف</h3>";
    echo "</div>";
}

// روابط الاختبار
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار النتائج:</h4>";
echo "<a href='admin_users.php' target='_blank' style='background: #4caf50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🧪 اختبار admin_users.php</a>";
echo "<a href='comprehensive_error_scanner.php' style='background: #2196f3; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🔍 فحص شامل</a>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🏠 لوحة التحكم</a>";
echo "</div>";
?>
