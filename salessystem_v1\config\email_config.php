<?php
/**
 * إعدادات البريد الإلكتروني
 * Email Configuration
 */

// إعدادات SMTP (للاستخدام مع PHPMailer في بيئة الإنتاج)
define('SMTP_HOST', 'smtp.hostinger.com'); // خادم SMTP
define('SMTP_PORT', 465); // منفذ SMTP
define('SMTP_USERNAME', '<EMAIL>'); // اسم المستخدم
define('SMTP_PASSWORD', 'dNz35nd5@'); // كلمة مرور التطبيق
define('SMTP_ENCRYPTION', 'tls'); // نوع التشفير

// إعدادات البريد الإلكتروني العامة
define('FROM_EMAIL', '<EMAIL>'); // البريد المرسل
define('FROM_NAME', 'نظام إدارة المبيعات'); // اسم المرسل
define('REPLY_TO_EMAIL', '<EMAIL>'); // البريد للرد

// إعدادات إعادة تعيين كلمة المرور
define('RESET_TOKEN_EXPIRY', 3600); // مدة صلاحية الرمز بالثواني (ساعة واحدة)
define('RESET_EMAIL_SUBJECT', 'إعادة تعيين كلمة المرور - نظام إدارة المبيعات');

/**
 * قالب البريد الإلكتروني لإعادة تعيين كلمة المرور
 */
function getPasswordResetEmailTemplate($user_name, $reset_link) {
    return "
    <!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>إعادة تعيين كلمة المرور</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                direction: rtl;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
                background: linear-gradient(135deg, #2563eb, #1d4ed8);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 24px;
                font-weight: 600;
            }
            .content {
                padding: 30px 20px;
                line-height: 1.6;
            }
            .content p {
                margin: 15px 0;
                color: #333;
            }
            .button-container {
                text-align: center;
                margin: 30px 0;
            }
            .reset-button {
                display: inline-block;
                background: linear-gradient(135deg, #2563eb, #1d4ed8);
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 8px;
                font-weight: 600;
                font-size: 16px;
                transition: all 0.3s ease;
            }
            .reset-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            }
            .warning {
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                color: #92400e;
            }
            .footer {
                background: #f8f9fa;
                padding: 20px;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
                border-top: 1px solid #e5e7eb;
            }
            .security-note {
                background: #dbeafe;
                border: 1px solid #3b82f6;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                color: #1e40af;
            }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🔐 إعادة تعيين كلمة المرور</h1>
            </div>
            
            <div class='content'>
                <p><strong>مرحباً " . htmlspecialchars($user_name) . ",</strong></p>
                
                <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في نظام إدارة المبيعات.</p>
                
                <p>لإعادة تعيين كلمة المرور، انقر على الزر أدناه:</p>
                
                <div class='button-container'>
                    <a href='" . $reset_link . "' class='reset-button'>
                        إعادة تعيين كلمة المرور
                    </a>
                </div>
                
                <div class='warning'>
                    <strong>⚠️ تنبيه أمني:</strong><br>
                    هذا الرابط صالح لمدة ساعة واحدة فقط من وقت إرسال هذا البريد.
                </div>
                
                <div class='security-note'>
                    <strong>💡 نصائح أمنية:</strong><br>
                    • لا تشارك هذا الرابط مع أي شخص آخر<br>
                    • استخدم كلمة مرور قوية تحتوي على أحرف وأرقام ورموز<br>
                    • لا تستخدم نفس كلمة المرور في مواقع أخرى
                </div>
                
                <p><strong>إذا لم تطلب إعادة تعيين كلمة المرور:</strong></p>
                <p>يرجى تجاهل هذا البريد الإلكتروني. حسابك آمن ولن يتم تغيير أي شيء.</p>
                
                <p>إذا كنت تواجه مشاكل مع الرابط أعلاه، يمكنك نسخ ولصق الرابط التالي في متصفحك:</p>
                <p style='word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace;'>
                    " . $reset_link . "
                </p>
            </div>
            
            <div class='footer'>
                <p><strong>نظام إدارة المبيعات</strong></p>
                <p>&copy; " . date('Y') . " جميع الحقوق محفوظة</p>
                <p>هذا بريد إلكتروني تلقائي، يرجى عدم الرد عليه</p>
            </div>
        </div>
    </body>
    </html>
    ";
}

/**
 * إرسال بريد إلكتروني محسن لإعادة تعيين كلمة المرور
 */
function sendEnhancedPasswordResetEmail($email, $reset_link, $user_name) {
    $subject = RESET_EMAIL_SUBJECT;
    $message = getPasswordResetEmailTemplate($user_name, $reset_link);

    // الطريقة 1: PHPMailer (الأفضل للإنتاج)
    // تحميل PHPMailer من المسار الصحيح
    $phpmailer_autoload = __DIR__ . '/../vendor/autoload.php';
    $phpmailer_manual = __DIR__ . '/../vendor/phpmailer/phpmailer/src/PHPMailer.php';

    if (file_exists($phpmailer_autoload)) {
        require_once $phpmailer_autoload;
    } elseif (file_exists($phpmailer_manual)) {
        require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/Exception.php';
        require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/PHPMailer.php';
        require_once __DIR__ . '/../vendor/phpmailer/phpmailer/src/SMTP.php';
    }

    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        try {
            // إعدادات الخادم
            $mail->isSMTP();
            $mail->Host = SMTP_HOST;
            $mail->SMTPAuth = true;
            $mail->Username = SMTP_USERNAME;
            $mail->Password = SMTP_PASSWORD;
            $mail->SMTPSecure = SMTP_ENCRYPTION;
            $mail->Port = SMTP_PORT;
            $mail->CharSet = 'UTF-8';

            // المرسل والمستقبل
            $mail->setFrom(FROM_EMAIL, FROM_NAME);
            $mail->addAddress($email, $user_name);
            $mail->addReplyTo(REPLY_TO_EMAIL, FROM_NAME);

            // المحتوى
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $message;

            $mail->send();
            error_log("Password reset email sent successfully via PHPMailer to: $email");
            return true;
        } catch (Exception $e) {
            error_log("فشل إرسال البريد الإلكتروني عبر PHPMailer: " . $e->getMessage());
        }
    }

    // الطريقة 2: دالة mail() البسيطة مع إعدادات محسنة
    return sendSimplePasswordResetEmail($email, $reset_link, $user_name);

    // الطريقة 3: محاكاة للاختبار (معطلة - تم التحويل للإرسال الحقيقي)
    /*
    $log_file = 'email_logs/password_reset_' . date('Y-m-d') . '.html';
    $log_dir = dirname($log_file);

    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }

    $log_content = "
    <div style='border: 2px solid #ccc; margin: 20px 0; padding: 20px;'>
        <h3>بريد إلكتروني مرسل في: " . date('Y-m-d H:i:s') . "</h3>
        <p><strong>إلى:</strong> $email</p>
        <p><strong>الموضوع:</strong> $subject</p>
        <hr>
        $message
        <hr>
    </div>
    ";

    file_put_contents($log_file, $log_content, FILE_APPEND | LOCK_EX);

    // تسجيل في ملف النشاط
    error_log("Password reset email sent to: $email for user: $user_name at " . date('Y-m-d H:i:s'));

    return true;
    */
}

/**
 * إرسال بريد إلكتروني عبر cURL مع Gmail SMTP (مبسط)
 */
function sendEmailViaCurl($email, $subject, $message, $user_name) {
    // تحقق من وجود cURL
    if (!function_exists('curl_init')) {
        error_log("cURL not available for email sending");
        return false;
    }

    try {
        // إنشاء رسالة بسيطة
        $simple_message = "
مرحباً $user_name,

تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في نظام إدارة المبيعات.

يرجى استخدام الرابط التالي لإعادة تعيين كلمة المرور:
[سيتم إرسال الرابط عبر طريقة أخرى]

هذا الرابط صالح لمدة ساعة واحدة فقط.

إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد.

نظام إدارة المبيعات
" . date('Y-m-d H:i:s');

        // محاولة إرسال بسيط عبر cURL (للاختبار فقط)
        $ch = curl_init();

        if ($ch === false) {
            error_log("Failed to initialize cURL for email sending");
            return false;
        }

        // إعدادات بسيطة
        curl_setopt($ch, CURLOPT_URL, "https://httpbin.org/post"); // خدمة اختبار
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            'to' => $email,
            'subject' => $subject,
            'message' => $simple_message,
            'method' => 'curl_test'
        ]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $result = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);

        if ($result && empty($error)) {
            error_log("Email test via cURL completed for: $email");
            return false; // نعيد false لأن هذا مجرد اختبار
        } else {
            error_log("cURL test failed for: $email. Error: $error");
            return false;
        }

    } catch (Exception $e) {
        error_log("Exception in sendEmailViaCurl: " . $e->getMessage());
        return false;
    }
}

/**
 * إرسال بريد إلكتروني عبر sendmail مباشرة (بديل أخير)
 */
function sendEmailViaSendmail($email, $subject, $message, $user_name) {
    // إنشاء رسالة بسيطة
    $simple_message = "
مرحباً $user_name,

تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك في نظام إدارة المبيعات.

للأسف، لا يمكن إرسال البريد الإلكتروني بالتنسيق المطلوب حالياً.
يرجى الاتصال بالدعم الفني لإعادة تعيين كلمة المرور يدوياً.

نظام إدارة المبيعات
" . date('Y-m-d H:i:s');

    // محاولة استخدام sendmail إذا كان متوفراً
    $sendmail_path = ini_get('sendmail_path');
    if (!empty($sendmail_path) && file_exists(dirname($sendmail_path))) {
        $headers = "From: " . FROM_EMAIL . "\r\n";
        $headers .= "Reply-To: " . REPLY_TO_EMAIL . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

        if (@mail($email, $subject, $simple_message, $headers)) {
            error_log("Password reset email sent via sendmail to: $email");
            return true;
        }
    }

    // إذا فشل كل شيء، احفظ في ملف للمراجعة اليدوية
    $fallback_file = 'email_logs/failed_emails_' . date('Y-m-d') . '.txt';
    $fallback_dir = dirname($fallback_file);

    if (!is_dir($fallback_dir)) {
        mkdir($fallback_dir, 0755, true);
    }

    $fallback_content = "
=== فشل إرسال بريد إلكتروني ===
التاريخ: " . date('Y-m-d H:i:s') . "
إلى: $email
الموضوع: $subject
المستخدم: $user_name
الرسالة: $simple_message
=====================================

";

    file_put_contents($fallback_file, $fallback_content, FILE_APPEND | LOCK_EX);
    error_log("Email sending failed completely. Saved to fallback file for manual review: $email");

    return false;
}

/**
 * إرسال بريد إلكتروني بسيط باستخدام دالة mail() المدمجة
 */
function sendSimplePasswordResetEmail($email, $reset_link, $user_name) {
    $subject = "إعادة تعيين كلمة المرور - نظام إدارة المبيعات";

    // تحديث إعدادات SMTP مؤقتاً
    $original_smtp = ini_get('SMTP');
    $original_port = ini_get('smtp_port');
    $original_from = ini_get('sendmail_from');

    // تطبيق إعدادات Gmail
    ini_set('SMTP', SMTP_HOST);
    ini_set('smtp_port', SMTP_PORT);
    ini_set('sendmail_from', FROM_EMAIL);

    // رسالة بسيطة
    $message = "
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>إعادة تعيين كلمة المرور</title>
    </head>
    <body style='font-family: Arial, sans-serif; direction: rtl;'>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
            <h2 style='color: #2563eb;'>إعادة تعيين كلمة المرور</h2>
            <p>مرحباً " . htmlspecialchars($user_name) . ",</p>
            <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك.</p>
            <p>انقر على الرابط أدناه لإعادة تعيين كلمة المرور:</p>
            <p><a href='" . $reset_link . "' style='background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة تعيين كلمة المرور</a></p>
            <p><strong>ملاحظة:</strong> هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
            <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد.</p>
            <hr>
            <p style='color: #666; font-size: 12px;'>نظام إدارة المبيعات &copy; " . date('Y') . "</p>
        </div>
    </body>
    </html>
    ";

    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= 'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>' . "\r\n";
    $headers .= 'Reply-To: ' . REPLY_TO_EMAIL . "\r\n";
    $headers .= 'X-Mailer: PHP/' . phpversion();

    // محاولة الإرسال
    $result = @mail($email, $subject, $message, $headers);

    // استعادة الإعدادات الأصلية
    if ($original_smtp) ini_set('SMTP', $original_smtp);
    if ($original_port) ini_set('smtp_port', $original_port);
    if ($original_from) ini_set('sendmail_from', $original_from);

    if ($result) {
        error_log("Password reset email sent successfully via mail() to: $email");
        return true;
    } else {
        $last_error = error_get_last();
        error_log("Failed to send password reset email via mail() to: $email. Error: " . ($last_error['message'] ?? 'Unknown error'));

        // كبديل أخير، احفظ في ملف
        return saveEmailToFile($email, $subject, $message, $user_name);
    }
}

/**
 * حفظ البريد في ملف كبديل أخير
 */
function saveEmailToFile($email, $subject, $message, $user_name) {
    $fallback_file = 'email_logs/failed_emails_' . date('Y-m-d') . '.html';
    $fallback_dir = dirname($fallback_file);

    if (!is_dir($fallback_dir)) {
        mkdir($fallback_dir, 0755, true);
    }

    $fallback_content = "
    <div style='border: 2px solid #dc3545; margin: 20px 0; padding: 20px; background: #f8d7da;'>
        <h3 style='color: #721c24;'>فشل إرسال بريد إلكتروني - " . date('Y-m-d H:i:s') . "</h3>
        <p><strong>إلى:</strong> $email</p>
        <p><strong>المستخدم:</strong> $user_name</p>
        <p><strong>الموضوع:</strong> $subject</p>
        <p><strong>الحالة:</strong> فشل الإرسال - تم الحفظ للمراجعة اليدوية</p>
        <hr>
        <div style='border: 1px solid #ccc; padding: 10px; background: white;'>
            $message
        </div>
        <hr>
        <p style='color: #721c24;'><strong>يرجى إرسال هذا البريد يدوياً أو إصلاح إعدادات SMTP</strong></p>
    </div>
    ";

    file_put_contents($fallback_file, $fallback_content, FILE_APPEND | LOCK_EX);
    error_log("Email sending failed completely. Saved to fallback file for manual review: $email");

    return false; // نعيد false لأن الإرسال فشل
}

?>
