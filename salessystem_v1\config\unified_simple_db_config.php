<?php
/**
 * ملف إعدادات قاعدة البيانات الموحد والمبسط
 */

function getSimpleDB() {
    $mysqli = new mysqli("localhost", "root", "", "salessystem_v2");
    
    if ($mysqli->connect_error) {
        error_log("Database connection failed: " . $mysqli->connect_error);
        return null;
    }
    
    $mysqli->set_charset("utf8mb4");
    return $mysqli;
}

// دوال التوافق للملفات القديمة
if (!function_exists("getUnifiedDB")) {
    function getUnifiedDB() { return getSimpleDB(); }
}

if (!function_exists("getMainDB")) {
    function getMainDB() { return getSimpleDB(); }
}

if (!function_exists("getOperationsDB")) {
    function getOperationsDB() { return getSimpleDB(); }
}

// دالة اختبار
function testSimpleConnection() {
    $db = getSimpleDB();
    if (!$db) return ["success" => false, "error" => "فشل الاتصال"];
    
    $result = $db->query("SELECT 1");
    $db->close();
    
    return $result ? 
        ["success" => true, "message" => "نجح الاتصال"] : 
        ["success" => false, "error" => "فشل في الاستعلام"];
}
?>