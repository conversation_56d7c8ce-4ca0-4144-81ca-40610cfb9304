<?php
/**
 * إنشاء المشرف التجريبي
 */
require_once 'config/simple_db_config.php';

echo '<h2>إنشاء المشرف التجريبي</h2>';

// الحصول على اتصال قاعدة البيانات
$db = getSimpleDB();
if (!$db) {
    echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
    exit;
}

echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات بنجاح</p>';

// إنشاء الجداول أولاً
echo '<h3>إنشاء الجداول:</h3>';
if (createUnifiedTables($db)) {
    echo '<p style="color: green;">✅ تم إنشاء/التحقق من الجداول بنجاح</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء الجداول</p>';
}

// التحقق من وجود جدول المشرفين
$result = $db->query("SHOW TABLES LIKE 'supervisors'");
if ($result && $result->num_rows > 0) {
    echo '<p style="color: green;">✅ جدول المشرفين موجود</p>';
    
    // عرض هيكل الجدول
    $structure = $db->query("DESCRIBE supervisors");
    if ($structure) {
        echo '<h4>هيكل جدول المشرفين:</h4>';
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>';
        while ($row = $structure->fetch_assoc()) {
            echo '<tr>';
            echo '<td>' . htmlspecialchars($row['Field']) . '</td>';
            echo '<td>' . htmlspecialchars($row['Type']) . '</td>';
            echo '<td>' . htmlspecialchars($row['Null']) . '</td>';
            echo '<td>' . htmlspecialchars($row['Key']) . '</td>';
            echo '<td>' . htmlspecialchars($row['Default'] ?? 'NULL') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
} else {
    echo '<p style="color: red;">❌ جدول المشرفين غير موجود</p>';
    exit;
}

// التحقق من وجود المشرف التجريبي
echo '<h3>التحقق من المشرف التجريبي:</h3>';
$check_supervisor = $db->query("SELECT * FROM supervisors WHERE username = 'supervisor'");
if ($check_supervisor && $check_supervisor->num_rows > 0) {
    $supervisor = $check_supervisor->fetch_assoc();
    echo '<p style="color: orange;">⚠️ المشرف التجريبي موجود مسبقاً</p>';
    echo '<ul>';
    echo '<li>ID: ' . htmlspecialchars($supervisor['id']) . '</li>';
    echo '<li>اسم المستخدم: ' . htmlspecialchars($supervisor['username']) . '</li>';
    echo '<li>الاسم الكامل: ' . htmlspecialchars($supervisor['full_name']) . '</li>';
    echo '<li>البريد الإلكتروني: ' . htmlspecialchars($supervisor['email']) . '</li>';
    echo '<li>القسم: ' . htmlspecialchars($supervisor['department']) . '</li>';
    echo '<li>الحالة: ' . htmlspecialchars($supervisor['status']) . '</li>';
    echo '<li>تاريخ الإنشاء: ' . htmlspecialchars($supervisor['created_at']) . '</li>';
    echo '</ul>';
} else {
    echo '<p style="color: red;">❌ المشرف التجريبي غير موجود - سيتم إنشاؤه الآن</p>';
    
    // إنشاء المشرف التجريبي
    $username = 'supervisor';
    $email = '<EMAIL>';
    $phone = '0987654321';
    $password = password_hash('supervisor123', PASSWORD_DEFAULT);
    $full_name = 'مشرف تجريبي';
    $department = 'الإشراف العام';
    $status = 'active';
    $permissions = 'supervisor_permissions';
    
    $insert_sql = "INSERT INTO supervisors (username, email, phone, password, full_name, department, permissions, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $stmt = $db->prepare($insert_sql);
    if ($stmt) {
        $stmt->bind_param('ssssssss', $username, $email, $phone, $password, $full_name, $department, $permissions, $status);
        
        if ($stmt->execute()) {
            $supervisor_id = $db->insert_id;
            echo '<p style="color: green;">✅ تم إنشاء المشرف التجريبي بنجاح</p>';
            echo '<ul>';
            echo '<li>ID: ' . $supervisor_id . '</li>';
            echo '<li>اسم المستخدم: ' . htmlspecialchars($username) . '</li>';
            echo '<li>كلمة المرور: supervisor123</li>';
            echo '<li>الاسم الكامل: ' . htmlspecialchars($full_name) . '</li>';
            echo '<li>البريد الإلكتروني: ' . htmlspecialchars($email) . '</li>';
            echo '<li>القسم: ' . htmlspecialchars($department) . '</li>';
            echo '<li>الحالة: ' . htmlspecialchars($status) . '</li>';
            echo '</ul>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء المشرف التجريبي: ' . htmlspecialchars($stmt->error) . '</p>';
        }
        $stmt->close();
    } else {
        echo '<p style="color: red;">❌ فشل في تحضير الاستعلام: ' . htmlspecialchars($db->error) . '</p>';
    }
}

// عرض جميع المشرفين
echo '<h3>جميع المشرفين في النظام:</h3>';
$all_supervisors = $db->query("SELECT * FROM supervisors ORDER BY created_at DESC");
if ($all_supervisors && $all_supervisors->num_rows > 0) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>القسم</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>';
    while ($supervisor = $all_supervisors->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($supervisor['id']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['username']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['full_name']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['email']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['department']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['status']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['created_at']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ لا توجد مشرفين في النظام</p>';
}

echo '<h3>اختبار تسجيل الدخول:</h3>';
echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">اختبار تسجيل الدخول</a></p>';
echo '<p><strong>بيانات المشرف:</strong> supervisor / supervisor123</p>';
?>
