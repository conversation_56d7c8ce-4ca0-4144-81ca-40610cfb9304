<?php
/**
 * فحص البيانات المالية لتشخيص المشكلة
 */

require_once __DIR__ . '/config/simple_db_config.php';

$db = getSimpleDB();

if (!$db) {
    die("فشل في الاتصال بقاعدة البيانات");
}

echo "<h2>فحص البيانات المالية</h2>";

// 1. فحص جدول المستخدمين
echo "<h3>1. المستخدمين الموجودين:</h3>";
$users_result = $db->query("SELECT id, username, full_name, status FROM users ORDER BY id");
if ($users_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px;'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الحالة</th></tr>";
    while ($user = $users_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>" . $user['full_name'] . "</td>";
        echo "<td>" . $user['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ فشل في جلب المستخدمين: " . $db->error . "<br>";
}

// 2. فحص جدول المبيعات
echo "<h3>2. بيانات المبيعات:</h3>";
$sales_result = $db->query("SELECT id, user_id, invoice_number, date, total_amount, customer_id FROM sales ORDER BY date DESC LIMIT 10");
if ($sales_result) {
    if ($sales_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>Customer ID</th></tr>";
        while ($sale = $sales_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $sale['id'] . "</td>";
            echo "<td>" . $sale['user_id'] . "</td>";
            echo "<td>" . $sale['invoice_number'] . "</td>";
            echo "<td>" . $sale['date'] . "</td>";
            echo "<td>" . $sale['total_amount'] . "</td>";
            echo "<td>" . $sale['customer_id'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠️ لا توجد بيانات مبيعات<br>";
    }
} else {
    echo "❌ فشل في جلب المبيعات: " . $db->error . "<br>";
}

// 3. فحص جدول المشتريات
echo "<h3>3. بيانات المشتريات:</h3>";
$purchases_result = $db->query("SELECT id, user_id, invoice_number, date, total_amount, supplier_id FROM purchases ORDER BY date DESC LIMIT 10");
if ($purchases_result) {
    if ($purchases_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>Supplier ID</th></tr>";
        while ($purchase = $purchases_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $purchase['id'] . "</td>";
            echo "<td>" . $purchase['user_id'] . "</td>";
            echo "<td>" . $purchase['invoice_number'] . "</td>";
            echo "<td>" . $purchase['date'] . "</td>";
            echo "<td>" . $purchase['total_amount'] . "</td>";
            echo "<td>" . $purchase['supplier_id'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠️ لا توجد بيانات مشتريات<br>";
    }
} else {
    echo "❌ فشل في جلب المشتريات: " . $db->error . "<br>";
}

// 4. فحص جدول العملاء
echo "<h3>4. بيانات العملاء:</h3>";
$customers_result = $db->query("SELECT id, user_id, name, customer_type FROM customers ORDER BY id LIMIT 10");
if ($customers_result) {
    if ($customers_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>الاسم</th><th>النوع</th></tr>";
        while ($customer = $customers_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $customer['id'] . "</td>";
            echo "<td>" . $customer['user_id'] . "</td>";
            echo "<td>" . $customer['name'] . "</td>";
            echo "<td>" . $customer['customer_type'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠️ لا توجد بيانات عملاء<br>";
    }
} else {
    echo "❌ فشل في جلب العملاء: " . $db->error . "<br>";
}

// 5. اختبار الاستعلامات المستخدمة في التقارير المالية
echo "<h3>5. اختبار استعلامات التقارير المالية:</h3>";

$date_from = '2025-01-01';
$date_to = '2025-12-31';

// اختبار استعلام المبيعات لجميع المستخدمين
echo "<h4>أ. استعلام المبيعات (جميع المستخدمين):</h4>";
$sales_query = "SELECT s.*, c.name as customer_name, u.full_name as user_name
               FROM sales s 
               LEFT JOIN customers c ON s.customer_id = c.id AND c.customer_type = 'customer'
               LEFT JOIN users u ON s.user_id = u.id
               WHERE s.date BETWEEN ? AND ?
               ORDER BY s.date DESC";
$sales_stmt = $db->prepare($sales_query);
if ($sales_stmt) {
    $sales_stmt->bind_param("ss", $date_from, $date_to);
    $sales_stmt->execute();
    $sales_test_result = $sales_stmt->get_result();
    echo "✅ الاستعلام نجح، عدد النتائج: " . $sales_test_result->num_rows . "<br>";
    
    if ($sales_test_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px;'>";
        echo "<tr><th>رقم الفاتورة</th><th>التاريخ</th><th>العميل</th><th>المستخدم</th><th>المبلغ</th></tr>";
        $count = 0;
        while ($row = $sales_test_result->fetch_assoc() && $count < 5) {
            echo "<tr>";
            echo "<td>" . $row['invoice_number'] . "</td>";
            echo "<td>" . $row['date'] . "</td>";
            echo "<td>" . ($row['customer_name'] ?: 'غير محدد') . "</td>";
            echo "<td>" . ($row['user_name'] ?: 'غير محدد') . "</td>";
            echo "<td>" . $row['total_amount'] . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
    }
    $sales_stmt->close();
} else {
    echo "❌ فشل في تحضير استعلام المبيعات: " . $db->error . "<br>";
}

// اختبار استعلام إجمالي المبيعات
echo "<h4>ب. استعلام إجمالي المبيعات:</h4>";
$sales_total_query = "SELECT SUM(total_amount) as total FROM sales WHERE date BETWEEN ? AND ?";
$sales_total_stmt = $db->prepare($sales_total_query);
if ($sales_total_stmt) {
    $sales_total_stmt->bind_param("ss", $date_from, $date_to);
    $sales_total_stmt->execute();
    $sales_total_result = $sales_total_stmt->get_result();
    $total_sales = $sales_total_result->fetch_assoc()['total'] ?? 0;
    echo "✅ إجمالي المبيعات: " . number_format($total_sales, 2) . "<br>";
    $sales_total_stmt->close();
} else {
    echo "❌ فشل في استعلام إجمالي المبيعات: " . $db->error . "<br>";
}

// اختبار استعلام المشتريات
echo "<h4>ج. استعلام المشتريات (جميع المستخدمين):</h4>";
$purchases_query = "SELECT p.*, c.name as supplier_name, u.full_name as user_name
                   FROM purchases p 
                   LEFT JOIN customers c ON p.supplier_id = c.id AND c.customer_type = 'supplier'
                   LEFT JOIN users u ON p.user_id = u.id
                   WHERE p.date BETWEEN ? AND ?
                   ORDER BY p.date DESC";
$purchases_stmt = $db->prepare($purchases_query);
if ($purchases_stmt) {
    $purchases_stmt->bind_param("ss", $date_from, $date_to);
    $purchases_stmt->execute();
    $purchases_test_result = $purchases_stmt->get_result();
    echo "✅ الاستعلام نجح، عدد النتائج: " . $purchases_test_result->num_rows . "<br>";
    
    if ($purchases_test_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px;'>";
        echo "<tr><th>رقم الفاتورة</th><th>التاريخ</th><th>المورد</th><th>المستخدم</th><th>المبلغ</th></tr>";
        $count = 0;
        while ($row = $purchases_test_result->fetch_assoc() && $count < 5) {
            echo "<tr>";
            echo "<td>" . $row['invoice_number'] . "</td>";
            echo "<td>" . $row['date'] . "</td>";
            echo "<td>" . ($row['supplier_name'] ?: 'غير محدد') . "</td>";
            echo "<td>" . ($row['user_name'] ?: 'غير محدد') . "</td>";
            echo "<td>" . $row['total_amount'] . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
    }
    $purchases_stmt->close();
} else {
    echo "❌ فشل في تحضير استعلام المشتريات: " . $db->error . "<br>";
}

// اختبار استعلام إجمالي المشتريات
echo "<h4>د. استعلام إجمالي المشتريات:</h4>";
$purchases_total_query = "SELECT SUM(total_amount) as total FROM purchases WHERE date BETWEEN ? AND ?";
$purchases_total_stmt = $db->prepare($purchases_total_query);
if ($purchases_total_stmt) {
    $purchases_total_stmt->bind_param("ss", $date_from, $date_to);
    $purchases_total_stmt->execute();
    $purchases_total_result = $purchases_total_stmt->get_result();
    $total_purchases = $purchases_total_result->fetch_assoc()['total'] ?? 0;
    echo "✅ إجمالي المشتريات: " . number_format($total_purchases, 2) . "<br>";
    $purchases_total_stmt->close();
} else {
    echo "❌ فشل في استعلام إجمالي المشتريات: " . $db->error . "<br>";
}

// 6. فحص التواريخ المستخدمة في التقارير
echo "<h3>6. فحص نطاق التواريخ:</h3>";
$date_range_result = $db->query("
    SELECT 
        MIN(date) as min_sales_date, 
        MAX(date) as max_sales_date,
        COUNT(*) as sales_count
    FROM sales
    UNION ALL
    SELECT 
        MIN(date) as min_purchase_date, 
        MAX(date) as max_purchase_date,
        COUNT(*) as purchases_count
    FROM purchases
");

if ($date_range_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px;'>";
    echo "<tr><th>النوع</th><th>أقدم تاريخ</th><th>أحدث تاريخ</th><th>العدد</th></tr>";
    $types = ['المبيعات', 'المشتريات'];
    $i = 0;
    while ($row = $date_range_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $types[$i] . "</td>";
        echo "<td>" . ($row['min_sales_date'] ?: 'لا يوجد') . "</td>";
        echo "<td>" . ($row['max_sales_date'] ?: 'لا يوجد') . "</td>";
        echo "<td>" . $row['sales_count'] . "</td>";
        echo "</tr>";
        $i++;
    }
    echo "</table>";
}

echo "<br><a href='admin_financial.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار التقارير المالية</a>";

$db->close();
?>
