<?php
/**
 * صفحة تشخيص مشاكل تسجيل الدخول
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config/simple_db_config.php';

echo '<h1>🔍 تشخيص مشاكل تسجيل الدخول</h1>';

// 1. اختبار قاعدة البيانات
echo '<h2>1. اختبار قاعدة البيانات</h2>';
$db = getSimpleDB();
if ($db) {
    echo '<p style="color: green;">✅ الاتصال بقاعدة البيانات: نجح</p>';
    
    // اختبار الجداول
    $tables = ['users', 'admins', 'supervisors'];
    foreach ($tables as $table) {
        $result = $db->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p>📊 جدول $table: $count صف</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في جدول $table: " . $db->error . "</p>";
        }
    }
} else {
    echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
    exit;
}

// 2. اختبار المستخدمين
echo '<h2>2. اختبار المستخدمين</h2>';

// اختبار المشرف
echo '<h3>المشرف التجريبي:</h3>';
$supervisor_result = $db->query("SELECT id, username, full_name, email, status FROM supervisors WHERE username = 'supervisor'");
if ($supervisor_result && $supervisor_result->num_rows > 0) {
    $supervisor = $supervisor_result->fetch_assoc();
    echo '<p style="color: green;">✅ المشرف موجود</p>';
    echo '<ul>';
    foreach ($supervisor as $key => $value) {
        echo '<li>' . htmlspecialchars($key) . ': ' . htmlspecialchars($value) . '</li>';
    }
    echo '</ul>';
    
    // اختبار كلمة المرور
    $password_result = $db->query("SELECT password FROM supervisors WHERE username = 'supervisor'");
    if ($password_result) {
        $password_row = $password_result->fetch_assoc();
        if (password_verify('supervisor123', $password_row['password'])) {
            echo '<p style="color: green;">✅ كلمة المرور صحيحة</p>';
        } else {
            echo '<p style="color: red;">❌ كلمة المرور غير صحيحة</p>';
        }
    }
} else {
    echo '<p style="color: red;">❌ المشرف غير موجود</p>';
}

// اختبار المدير
echo '<h3>المدير الرئيسي:</h3>';
$admin_result = $db->query("SELECT id, username, full_name, email, role, status FROM admins WHERE username = 'admin'");
if ($admin_result && $admin_result->num_rows > 0) {
    $admin = $admin_result->fetch_assoc();
    echo '<p style="color: green;">✅ المدير موجود</p>';
    echo '<ul>';
    foreach ($admin as $key => $value) {
        echo '<li>' . htmlspecialchars($key) . ': ' . htmlspecialchars($value) . '</li>';
    }
    echo '</ul>';
    
    // اختبار كلمة المرور
    $password_result = $db->query("SELECT password FROM admins WHERE username = 'admin'");
    if ($password_result) {
        $password_row = $password_result->fetch_assoc();
        if (password_verify('admin123', $password_row['password'])) {
            echo '<p style="color: green;">✅ كلمة المرور صحيحة</p>';
        } else {
            echo '<p style="color: red;">❌ كلمة المرور غير صحيحة</p>';
        }
    }
} else {
    echo '<p style="color: red;">❌ المدير غير موجود</p>';
}

// 3. اختبار الملفات
echo '<h2>3. اختبار الملفات</h2>';
$files = [
    'work_management_login.php' => 'صفحة تسجيل الدخول',
    'work_login_handler.php' => 'معالج تسجيل الدخول الأصلي',
    'work_login_handler_fixed.php' => 'معالج تسجيل الدخول المحدث',
    'supervisor_dashboard.php' => 'لوحة تحكم المشرفين',
    'admin_dashboard.php' => 'لوحة تحكم الإدارة',
    'index.php' => 'نظام المبيعات'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description ($file): موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file): غير موجود</p>";
    }
}

// 4. اختبار تسجيل الدخول المباشر
echo '<h2>4. اختبار تسجيل الدخول المباشر</h2>';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $test_username = $_POST['test_username'] ?? '';
    $test_password = $_POST['test_password'] ?? '';
    $test_department = $_POST['test_department'] ?? '';
    
    echo '<h3>نتيجة الاختبار:</h3>';
    echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">';
    
    // محاكاة معالج تسجيل الدخول
    if (empty($test_username) || empty($test_password) || empty($test_department)) {
        echo '<p style="color: red;">❌ جميع الحقول مطلوبة</p>';
    } else {
        // تحديد الجدول
        $table = '';
        switch ($test_department) {
            case 'employees': $table = 'users'; break;
            case 'supervisors': $table = 'supervisors'; break;
            case 'management': $table = 'admins'; break;
            default: 
                echo '<p style="color: red;">❌ قسم غير صحيح</p>';
                $table = null;
        }
        
        if ($table) {
            $query = "SELECT * FROM $table WHERE username = ? AND status = 'active'";
            $stmt = $db->prepare($query);
            $stmt->bind_param('s', $test_username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();
                if (password_verify($test_password, $user['password'])) {
                    echo '<p style="color: green;">✅ تسجيل الدخول نجح!</p>';
                    echo '<p>المستخدم: ' . htmlspecialchars($user['username']) . '</p>';
                    echo '<p>الاسم: ' . htmlspecialchars($user['full_name'] ?? $user['name'] ?? 'غير محدد') . '</p>';
                } else {
                    echo '<p style="color: red;">❌ كلمة المرور غير صحيحة</p>';
                }
            } else {
                echo '<p style="color: red;">❌ المستخدم غير موجود أو غير نشط</p>';
            }
        }
    }
    echo '</div>';
}

// نموذج الاختبار
echo '<h3>نموذج اختبار تسجيل الدخول:</h3>';
echo '<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 8px; max-width: 400px;">';
echo '<div style="margin-bottom: 15px;">';
echo '<label>اسم المستخدم:</label><br>';
echo '<input type="text" name="test_username" value="supervisor" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
echo '</div>';
echo '<div style="margin-bottom: 15px;">';
echo '<label>كلمة المرور:</label><br>';
echo '<input type="password" name="test_password" value="supervisor123" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
echo '</div>';
echo '<div style="margin-bottom: 15px;">';
echo '<label>القسم:</label><br>';
echo '<select name="test_department" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
echo '<option value="">اختر القسم</option>';
echo '<option value="employees">الموظفين</option>';
echo '<option value="supervisors" selected>المشرفين</option>';
echo '<option value="management">الإدارة</option>';
echo '</select>';
echo '</div>';
echo '<button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">اختبار تسجيل الدخول</button>';
echo '</form>';

// 5. روابط الاختبار
echo '<h2>5. روابط الاختبار</h2>';
echo '<div style="display: flex; gap: 10px; flex-wrap: wrap;">';
echo '<a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">صفحة تسجيل الدخول</a>';
echo '<a href="simple_login_test.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">اختبار بسيط</a>';
echo '<a href="test_login.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;">اختبار مفصل</a>';
echo '<a href="final_test.php" style="background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">الاختبار النهائي</a>';
echo '</div>';

// 6. معلومات النظام
echo '<h2>6. معلومات النظام</h2>';
echo '<ul>';
echo '<li>إصدار PHP: ' . phpversion() . '</li>';
echo '<li>إصدار MySQL: ' . $db->server_info . '</li>';
echo '<li>مجلد العمل: ' . getcwd() . '</li>';
echo '<li>الوقت الحالي: ' . date('Y-m-d H:i:s') . '</li>';
echo '</ul>';

echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-top: 20px;">';
echo '<h3>✅ ملخص التشخيص</h3>';
echo '<p>إذا كانت جميع الاختبارات أعلاه تظهر ✅، فإن المشكلة قد تكون في:</p>';
echo '<ul>';
echo '<li>JavaScript في المتصفح</li>';
echo '<li>إعدادات CORS أو الأمان</li>';
echo '<li>مشاكل في الشبكة</li>';
echo '<li>إعدادات الخادم</li>';
echo '</ul>';
echo '<p><strong>الحل المقترح:</strong> استخدم أدوات المطور في المتصفح (F12) لمراقبة طلبات الشبكة والأخطاء في وحدة التحكم.</p>';
echo '</div>';
?>
