<?php
/**
 * تشخيص مشكلة التوجيه
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once 'config/simple_db_config.php';

echo '<h1>🔍 تشخيص مشكلة التوجيه</h1>';

// اختبار معالج تسجيل الدخول مباشرة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $department = trim($_POST['department'] ?? '');
    
    echo '<h2>اختبار معالج تسجيل الدخول:</h2>';
    echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">';
    
    // محاكاة المعالج
    $table = '';
    $redirect_page = '';
    $session_prefix = '';

    switch ($department) {
        case 'employees':
            $table = 'users';
            $redirect_page = 'index.php';
            $session_prefix = 'user_';
            break;
        case 'supervisors':
            $table = 'supervisors';
            $redirect_page = 'supervisor_dashboard.php';
            $session_prefix = 'supervisor_';
            break;
        case 'management':
            $table = 'admins';
            $redirect_page = 'admin_dashboard.php';
            $session_prefix = 'admin_';
            break;
        default:
            $redirect_page = 'ERROR: قسم غير صحيح';
    }
    
    echo '<h3>نتائج التحليل:</h3>';
    echo '<ul>';
    echo '<li><strong>اسم المستخدم:</strong> ' . htmlspecialchars($username) . '</li>';
    echo '<li><strong>القسم:</strong> ' . htmlspecialchars($department) . '</li>';
    echo '<li><strong>الجدول:</strong> ' . htmlspecialchars($table) . '</li>';
    echo '<li><strong>صفحة التوجه:</strong> ' . htmlspecialchars($redirect_page) . '</li>';
    echo '<li><strong>بادئة الجلسة:</strong> ' . htmlspecialchars($session_prefix) . '</li>';
    echo '</ul>';
    
    // اختبار قاعدة البيانات
    $db = getSimpleDB();
    if ($db && $table) {
        $query = "SELECT * FROM $table WHERE username = ? AND status = 'active'";
        $stmt = $db->prepare($query);
        $stmt->bind_param('s', $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();
            if (password_verify($password, $user['password'])) {
                echo '<p style="color: green;">✅ تسجيل الدخول صحيح</p>';
                
                // محاكاة الاستجابة
                $response = [
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'data' => [
                        'redirect' => $redirect_page,
                        'department' => $department,
                        'username' => $username,
                        'user_id' => $user['id']
                    ]
                ];
                
                echo '<h3>استجابة JSON المتوقعة:</h3>';
                echo '<pre style="background: #e9ecef; padding: 10px; border-radius: 4px;">';
                echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                echo '</pre>';
                
            } else {
                echo '<p style="color: red;">❌ كلمة المرور غير صحيحة</p>';
            }
        } else {
            echo '<p style="color: red;">❌ المستخدم غير موجود</p>';
        }
    } else {
        echo '<p style="color: red;">❌ خطأ في قاعدة البيانات أو الجدول</p>';
    }
    
    echo '</div>';
}

// اختبار AJAX مباشر
echo '<h2>اختبار AJAX مباشر:</h2>';
?>

<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>اختبار تسجيل الدخول:</h3>
    <form method="POST">
        <div style="margin-bottom: 15px;">
            <label>اسم المستخدم:</label><br>
            <input type="text" name="username" value="supervisor" style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        <div style="margin-bottom: 15px;">
            <label>كلمة المرور:</label><br>
            <input type="password" name="password" value="supervisor123" style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        <div style="margin-bottom: 15px;">
            <label>القسم:</label><br>
            <select name="department" style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">اختر القسم</option>
                <option value="employees">الموظفين</option>
                <option value="supervisors" selected>المشرفين</option>
                <option value="management">الإدارة</option>
            </select>
        </div>
        <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">اختبار</button>
    </form>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>اختبار AJAX مع JavaScript:</h3>
    <button onclick="testAjaxLogin()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">اختبار AJAX</button>
    <div id="ajax-result" style="margin-top: 15px;"></div>
</div>

<script>
async function testAjaxLogin() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
    
    try {
        const formData = new FormData();
        formData.append('username', 'supervisor');
        formData.append('password', 'supervisor123');
        formData.append('department', 'supervisors');
        formData.append('ajax', '1');
        
        console.log('إرسال البيانات:', {
            username: 'supervisor',
            password: '***',
            department: 'supervisors',
            ajax: '1'
        });
        
        const response = await fetch('work_login_handler_simple.php', {
            method: 'POST',
            body: formData
        });
        
        console.log('استجابة الخادم:', response);
        console.log('حالة الاستجابة:', response.status);
        console.log('نوع المحتوى:', response.headers.get('content-type'));
        
        const responseText = await response.text();
        console.log('نص الاستجابة:', responseText);
        
        let result;
        try {
            result = JSON.parse(responseText);
            console.log('JSON المحول:', result);
        } catch (jsonError) {
            console.error('خطأ في تحويل JSON:', jsonError);
            resultDiv.innerHTML = '<p style="color: red;">خطأ في تحويل JSON: ' + jsonError.message + '</p><pre>' + responseText + '</pre>';
            return;
        }
        
        if (result.success) {
            resultDiv.innerHTML = `
                <div style="color: green;">
                    <h4>✅ نجح تسجيل الدخول!</h4>
                    <p><strong>الرسالة:</strong> ${result.message}</p>
                    <p><strong>صفحة التوجه:</strong> ${result.data.redirect}</p>
                    <p><strong>القسم:</strong> ${result.data.department}</p>
                    <p><strong>اسم المستخدم:</strong> ${result.data.username}</p>
                    <p><a href="${result.data.redirect}" style="background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;">الانتقال للصفحة</a></p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style="color: red;">
                    <h4>❌ فشل تسجيل الدخول</h4>
                    <p><strong>الرسالة:</strong> ${result.message}</p>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('خطأ في الطلب:', error);
        resultDiv.innerHTML = '<p style="color: red;">خطأ في الطلب: ' + error.message + '</p>';
    }
}
</script>

<?php
echo '<h2>معلومات إضافية:</h2>';
echo '<ul>';
echo '<li>الملف الحالي: ' . __FILE__ . '</li>';
echo '<li>مجلد العمل: ' . getcwd() . '</li>';
echo '<li>معالج تسجيل الدخول: work_login_handler_simple.php</li>';
echo '<li>وجود المعالج: ' . (file_exists('work_login_handler_simple.php') ? 'موجود' : 'غير موجود') . '</li>';
echo '</ul>';

echo '<h2>روابط مفيدة:</h2>';
echo '<div style="display: flex; gap: 10px; flex-wrap: wrap;">';
echo '<a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">صفحة تسجيل الدخول</a>';
echo '<a href="test_sessions.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">اختبار الجلسات</a>';
echo '<a href="simple_login_test.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;">اختبار بسيط</a>';
echo '</div>';
?>
