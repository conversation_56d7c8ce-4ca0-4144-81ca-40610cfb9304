<?php
/**
 * صفحة تشخيص إعدادات النظام
 */

require_once __DIR__ . '/config/simple_db_config.php';

// التحقق من اتصال قاعدة البيانات
$db = getSimpleDB();
if (!$db) {
    die('خطأ في الاتصال بقاعدة البيانات');
}

echo "<h2>تشخيص إعدادات النظام</h2>";

// 1. فحص وجود جدول system_settings
echo "<h3>1. فحص جدول system_settings:</h3>";
$table_check = $db->query("SHOW TABLES LIKE 'system_settings'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ جدول system_settings موجود</p>";
    
    // عرض بنية الجدول
    echo "<h4>بنية الجدول:</h4>";
    $structure = $db->query("DESCRIBE system_settings");
    if ($structure) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'>❌ جدول system_settings غير موجود</p>";
    
    // إنشاء الجدول
    echo "<p>محاولة إنشاء الجدول...</p>";
    $create_table = "CREATE TABLE `system_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL UNIQUE,
        `setting_value` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($db->query($create_table)) {
        echo "<p style='color: green;'>✅ تم إنشاء جدول system_settings بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء الجدول: " . $db->error . "</p>";
    }
}

// 2. فحص الإعدادات الموجودة
echo "<h3>2. الإعدادات الموجودة:</h3>";
$settings_result = $db->query("SELECT * FROM system_settings ORDER BY setting_key");
if ($settings_result && $settings_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>المفتاح</th><th>القيمة</th><th>تاريخ الإنشاء</th><th>تاريخ التحديث</th></tr>";
    while ($row = $settings_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['setting_key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['setting_value']) . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "<td>" . $row['updated_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ لا توجد إعدادات محفوظة</p>";
}

// 3. اختبار إضافة إعداد تجريبي
echo "<h3>3. اختبار إضافة إعداد:</h3>";
$test_key = 'test_setting_' . time();
$test_value = 'test_value_' . time();

$stmt = $db->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)");
if ($stmt) {
    $stmt->bind_param("ss", $test_key, $test_value);
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ تم إضافة إعداد تجريبي بنجاح: $test_key = $test_value</p>";
        
        // حذف الإعداد التجريبي
        $delete_stmt = $db->prepare("DELETE FROM system_settings WHERE setting_key = ?");
        $delete_stmt->bind_param("s", $test_key);
        if ($delete_stmt->execute()) {
            echo "<p style='color: green;'>✅ تم حذف الإعداد التجريبي</p>";
        }
        $delete_stmt->close();
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة الإعداد التجريبي: " . $stmt->error . "</p>";
    }
    $stmt->close();
} else {
    echo "<p style='color: red;'>❌ فشل في إعداد الاستعلام: " . $db->error . "</p>";
}

// 4. اختبار دالة getSetting
echo "<h3>4. اختبار دالة getSetting:</h3>";

function testGetSetting($key, $default = '') {
    global $db;
    
    $stmt = $db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    if (!$stmt) {
        return "خطأ في إعداد الاستعلام: " . $db->error;
    }
    
    $stmt->bind_param("s", $key);
    if (!$stmt->execute()) {
        $stmt->close();
        return "خطأ في تنفيذ الاستعلام: " . $stmt->error;
    }
    
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $stmt->close();
    
    return $row ? $row['setting_value'] : $default;
}

// اختبار جلب إعداد موجود
$company_name = testGetSetting('company_name', 'القيمة الافتراضية');
echo "<p>company_name = " . htmlspecialchars($company_name) . "</p>";

// اختبار جلب إعداد غير موجود
$non_existent = testGetSetting('non_existent_setting', 'افتراضي');
echo "<p>non_existent_setting = " . htmlspecialchars($non_existent) . "</p>";

// 5. معلومات قاعدة البيانات
echo "<h3>5. معلومات قاعدة البيانات:</h3>";
echo "<p>اسم قاعدة البيانات: " . $db->query("SELECT DATABASE()")->fetch_row()[0] . "</p>";
echo "<p>إصدار MySQL: " . $db->server_info . "</p>";
echo "<p>ترميز الاتصال: " . $db->character_set_name() . "</p>";

// 6. فحص صلاحيات المستخدم
echo "<h3>6. صلاحيات المستخدم:</h3>";
$grants = $db->query("SHOW GRANTS");
if ($grants) {
    while ($row = $grants->fetch_row()) {
        echo "<p>" . htmlspecialchars($row[0]) . "</p>";
    }
}

// 7. نموذج لإضافة إعداد يدوياً
echo "<h3>7. إضافة إعداد يدوياً:</h3>";

if ($_POST['add_setting'] ?? false) {
    $key = trim($_POST['setting_key'] ?? '');
    $value = trim($_POST['setting_value'] ?? '');
    
    if ($key && $value) {
        $stmt = $db->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        if ($stmt) {
            $stmt->bind_param("ss", $key, $value);
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم حفظ الإعداد: $key = $value</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في حفظ الإعداد: " . $stmt->error . "</p>";
            }
            $stmt->close();
        }
    }
}
?>

<form method="POST">
    <table>
        <tr>
            <td>مفتاح الإعداد:</td>
            <td><input type="text" name="setting_key" placeholder="company_name" required></td>
        </tr>
        <tr>
            <td>قيمة الإعداد:</td>
            <td><input type="text" name="setting_value" placeholder="اسم الشركة" required></td>
        </tr>
        <tr>
            <td colspan="2">
                <button type="submit" name="add_setting" value="1">إضافة/تحديث الإعداد</button>
            </td>
        </tr>
    </table>
</form>

<hr>
<p><a href="admin_system.php">العودة لصفحة الإعدادات</a></p>
