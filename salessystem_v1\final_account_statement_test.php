<?php
/**
 * اختبار نهائي لكشف الحساب بعد الإصلاحات
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>🧪 اختبار نهائي لكشف الحساب</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f8f9fa; }
    .comparison { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
</style>";

try {
    $db = getCurrentUserDB();
    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }
    
    $username = $_SESSION['username'] ?? 'testuser';
    $user_id = $_SESSION['user_id'] ?? 1;
    
    $sales_table = getUserTableName('sales', $username);
    $purchases_table = getUserTableName('purchases', $username);
    $customers_table = getUserTableName('customers', $username);
    
    echo "<div class='section'>";
    echo "<h2>1. معلومات النظام</h2>";
    echo "<p class='info'>المستخدم: $username (ID: $user_id)</p>";
    echo "<p class='info'>جدول المبيعات: $sales_table</p>";
    echo "<p class='info'>جدول المشتريات: $purchases_table</p>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2. مقارنة الفترات الزمنية</h2>";
    
    // الفترة القديمة (الشهر الحالي)
    $old_start_date = date('Y-m-01');
    $old_end_date = date('Y-m-d');
    
    // الفترة الجديدة (السنة الحالية)
    $new_start_date = date('Y-01-01');
    $new_end_date = date('Y-m-d');
    
    echo "<div class='comparison'>";
    echo "<h3>مقارنة النتائج:</h3>";
    
    // اختبار الفترة القديمة
    $old_sales_query = "SELECT COUNT(*) as count FROM `$sales_table` WHERE date BETWEEN '$old_start_date' AND '$old_end_date' AND user_id = $user_id";
    $old_purchases_query = "SELECT COUNT(*) as count FROM `$purchases_table` WHERE date BETWEEN '$old_start_date' AND '$old_end_date' AND user_id = $user_id";
    
    $old_sales_count = $db->query($old_sales_query)->fetch_assoc()['count'];
    $old_purchases_count = $db->query($old_purchases_query)->fetch_assoc()['count'];
    
    // اختبار الفترة الجديدة
    $new_sales_query = "SELECT COUNT(*) as count FROM `$sales_table` WHERE date BETWEEN '$new_start_date' AND '$new_end_date' AND user_id = $user_id";
    $new_purchases_query = "SELECT COUNT(*) as count FROM `$purchases_table` WHERE date BETWEEN '$new_start_date' AND '$new_end_date' AND user_id = $user_id";
    
    $new_sales_count = $db->query($new_sales_query)->fetch_assoc()['count'];
    $new_purchases_count = $db->query($new_purchases_query)->fetch_assoc()['count'];
    
    echo "<table>";
    echo "<tr><th>الفترة</th><th>المبيعات</th><th>المشتريات</th><th>الإجمالي</th></tr>";
    echo "<tr>";
    echo "<td>الشهر الحالي ($old_start_date إلى $old_end_date)</td>";
    echo "<td>$old_sales_count</td>";
    echo "<td>$old_purchases_count</td>";
    echo "<td>" . ($old_sales_count + $old_purchases_count) . "</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>السنة الحالية ($new_start_date إلى $new_end_date)</td>";
    echo "<td>$new_sales_count</td>";
    echo "<td>$new_purchases_count</td>";
    echo "<td>" . ($new_sales_count + $new_purchases_count) . "</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($new_sales_count > $old_sales_count || $new_purchases_count > $old_purchases_count) {
        echo "<p class='success'>✅ الإصلاح نجح! الفترة الجديدة تُظهر بيانات أكثر</p>";
    } else {
        echo "<p class='warning'>⚠️ لا يوجد فرق في النتائج - قد تكون جميع البيانات في الشهر الحالي</p>";
    }
    
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3. اختبار كشف الحساب الفعلي</h2>";
    
    // محاكاة كود كشف الحساب المحسن
    $start_date = $new_start_date;
    $end_date = $new_end_date;
    $account_transactions = [];
    
    echo "<p class='info'>اختبار مع الفترة: $start_date إلى $end_date</p>";
    
    // اختبار المبيعات
    $sales_query = "SELECT
                    s.id,
                    s.invoice_number,
                    s.date,
                    s.total_amount,
                    COALESCE(s.subtotal, s.total_amount) as subtotal,
                    COALESCE(s.tax_amount, 0) as tax_amount,
                    s.payment_status,
                    COALESCE(c.name, 'عميل غير محدد') as customer_name,
                    'sale' as transaction_type,
                    'مبيعات' as transaction_type_ar,
                    'sales' as source_table,
                    'SALE' as type_verification
                    FROM `$sales_table` s
                    LEFT JOIN `$customers_table` c ON s.customer_id = c.id
                    WHERE s.date BETWEEN '$start_date' AND '$end_date' AND s.user_id = $user_id
                    ORDER BY s.date ASC, s.id ASC";
    
    $sales_result = $db->query($sales_query);
    $sales_found = 0;
    if ($sales_result) {
        while ($row = $sales_result->fetch_assoc()) {
            $row['transaction_type'] = 'sale';
            $row['transaction_type_ar'] = 'مبيعات';
            $row['source_table'] = 'sales';
            $row['type_verification'] = 'SALE';
            $row['is_sale'] = true;
            $row['is_purchase'] = false;
            $account_transactions[] = $row;
            $sales_found++;
        }
    }
    
    // اختبار المشتريات
    $purchases_query = "SELECT
                       p.id,
                       p.invoice_number,
                       p.date,
                       p.total_amount,
                       COALESCE(p.subtotal, p.total_amount) as subtotal,
                       COALESCE(p.tax_amount, 0) as tax_amount,
                       p.payment_status,
                       COALESCE(c.name, p.supplier_name, 'مورد غير محدد') as customer_name,
                       'purchase' as transaction_type,
                       'مشتريات' as transaction_type_ar,
                       'purchases' as source_table,
                       'PURCHASE' as type_verification
                       FROM `$purchases_table` p
                       LEFT JOIN `$customers_table` c ON p.customer_id = c.id
                       WHERE p.date BETWEEN '$start_date' AND '$end_date' AND p.user_id = $user_id
                       ORDER BY p.date ASC, p.id ASC";
    
    $purchases_result = $db->query($purchases_query);
    $purchases_found = 0;
    if ($purchases_result) {
        while ($row = $purchases_result->fetch_assoc()) {
            $row['transaction_type'] = 'purchase';
            $row['transaction_type_ar'] = 'مشتريات';
            $row['source_table'] = 'purchases';
            $row['type_verification'] = 'PURCHASE';
            $row['is_sale'] = false;
            $row['is_purchase'] = true;
            $account_transactions[] = $row;
            $purchases_found++;
        }
    }
    
    echo "<p class='success'>✅ نتائج الاختبار:</p>";
    echo "<ul>";
    echo "<li><strong>المبيعات المجلبة:</strong> $sales_found</li>";
    echo "<li><strong>المشتريات المجلبة:</strong> $purchases_found</li>";
    echo "<li><strong>إجمالي المعاملات:</strong> " . count($account_transactions) . "</li>";
    echo "</ul>";
    
    // التحقق من التطابق
    if ($sales_found == $new_sales_count && $purchases_found == $new_purchases_count) {
        echo "<p class='success'>✅ ممتاز! النتائج متطابقة مع قاعدة البيانات</p>";
    } else {
        echo "<p class='error'>❌ عدم تطابق في النتائج!</p>";
        echo "<p class='error'>متوقع: $new_sales_count مبيعة، $new_purchases_count مشترية</p>";
        echo "<p class='error'>فعلي: $sales_found مبيعة، $purchases_found مشترية</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4. اختبار معالجة البيانات</h2>";
    
    if (!empty($account_transactions)) {
        // ترتيب المعاملات
        usort($account_transactions, function($a, $b) {
            $date_compare = strtotime($a['date']) - strtotime($b['date']);
            if ($date_compare == 0) {
                return $a['id'] - $b['id'];
            }
            return $date_compare;
        });
        
        // حساب الرصيد التراكمي
        $running_balance = 0;
        $sales_total = 0;
        $purchases_total = 0;
        $verification_errors = 0;
        
        foreach ($account_transactions as &$transaction) {
            $is_sale = (
                $transaction['transaction_type'] === 'sale' ||
                $transaction['type_verification'] === 'SALE' ||
                $transaction['source_table'] === 'sales' ||
                isset($transaction['is_sale']) && $transaction['is_sale'] === true
            );
            
            $is_purchase = (
                $transaction['transaction_type'] === 'purchase' ||
                $transaction['type_verification'] === 'PURCHASE' ||
                $transaction['source_table'] === 'purchases' ||
                isset($transaction['is_purchase']) && $transaction['is_purchase'] === true
            );

            if ($is_sale && $is_purchase) {
                $verification_errors++;
            }
            
            if (!$is_sale && !$is_purchase) {
                $verification_errors++;
            }

            if ($is_sale && !$is_purchase) {
                $running_balance += floatval($transaction['total_amount']);
                $sales_total += floatval($transaction['total_amount']);
                $transaction['balance_change'] = '+' . number_format($transaction['total_amount'], 2);
            } elseif ($is_purchase && !$is_sale) {
                $running_balance -= floatval($transaction['total_amount']);
                $purchases_total += floatval($transaction['total_amount']);
                $transaction['balance_change'] = '-' . number_format($transaction['total_amount'], 2);
            }

            $transaction['running_balance'] = $running_balance;
            $transaction['verified_is_sale'] = $is_sale && !$is_purchase;
            $transaction['verified_is_purchase'] = $is_purchase && !$is_sale;
        }
        
        echo "<p class='success'>✅ تم معالجة " . count($account_transactions) . " معاملة</p>";
        echo "<ul>";
        echo "<li><strong>إجمالي المبيعات:</strong> " . number_format($sales_total, 2) . " ر.س</li>";
        echo "<li><strong>إجمالي المشتريات:</strong> " . number_format($purchases_total, 2) . " ر.س</li>";
        echo "<li><strong>صافي الرصيد:</strong> " . number_format($running_balance, 2) . " ر.س</li>";
        echo "<li><strong>أخطاء التحقق:</strong> $verification_errors</li>";
        echo "</ul>";
        
        if ($verification_errors == 0) {
            echo "<p class='success'>✅ لا توجد أخطاء في التحقق - النظام يعمل بشكل مثالي!</p>";
        } else {
            echo "<p class='error'>❌ توجد $verification_errors أخطاء في التحقق</p>";
        }
        
    } else {
        echo "<p class='warning'>⚠️ لا توجد معاملات للاختبار</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5. النتيجة النهائية</h2>";
    
    $expected_sales = 3;
    $expected_purchases = 1;
    
    echo "<div class='comparison'>";
    echo "<h3>مقارنة مع التوقعات:</h3>";
    echo "<table>";
    echo "<tr><th>النوع</th><th>المتوقع</th><th>الفعلي</th><th>الحالة</th></tr>";
    
    $sales_status = ($sales_found == $expected_sales) ? '✅ صحيح' : '❌ خطأ';
    $purchases_status = ($purchases_found == $expected_purchases) ? '✅ صحيح' : '❌ خطأ';
    
    echo "<tr>";
    echo "<td>المبيعات</td>";
    echo "<td>$expected_sales</td>";
    echo "<td>$sales_found</td>";
    echo "<td>$sales_status</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>المشتريات</td>";
    echo "<td>$expected_purchases</td>";
    echo "<td>$purchases_found</td>";
    echo "<td>$purchases_status</td>";
    echo "</tr>";
    
    echo "</table>";
    
    if ($sales_found == $expected_sales && $purchases_found == $expected_purchases) {
        echo "<h3 class='success'>🎉 تم إصلاح المشكلة بنجاح!</h3>";
        echo "<p class='success'>كشف الحساب يُظهر الآن العدد الصحيح من المعاملات</p>";
    } else {
        echo "<h3 class='warning'>⚠️ لا تزال هناك مشكلة</h3>";
        echo "<p class='warning'>العدد المعروض لا يطابق العدد المتوقع</p>";
        
        if ($sales_found != $expected_sales) {
            echo "<p class='error'>مشكلة في المبيعات: متوقع $expected_sales، فعلي $sales_found</p>";
        }
        
        if ($purchases_found != $expected_purchases) {
            echo "<p class='error'>مشكلة في المشتريات: متوقع $expected_purchases، فعلي $purchases_found</p>";
        }
    }
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>6. الخطوات التالية</h2>";
echo "<ul>";
echo "<li><a href='reports.php?report_type=account_statement' target='_blank'>عرض كشف الحساب النهائي</a></li>";
echo "<li><a href='debug_account_statement_data.php'>فحص البيانات مرة أخرى</a></li>";
echo "<li><a href='final_system_status.php'>تقرير حالة النظام</a></li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الاختبار النهائي في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
