<?php
/**
 * فحص نهائي لجميع صفحات المدير بعد الإصلاح
 */

// قائمة جميع صفحات المدير
$admin_pages = [
    'admin_dashboard.php' => 'لوحة تحكم المدير',
    'admin_users.php' => 'إدارة المستخدمين',
    'admin_activity.php' => 'سجل العمليات',
    'admin_reports.php' => 'التقارير الشاملة',
    'admin_financial.php' => 'التقارير المالية',
    'admin_error_logs.php' => 'سجل الأخطاء',
    'admin_system.php' => 'إعدادات النظام',
    'admin_manage_admins.php' => 'إدارة المديرين',
    'admin_user_details.php' => 'تفاصيل المستخدم',
    'admin_invoice_details.php' => 'تفاصيل الفاتورة'
];

$status_report = [];
$total_fixed = 0;
$total_working = 0;
$total_issues = 0;

echo "<h2>✅ فحص نهائي لجميع صفحات المدير بعد الإصلاح</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>🎯 الهدف من الفحص:</h4>";
echo "<ul>";
echo "<li>✅ التأكد من عدم وجود أخطاء PHP</li>";
echo "<li>✅ التأكد من صحة تنسيق الكود</li>";
echo "<li>✅ التأكد من عمل التصميم الحديث</li>";
echo "<li>✅ التأكد من وجود جميع العناصر المطلوبة</li>";
echo "</ul>";
echo "</div>";

foreach ($admin_pages as $page => $title) {
    $file_path = __DIR__ . '/' . $page;
    $issues = [];
    $status = 'success';
    
    echo "<h3>🔍 فحص: $title</h3>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    // 1. فحص وجود الملف
    if (!file_exists($file_path)) {
        $issues[] = "❌ الملف غير موجود";
        $status = 'error';
        echo "<span style='color: red;'>❌ الملف غير موجود</span><br>";
        $status_report[$page] = ['status' => $status, 'issues' => $issues];
        echo "</div>";
        continue;
    }
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    if ($content === false) {
        $issues[] = "❌ فشل في قراءة الملف";
        $status = 'error';
        echo "<span style='color: red;'>❌ فشل في قراءة الملف</span><br>";
        $status_report[$page] = ['status' => $status, 'issues' => $issues];
        echo "</div>";
        continue;
    }
    
    // 2. فحص أخطاء PHP الأساسية
    $php_errors = [];
    
    // فحص الأقواس المتطابقة
    $open_braces = substr_count($content, '{');
    $close_braces = substr_count($content, '}');
    if ($open_braces !== $close_braces) {
        $php_errors[] = "أقواس غير متطابقة ({: $open_braces, }: $close_braces)";
    }
    
    // فحص الأقواس المربعة
    $open_brackets = substr_count($content, '[');
    $close_brackets = substr_count($content, ']');
    if ($open_brackets !== $close_brackets) {
        $php_errors[] = "أقواس مربعة غير متطابقة ([: $open_brackets, ]: $close_brackets)";
    }
    
    // فحص الأقواس العادية
    $open_parens = substr_count($content, '(');
    $close_parens = substr_count($content, ')');
    if ($open_parens !== $close_parens) {
        $php_errors[] = "أقواس عادية غير متطابقة ((: $open_parens, ): $close_parens)";
    }
    
    if (!empty($php_errors)) {
        foreach ($php_errors as $error) {
            $issues[] = "⚠️ $error";
            echo "<span style='color: orange;'>⚠️ $error</span><br>";
        }
        if ($status !== 'error') $status = 'warning';
    } else {
        echo "<span style='color: green;'>✅ لا توجد أخطاء PHP أساسية</span><br>";
    }
    
    // 3. فحص تنسيق الكود
    $line_count = substr_count($content, "\n");
    if ($line_count < 10) {
        $issues[] = "⚠️ الملف مضغوط ($line_count أسطر)";
        if ($status !== 'error') $status = 'warning';
        echo "<span style='color: orange;'>⚠️ الملف مضغوط ($line_count أسطر)</span><br>";
    } else {
        echo "<span style='color: green;'>✅ تنسيق الكود جيد ($line_count أسطر)</span><br>";
        $total_fixed++;
    }
    
    // 4. فحص العناصر الأساسية
    $required_elements = [
        'admin_header_new.php' => 'Header الجديد',
        'admin-layout' => 'تخطيط الصفحة',
        'modern-sidebar' => 'الشريط الجانبي',
        'admin-content' => 'منطقة المحتوى',
        'gradient-text' => 'نص متدرج'
    ];
    
    $found_elements = 0;
    foreach ($required_elements as $element => $description) {
        if (strpos($content, $element) !== false) {
            echo "<span style='color: green;'>✅ $description موجود</span><br>";
            $found_elements++;
        } else {
            echo "<span style='color: orange;'>⚠️ $description مفقود</span><br>";
            $issues[] = "⚠️ $description مفقود";
            if ($status !== 'error') $status = 'warning';
        }
    }
    
    // 5. فحص CSS Classes الحديثة
    $modern_classes = [
        'modern-card' => 'بطاقات حديثة',
        'modern-btn' => 'أزرار حديثة',
        'stats-card' => 'بطاقات إحصائيات'
    ];
    
    $found_classes = 0;
    foreach ($modern_classes as $class => $description) {
        if (strpos($content, $class) !== false) {
            echo "<span style='color: green;'>✅ $description موجودة</span><br>";
            $found_classes++;
        }
    }
    
    if ($found_classes === 0) {
        echo "<span style='color: orange;'>⚠️ لا توجد CSS classes حديثة</span><br>";
        $issues[] = "⚠️ لا توجد CSS classes حديثة";
        if ($status !== 'error') $status = 'warning';
    }
    
    // 6. تحديد الحالة النهائية
    if (empty($issues)) {
        echo "<span style='color: green; font-weight: bold;'>🎉 الصفحة تعمل بشكل مثالي!</span><br>";
        $total_working++;
    } else {
        $total_issues += count($issues);
        echo "<span style='color: " . ($status === 'error' ? 'red' : 'orange') . "; font-weight: bold;'>";
        echo ($status === 'error' ? '❌' : '⚠️') . " عدد المشاكل: " . count($issues) . "</span><br>";
    }
    
    $status_report[$page] = ['status' => $status, 'issues' => $issues];
    echo "</div>";
}

// عرض الملخص النهائي
echo "<h3>📊 الملخص النهائي للإصلاح</h3>";

$success_count = 0;
$warning_count = 0;
$error_count = 0;

foreach ($status_report as $page => $report) {
    switch ($report['status']) {
        case 'success':
            $success_count++;
            break;
        case 'warning':
            $warning_count++;
            break;
        case 'error':
            $error_count++;
            break;
    }
}

echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>";

// بطاقة النجاح
echo "<div style='flex: 1; background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #28a745; text-align: center;'>";
echo "<h4 style='color: #155724; margin: 0;'>✅ صفحات مثالية</h4>";
echo "<p style='color: #155724; margin: 10px 0 0 0; font-size: 32px; font-weight: bold;'>$success_count</p>";
echo "</div>";

// بطاقة التحذيرات
echo "<div style='flex: 1; background: #fff3cd; padding: 20px; border-radius: 10px; border-left: 5px solid #ffc107; text-align: center;'>";
echo "<h4 style='color: #856404; margin: 0;'>⚠️ تحتاج تحسين</h4>";
echo "<p style='color: #856404; margin: 10px 0 0 0; font-size: 32px; font-weight: bold;'>$warning_count</p>";
echo "</div>";

// بطاقة الأخطاء
echo "<div style='flex: 1; background: #f8d7da; padding: 20px; border-radius: 10px; border-left: 5px solid #dc3545; text-align: center;'>";
echo "<h4 style='color: #721c24; margin: 0;'>❌ تحتاج إصلاح</h4>";
echo "<p style='color: #721c24; margin: 10px 0 0 0; font-size: 32px; font-weight: bold;'>$error_count</p>";
echo "</div>";

echo "</div>";

// إحصائيات الإصلاح
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h4>📈 إحصائيات الإصلاح:</h4>";
echo "<ul style='font-size: 16px;'>";
echo "<li><strong>الصفحات المُصلحة:</strong> $total_fixed من " . count($admin_pages) . "</li>";
echo "<li><strong>الصفحات العاملة:</strong> $total_working من " . count($admin_pages) . "</li>";
echo "<li><strong>إجمالي المشاكل المتبقية:</strong> $total_issues</li>";
echo "<li><strong>معدل النجاح:</strong> " . round(($success_count / count($admin_pages)) * 100, 1) . "%</li>";
echo "</ul>";
echo "</div>";

// روابط اختبار الصفحات
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار الصفحات:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;'>";
foreach ($admin_pages as $page => $title) {
    $color = '#6c757d'; // رمادي افتراضي
    if (isset($status_report[$page])) {
        switch ($status_report[$page]['status']) {
            case 'success': $color = '#28a745'; break; // أخضر
            case 'warning': $color = '#ffc107'; break; // أصفر
            case 'error': $color = '#dc3545'; break; // أحمر
        }
    }
    echo "<a href='$page' target='_blank' style='background: $color; color: white; padding: 10px 15px; text-decoration: none; border-radius: 8px; font-size: 14px; margin: 5px;'>$title</a>";
}
echo "</div>";
echo "</div>";

// رسالة النجاح أو التحذير
if ($success_count === count($admin_pages)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; border-left: 5px solid #28a745; margin: 20px 0; text-align: center;'>";
    echo "<h3>🎉 تهانينا! جميع الصفحات تعمل بشكل مثالي!</h3>";
    echo "<p>تم إصلاح جميع مشاكل التنسيق والأخطاء بنجاح. النظام جاهز للاستخدام.</p>";
    echo "</div>";
} elseif ($error_count === 0) {
    echo "<div style='background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; border-left: 5px solid #ffc107; margin: 20px 0; text-align: center;'>";
    echo "<h3>⚠️ الإصلاح مكتمل مع بعض التحسينات المطلوبة</h3>";
    echo "<p>تم إصلاح الأخطاء الحرجة، لكن هناك بعض التحسينات التي يمكن تطبيقها.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; border-left: 5px solid #dc3545; margin: 20px 0; text-align: center;'>";
    echo "<h3>❌ هناك مشاكل تحتاج إصلاح</h3>";
    echo "<p>يرجى مراجعة الصفحات التي تحتوي على أخطاء وإصلاحها.</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🏠 العودة للوحة التحكم</a>";
echo "<a href='check_all_pages.php' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🔍 فحص تفصيلي</a>";
echo "</div>";
?>
