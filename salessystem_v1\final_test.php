<?php
/**
 * اختبار نهائي للنظام مع إنشاء المشرف التجريبي
 */
require_once 'config/simple_db_config.php';

echo '<h1>🚀 الاختبار النهائي للنظام</h1>';

// 1. اختبار قاعدة البيانات
echo '<h2>1. اختبار قاعدة البيانات</h2>';
$db = getSimpleDB();
if ($db) {
    echo '<p style="color: green;">✅ الاتصال بقاعدة البيانات: نجح</p>';
    
    // إنشاء الجداول
    if (createUnifiedTables($db)) {
        echo '<p style="color: green;">✅ إنشاء الجداول: نجح</p>';
    } else {
        echo '<p style="color: red;">❌ إنشاء الجداول: فشل</p>';
    }
    
    // إنشاء البيانات الأولية
    if (insertInitialSystemData()) {
        echo '<p style="color: green;">✅ إنشاء البيانات الأولية: نجح</p>';
    } else {
        echo '<p style="color: orange;">⚠️ البيانات الأولية: موجودة مسبقاً</p>';
    }
    
    // إنشاء المدير الرئيسي
    if (createSuperAdmin()) {
        echo '<p style="color: green;">✅ إنشاء المدير الرئيسي: نجح</p>';
    } else {
        echo '<p style="color: orange;">⚠️ المدير الرئيسي: موجود مسبقاً</p>';
    }
    
} else {
    echo '<p style="color: red;">❌ الاتصال بقاعدة البيانات: فشل</p>';
    exit;
}

// 2. التحقق من المشرف التجريبي
echo '<h2>2. التحقق من المشرف التجريبي</h2>';

// إجبار إنشاء المشرف التجريبي
echo '<h3>إجبار إنشاء المشرف التجريبي:</h3>';
if (createDefaultSupervisor($db)) {
    echo '<p style="color: green;">✅ دالة createDefaultSupervisor: نجحت</p>';
} else {
    echo '<p style="color: red;">❌ دالة createDefaultSupervisor: فشلت</p>';
}

// التحقق من وجود المشرف
$supervisor_check = $db->query("SELECT * FROM supervisors WHERE username = 'supervisor'");
if ($supervisor_check && $supervisor_check->num_rows > 0) {
    $supervisor = $supervisor_check->fetch_assoc();
    echo '<p style="color: green;">✅ المشرف التجريبي موجود</p>';
    echo '<div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;">';
    echo '<h4>📋 تفاصيل المشرف التجريبي:</h4>';
    echo '<ul>';
    echo '<li><strong>ID:</strong> ' . htmlspecialchars($supervisor['id']) . '</li>';
    echo '<li><strong>اسم المستخدم:</strong> ' . htmlspecialchars($supervisor['username']) . '</li>';
    echo '<li><strong>كلمة المرور:</strong> supervisor123</li>';
    echo '<li><strong>الاسم الكامل:</strong> ' . htmlspecialchars($supervisor['full_name']) . '</li>';
    echo '<li><strong>البريد الإلكتروني:</strong> ' . htmlspecialchars($supervisor['email']) . '</li>';
    echo '<li><strong>الهاتف:</strong> ' . htmlspecialchars($supervisor['phone']) . '</li>';
    echo '<li><strong>القسم:</strong> ' . htmlspecialchars($supervisor['department']) . '</li>';
    echo '<li><strong>الحالة:</strong> ' . htmlspecialchars($supervisor['status']) . '</li>';
    echo '<li><strong>تاريخ الإنشاء:</strong> ' . htmlspecialchars($supervisor['created_at']) . '</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<p style="color: red;">❌ المشرف التجريبي غير موجود</p>';
    
    // محاولة إنشاؤه مباشرة
    echo '<h4>محاولة إنشاء المشرف مباشرة:</h4>';
    $username = 'supervisor';
    $email = '<EMAIL>';
    $phone = '0987654321';
    $password = password_hash('supervisor123', PASSWORD_DEFAULT);
    $full_name = 'مشرف تجريبي';
    $department = 'الإشراف العام';
    $status = 'active';
    $permissions = 'supervisor_permissions';
    
    $insert_sql = "INSERT INTO supervisors (username, email, phone, password, full_name, department, permissions, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
    $stmt = $db->prepare($insert_sql);
    if ($stmt) {
        $stmt->bind_param('ssssssss', $username, $email, $phone, $password, $full_name, $department, $permissions, $status);
        if ($stmt->execute()) {
            echo '<p style="color: green;">✅ تم إنشاء المشرف التجريبي مباشرة</p>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء المشرف: ' . $stmt->error . '</p>';
        }
    }
}

// 3. اختبار تسجيل الدخول
echo '<h2>3. اختبار تسجيل الدخول</h2>';

// اختبار كلمة مرور المشرف
$test_password = 'supervisor123';
$supervisor_check = $db->query("SELECT password FROM supervisors WHERE username = 'supervisor'");
if ($supervisor_check && $supervisor_check->num_rows > 0) {
    $supervisor = $supervisor_check->fetch_assoc();
    if (password_verify($test_password, $supervisor['password'])) {
        echo '<p style="color: green;">✅ كلمة مرور المشرف صحيحة</p>';
    } else {
        echo '<p style="color: red;">❌ كلمة مرور المشرف غير صحيحة</p>';
    }
}

// 4. عرض جميع المستخدمين
echo '<h2>4. جميع المستخدمين في النظام</h2>';

// المديرين
echo '<h3>المديرين:</h3>';
$admins = $db->query("SELECT id, username, full_name, email, role, status FROM admins ORDER BY created_at DESC");
if ($admins && $admins->num_rows > 0) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
    echo '<tr style="background: #f8f9fa;"><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>';
    while ($admin = $admins->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($admin['id']) . '</td>';
        echo '<td>' . htmlspecialchars($admin['username']) . '</td>';
        echo '<td>' . htmlspecialchars($admin['full_name']) . '</td>';
        echo '<td>' . htmlspecialchars($admin['email']) . '</td>';
        echo '<td>' . htmlspecialchars($admin['role']) . '</td>';
        echo '<td>' . htmlspecialchars($admin['status']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ لا توجد مديرين</p>';
}

// المشرفين
echo '<h3>المشرفين:</h3>';
$supervisors = $db->query("SELECT id, username, full_name, email, department, status FROM supervisors ORDER BY created_at DESC");
if ($supervisors && $supervisors->num_rows > 0) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
    echo '<tr style="background: #f8f9fa;"><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>القسم</th><th>الحالة</th></tr>';
    while ($supervisor = $supervisors->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($supervisor['id']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['username']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['full_name']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['email']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['department']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['status']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ لا توجد مشرفين</p>';
}

// المستخدمين
echo '<h3>المستخدمين (الموظفين):</h3>';
$users = $db->query("SELECT COUNT(*) as count FROM users");
if ($users) {
    $user_count = $users->fetch_assoc()['count'];
    echo '<p>عدد المستخدمين: ' . $user_count . '</p>';
}

// 5. روابط الاختبار
echo '<h2>5. 🔗 روابط الاختبار</h2>';
echo '<div style="display: flex; gap: 15px; flex-wrap: wrap; margin: 20px 0;">';
echo '<a href="work_management_login.php" style="background: #007bff; color: white; padding: 15px 25px; text-decoration: none; border-radius: 8px; font-weight: bold;">🔐 صفحة تسجيل الدخول</a>';
echo '<a href="system_navigation.php" style="background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 8px; font-weight: bold;">🏠 التنقل المركزي</a>';
echo '<a href="supervisor_dashboard.php" style="background: #ffc107; color: black; padding: 15px 25px; text-decoration: none; border-radius: 8px; font-weight: bold;">👔 لوحة المشرفين</a>';
echo '<a href="admin_dashboard.php" style="background: #dc3545; color: white; padding: 15px 25px; text-decoration: none; border-radius: 8px; font-weight: bold;">👑 لوحة الإدارة</a>';
echo '</div>';

// 6. ملخص نهائي
echo '<h2>6. 📊 الملخص النهائي</h2>';
echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #007bff;">';
echo '<h3>✅ النظام جاهز للاستخدام!</h3>';
echo '<h4>بيانات تسجيل الدخول:</h4>';
echo '<ul>';
echo '<li><strong>المدير:</strong> admin / admin123</li>';
echo '<li><strong>المشرف:</strong> supervisor / supervisor123</li>';
echo '<li><strong>الموظفين:</strong> يحتاج إنشاء حسابات في نظام المبيعات</li>';
echo '</ul>';
echo '<h4>المزايا المطبقة:</h4>';
echo '<ul>';
echo '<li>✅ تسجيل دخول تفاعلي من البطاقات المنسدلة</li>';
echo '<li>✅ ثلاثة مستويات إدارية منفصلة</li>';
echo '<li>✅ نظام صلاحيات متكامل</li>';
echo '<li>✅ تأثيرات بصرية متقدمة</li>';
echo '<li>✅ تنقل سلس بين الأقسام</li>';
echo '</ul>';
echo '</div>';
?>
