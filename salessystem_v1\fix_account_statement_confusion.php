<?php
/**
 * إصلاح مشكلة الخلط بين المبيعات والمشتريات في كشف الحساب
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>🔧 إصلاح مشكلة كشف الحساب</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
</style>";

echo "<div class='section'>";
echo "<h2>1. تشخيص المشكلة</h2>";

echo "<h3>المشاكل المحتملة في كشف الحساب:</h3>";
echo "<ul>";
echo "<li><strong>خلط في نوع المعاملة:</strong> قد يتم تصنيف المبيعات كمشتريات أو العكس</li>";
echo "<li><strong>مشاكل في الاستعلامات:</strong> قد تكون هناك مشاكل في JOIN أو WHERE clauses</li>";
echo "<li><strong>مشاكل في معالجة البيانات:</strong> قد يتم تغيير نوع المعاملة أثناء المعالجة</li>";
echo "<li><strong>مشاكل في العرض:</strong> قد تكون المشكلة في طريقة عرض البيانات</li>";
echo "</ul>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. فحص البيانات الحالية</h2>";

try {
    $db = getCurrentUserDB();
    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }
    
    $username = $_SESSION['username'] ?? 'testuser';
    $user_id = $_SESSION['user_id'] ?? 1;
    
    // الحصول على أسماء الجداول
    $sales_table = getUserTableName('sales', $username);
    $purchases_table = getUserTableName('purchases', $username);
    $customers_table = getUserTableName('customers', $username);
    
    echo "<h3>أسماء الجداول:</h3>";
    echo "<ul>";
    echo "<li><strong>جدول المبيعات:</strong> $sales_table</li>";
    echo "<li><strong>جدول المشتريات:</strong> $purchases_table</li>";
    echo "<li><strong>جدول العملاء:</strong> $customers_table</li>";
    echo "</ul>";
    
    // فحص بيانات المبيعات
    $sales_check = $db->query("SELECT COUNT(*) as count FROM `$sales_table` WHERE user_id = $user_id");
    $sales_count = $sales_check ? $sales_check->fetch_assoc()['count'] : 0;
    echo "<p class='info'>عدد المبيعات: $sales_count</p>";
    
    // فحص بيانات المشتريات
    $purchases_check = $db->query("SELECT COUNT(*) as count FROM `$purchases_table` WHERE user_id = $user_id");
    $purchases_count = $purchases_check ? $purchases_check->fetch_assoc()['count'] : 0;
    echo "<p class='info'>عدد المشتريات: $purchases_count</p>";
    
    // عرض عينة من البيانات
    echo "<h3>عينة من بيانات المبيعات:</h3>";
    $sales_sample = $db->query("SELECT id, invoice_number, date, total_amount, customer_id FROM `$sales_table` WHERE user_id = $user_id ORDER BY date DESC LIMIT 3");
    if ($sales_sample && $sales_sample->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>العميل</th></tr>";
        while ($row = $sales_sample->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['invoice_number']}</td>";
            echo "<td>{$row['date']}</td>";
            echo "<td>{$row['total_amount']}</td>";
            echo "<td>{$row['customer_id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>لا توجد بيانات مبيعات</p>";
    }
    
    echo "<h3>عينة من بيانات المشتريات:</h3>";
    $purchases_sample = $db->query("SELECT id, invoice_number, date, total_amount, customer_id FROM `$purchases_table` WHERE user_id = $user_id ORDER BY date DESC LIMIT 3");
    if ($purchases_sample && $purchases_sample->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>رقم الفاتورة</th><th>التاريخ</th><th>المبلغ</th><th>العميل/المورد</th></tr>";
        while ($row = $purchases_sample->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['invoice_number']}</td>";
            echo "<td>{$row['date']}</td>";
            echo "<td>{$row['total_amount']}</td>";
            echo "<td>{$row['customer_id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>لا توجد بيانات مشتريات</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>خطأ في فحص البيانات: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. الإصلاحات المطلوبة</h2>";

echo "<h3>الإصلاحات التي سيتم تطبيقها:</h3>";
echo "<ol>";
echo "<li><strong>تحسين استعلامات قاعدة البيانات</strong> - إضافة تحديد صريح لنوع المعاملة</li>";
echo "<li><strong>تحسين معالجة البيانات</strong> - التأكد من عدم تغيير نوع المعاملة</li>";
echo "<li><strong>إضافة تحقق مزدوج</strong> - التحقق من نوع المعاملة في عدة نقاط</li>";
echo "<li><strong>تحسين العرض</strong> - إضافة مؤشرات بصرية واضحة</li>";
echo "<li><strong>إضافة تسجيل للأخطاء</strong> - لتتبع أي مشاكل مستقبلية</li>";
echo "</ol>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. تطبيق الإصلاحات</h2>";

echo "<p class='info'>سيتم الآن إنشاء نسخة محسنة من كود كشف الحساب...</p>";

// إنشاء الكود المحسن
$improved_code = '
// جلب كشف الحساب الشامل (محسن ومصحح)
if ($report_type == \'account_statement\') {
    // جلب جميع المعاملات (مبيعات ومشتريات) مرتبة حسب التاريخ
    $account_transactions = [];

    // جلب المبيعات مع تحديد صريح لنوع المعاملة
    $sales_query = "SELECT
                    s.id,
                    s.invoice_number,
                    s.date,
                    s.total_amount,
                    COALESCE(s.subtotal, s.total_amount) as subtotal,
                    COALESCE(s.tax_amount, 0) as tax_amount,
                    s.payment_status,
                    COALESCE(c.name, \'عميل غير محدد\') as customer_name,
                    \'sale\' as transaction_type,
                    \'مبيعات\' as transaction_type_ar,
                    \'sales\' as source_table,
                    \'SALE\' as type_verification
                    FROM `$sales_table` s
                    LEFT JOIN `$customers_table` c ON s.customer_id = c.id AND c.user_id = s.user_id
                    WHERE s.date BETWEEN \'$start_date\' AND \'$end_date\' AND s.user_id = {$_SESSION[\'user_id\']}";

    if ($customer_id > 0) {
        $sales_query .= " AND s.customer_id = $customer_id";
    }

    $sales_query .= " ORDER BY s.date ASC, s.id ASC";

    $sales_result = $db->query($sales_query);
    if ($sales_result) {
        while ($row = $sales_result->fetch_assoc()) {
            // التأكد المطلق من نوع المعاملة
            $row[\'transaction_type\'] = \'sale\';
            $row[\'transaction_type_ar\'] = \'مبيعات\';
            $row[\'source_table\'] = \'sales\';
            $row[\'type_verification\'] = \'SALE\';
            $row[\'is_sale\'] = true;
            $row[\'is_purchase\'] = false;
            $account_transactions[] = $row;
        }
    }

    // جلب المشتريات مع تحديد صريح لنوع المعاملة
    $check_purchases_table = $db->query("SHOW TABLES LIKE \'$purchases_table\'");
    if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
        $purchases_query = "SELECT
                           p.id,
                           p.invoice_number,
                           p.date,
                           p.total_amount,
                           COALESCE(p.subtotal, p.total_amount) as subtotal,
                           COALESCE(p.tax_amount, 0) as tax_amount,
                           p.payment_status,
                           COALESCE(c.name, p.supplier_name, \'مورد غير محدد\') as customer_name,
                           \'purchase\' as transaction_type,
                           \'مشتريات\' as transaction_type_ar,
                           \'purchases\' as source_table,
                           \'PURCHASE\' as type_verification
                           FROM `$purchases_table` p
                           LEFT JOIN `$customers_table` c ON p.customer_id = c.id AND c.user_id = p.user_id
                           WHERE p.date BETWEEN \'$start_date\' AND \'$end_date\' AND p.user_id = {$_SESSION[\'user_id\']}";

        if ($customer_id > 0) {
            $check_customer_column = $db->query("SHOW COLUMNS FROM `$purchases_table` LIKE \'customer_id\'");
            if ($check_customer_column && $check_customer_column->num_rows > 0) {
                $purchases_query .= " AND p.customer_id = $customer_id";
            }
        }

        $purchases_query .= " ORDER BY p.date ASC, p.id ASC";

        $purchases_result = $db->query($purchases_query);
        if ($purchases_result) {
            while ($row = $purchases_result->fetch_assoc()) {
                // التأكد المطلق من نوع المعاملة
                $row[\'transaction_type\'] = \'purchase\';
                $row[\'transaction_type_ar\'] = \'مشتريات\';
                $row[\'source_table\'] = \'purchases\';
                $row[\'type_verification\'] = \'PURCHASE\';
                $row[\'is_sale\'] = false;
                $row[\'is_purchase\'] = true;
                $account_transactions[] = $row;
            }
        }
    }

    // ترتيب المعاملات حسب التاريخ والوقت
    usort($account_transactions, function($a, $b) {
        $date_compare = strtotime($a[\'date\']) - strtotime($b[\'date\']);
        if ($date_compare == 0) {
            return $a[\'id\'] - $b[\'id\'];
        }
        return $date_compare;
    });

    // حساب الرصيد التراكمي مع تحقق مضاعف
    $running_balance = 0;
    foreach ($account_transactions as &$transaction) {
        // التحقق المتعدد من نوع المعاملة
        $is_sale = (
            $transaction[\'transaction_type\'] === \'sale\' ||
            $transaction[\'type_verification\'] === \'SALE\' ||
            $transaction[\'source_table\'] === \'sales\' ||
            isset($transaction[\'is_sale\']) && $transaction[\'is_sale\'] === true
        );
        
        $is_purchase = (
            $transaction[\'transaction_type\'] === \'purchase\' ||
            $transaction[\'type_verification\'] === \'PURCHASE\' ||
            $transaction[\'source_table\'] === \'purchases\' ||
            isset($transaction[\'is_purchase\']) && $transaction[\'is_purchase\'] === true
        );

        // تسجيل أي تضارب في التصنيف
        if ($is_sale && $is_purchase) {
            error_log("Transaction type conflict for ID: " . $transaction[\'id\']);
        }
        
        if (!$is_sale && !$is_purchase) {
            error_log("Unknown transaction type for ID: " . $transaction[\'id\'] . " - Type: " . $transaction[\'transaction_type\']);
        }

        // تطبيق التغيير على الرصيد
        if ($is_sale && !$is_purchase) {
            $running_balance += floatval($transaction[\'total_amount\']);
            $transaction[\'balance_change\'] = \'+\' . number_format($transaction[\'total_amount\'], 2);
        } elseif ($is_purchase && !$is_sale) {
            $running_balance -= floatval($transaction[\'total_amount\']);
            $transaction[\'balance_change\'] = \'-\' . number_format($transaction[\'total_amount\'], 2);
        }

        $transaction[\'running_balance\'] = $running_balance;
        
        // إضافة معرفات إضافية للتحقق في العرض
        $transaction[\'verified_is_sale\'] = $is_sale && !$is_purchase;
        $transaction[\'verified_is_purchase\'] = $is_purchase && !$is_sale;
    }
}
';

echo "<div class='code'>";
echo "<h4>الكود المحسن:</h4>";
echo "<pre>" . htmlspecialchars($improved_code) . "</pre>";
echo "</div>";

echo "<p class='success'>✅ تم إنشاء الكود المحسن بنجاح</p>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. الخطوات التالية</h2>";

echo "<h3>لتطبيق الإصلاح:</h3>";
echo "<ol>";
echo "<li><a href='apply_account_statement_fix.php'>تطبيق الإصلاح على ملف reports.php</a></li>";
echo "<li><a href='test_account_statement.php'>اختبار كشف الحساب المحسن</a></li>";
echo "<li><a href='reports.php?report_type=account_statement'>عرض كشف الحساب</a></li>";
echo "</ol>";

echo "<h3>للمراقبة:</h3>";
echo "<ul>";
echo "<li>مراقبة ملفات السجلات للتأكد من عدم وجود أخطاء</li>";
echo "<li>التحقق من صحة البيانات المعروضة</li>";
echo "<li>التأكد من أن المبيعات تظهر كدائن والمشتريات كمدين</li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء التشخيص في: " . date('Y-m-d H:i:s');
echo "</p>";

?>
