<?php
/**
 * إصلاح صفحات المدير - إعدادات النظام وإدارة المديرين
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>إصلاح صفحات المدير</h1>";

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    echo "<p style='color: red;'>❌ يجب تسجيل الدخول كمدير أولاً</p>";
    echo "<p><a href='admin_login.php'>تسجيل دخول المدير</a></p>";
    exit;
}

echo "<h2>1. فحص الدوال المطلوبة</h2>";

$required_functions = [
    'isAdminLoggedIn',
    'hasAdminPermission', 
    'getCurrentAdmin',
    'getSystemSettings',
    'updateSystemSetting',
    'getSystemSetting'
];

foreach ($required_functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ دالة $func موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة $func غير موجودة</p>";
    }
}

echo "<h2>2. فحص جدول إعدادات النظام</h2>";

$db = getUnifiedDB();
if (!$db) {
    echo "<p style='color: red;'>❌ فشل في الاتصال بقاعدة البيانات</p>";
    exit;
}

// التحقق من وجود جدول system_settings
$table_check = $db->query("SHOW TABLES LIKE 'system_settings'");
if ($table_check && $table_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ جدول system_settings موجود</p>";
    
    // عرض الإعدادات الحالية
    $settings = getSystemSettings();
    echo "<p>عدد الإعدادات الحالية: " . count($settings) . "</p>";
    
    if (empty($settings)) {
        echo "<p style='color: orange;'>⚠️ لا توجد إعدادات، سيتم إضافة الإعدادات الافتراضية</p>";
        
        // إضافة إعدادات افتراضية
        $default_settings = [
            'company_name' => ['نظام المبيعات والمشتريات', 'text', 'اسم الشركة'],
            'company_address' => ['', 'text', 'عنوان الشركة'],
            'company_phone' => ['', 'text', 'هاتف الشركة'],
            'company_email' => ['', 'text', 'بريد الشركة الإلكتروني'],
            'company_tax_number' => ['', 'text', 'الرقم الضريبي للشركة'],
            'default_currency' => ['ر.س', 'text', 'العملة الافتراضية'],
            'default_tax_rate' => ['15', 'number', 'نسبة الضريبة الافتراضية'],
            'auto_print_pos' => ['1', 'boolean', 'طباعة POS تلقائياً'],
            'backup_enabled' => ['1', 'boolean', 'تفعيل النسخ الاحتياطي'],
            'system_language' => ['ar', 'text', 'لغة النظام'],
            'items_per_page' => ['20', 'number', 'عدد العناصر في الصفحة'],
            'low_stock_alert' => ['10', 'number', 'تنبيه المخزون المنخفض']
        ];
        
        foreach ($default_settings as $key => $data) {
            if (updateSystemSetting($key, $data[0], $data[1], $data[2])) {
                echo "<p style='color: green;'>✅ تم إضافة إعداد: $key</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إضافة إعداد: $key</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ الإعدادات موجودة:</p>";
        echo "<ul>";
        foreach ($settings as $key => $setting) {
            echo "<li><strong>$key:</strong> {$setting['value']} ({$setting['type']})</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p style='color: red;'>❌ جدول system_settings غير موجود</p>";
    
    // إنشاء الجدول
    $create_table_sql = "CREATE TABLE IF NOT EXISTS `system_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text DEFAULT NULL,
        `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
        `description` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($db->query($create_table_sql)) {
        echo "<p style='color: green;'>✅ تم إنشاء جدول system_settings</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء جدول system_settings: " . $db->error . "</p>";
    }
}

echo "<h2>3. فحص جدول المديرين</h2>";

$admins_check = $db->query("SHOW TABLES LIKE 'admins'");
if ($admins_check && $admins_check->num_rows > 0) {
    echo "<p style='color: green;'>✅ جدول admins موجود</p>";
    
    // عرض المديرين الحاليين
    $admins_result = $db->query("SELECT id, username, full_name, email, role, status FROM admins");
    if ($admins_result) {
        echo "<p>عدد المديرين: " . $admins_result->num_rows . "</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>";
        
        while ($admin = $admins_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$admin['id']}</td>";
            echo "<td>{$admin['username']}</td>";
            echo "<td>{$admin['full_name']}</td>";
            echo "<td>{$admin['email']}</td>";
            echo "<td>{$admin['role']}</td>";
            echo "<td>{$admin['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'>❌ جدول admins غير موجود</p>";
}

echo "<h2>4. اختبار الصفحات</h2>";

// اختبار صفحة إعدادات النظام
echo "<h3>أ. صفحة إعدادات النظام</h3>";
if (file_exists('admin_system.php')) {
    echo "<p style='color: green;'>✅ ملف admin_system.php موجود</p>";
    echo "<p><a href='admin_system.php' target='_blank'>فتح صفحة إعدادات النظام</a></p>";
} else {
    echo "<p style='color: red;'>❌ ملف admin_system.php غير موجود</p>";
}

// اختبار صفحة إدارة المديرين
echo "<h3>ب. صفحة إدارة المديرين</h3>";
if (file_exists('admin_manage_admins.php')) {
    echo "<p style='color: green;'>✅ ملف admin_manage_admins.php موجود</p>";
    echo "<p><a href='admin_manage_admins.php' target='_blank'>فتح صفحة إدارة المديرين</a></p>";
} else {
    echo "<p style='color: red;'>❌ ملف admin_manage_admins.php غير موجود</p>";
}

echo "<h2>5. معلومات المدير الحالي</h2>";

$current_admin = getCurrentAdmin();
if ($current_admin) {
    echo "<p style='color: green;'>✅ معلومات المدير الحالي:</p>";
    echo "<ul>";
    echo "<li><strong>ID:</strong> {$current_admin['id']}</li>";
    echo "<li><strong>اسم المستخدم:</strong> {$current_admin['username']}</li>";
    echo "<li><strong>الاسم الكامل:</strong> {$current_admin['full_name']}</li>";
    echo "<li><strong>البريد الإلكتروني:</strong> {$current_admin['email']}</li>";
    echo "<li><strong>مدير رئيسي:</strong> " . ($current_admin['is_super'] ? 'نعم' : 'لا') . "</li>";
    echo "</ul>";
    
    // اختبار الصلاحيات
    echo "<h3>ج. اختبار الصلاحيات</h3>";
    $permissions_to_test = ['manage_system', 'manage_admins', 'manage_users', 'view_reports'];
    
    foreach ($permissions_to_test as $permission) {
        $has_permission = hasAdminPermission($permission);
        $status = $has_permission ? '✅ يملك' : '❌ لا يملك';
        echo "<p>$status صلاحية: $permission</p>";
    }
} else {
    echo "<p style='color: red;'>❌ فشل في الحصول على معلومات المدير الحالي</p>";
}

echo "<h2>6. الروابط المفيدة</h2>";
echo "<ul>";
echo "<li><a href='admin_dashboard.php'>لوحة تحكم المدير</a></li>";
echo "<li><a href='admin_system.php'>إعدادات النظام</a></li>";
echo "<li><a href='admin_manage_admins.php'>إدارة المديرين</a></li>";
echo "<li><a href='admin_users.php'>إدارة المستخدمين</a></li>";
echo "<li><a href='admin_reports.php'>التقارير</a></li>";
echo "<li><a href='admin_logout.php'>تسجيل الخروج</a></li>";
echo "</ul>";

echo "<h2>✅ انتهى الفحص والإصلاح</h2>";
echo "<p style='color: green; font-weight: bold;'>تم فحص وإصلاح صفحات المدير بنجاح!</p>";

?>
