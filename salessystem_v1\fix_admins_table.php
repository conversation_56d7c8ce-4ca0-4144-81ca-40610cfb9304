<?php
/**
 * إصلاح هيكل جدول المديرين وتوحيد الأعمدة
 */

require_once __DIR__ . '/config/init.php';

echo "<h2>🔧 إصلاح هيكل جدول المديرين</h2>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    $results = [];
    $errors = [];

    // التحقق من وجود جدول المديرين
    $check_table = $db->query("SHOW TABLES LIKE 'admins'");
    if ($check_table->num_rows == 0) {
        // إنشاء جدول المديرين بالهيكل الصحيح
        $create_table_sql = "
            CREATE TABLE admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                status ENUM('active', 'inactive') DEFAULT 'active',
                permissions JSON,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        if ($db->query($create_table_sql)) {
            $results[] = "✅ تم إنشاء جدول المديرين بنجاح";
        } else {
            $errors[] = "❌ فشل في إنشاء جدول المديرين: " . $db->error;
        }
    } else {
        $results[] = "✅ جدول المديرين موجود";
        
        // فحص الأعمدة الموجودة
        $columns_query = $db->query("SHOW COLUMNS FROM admins");
        $existing_columns = [];
        while ($column = $columns_query->fetch_assoc()) {
            $existing_columns[$column['Field']] = $column;
        }
        
        // التحقق من وجود عمود is_active وتحويله إلى status
        if (isset($existing_columns['is_active']) && !isset($existing_columns['status'])) {
            $results[] = "🔄 تحويل عمود is_active إلى status...";
            
            // إضافة عمود status
            $add_status = $db->query("ALTER TABLE admins ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active' AFTER phone");
            if ($add_status) {
                $results[] = "✅ تم إضافة عمود status";
                
                // نقل البيانات من is_active إلى status
                $update_status = $db->query("UPDATE admins SET status = CASE WHEN is_active = 1 THEN 'active' ELSE 'inactive' END");
                if ($update_status) {
                    $results[] = "✅ تم نقل البيانات من is_active إلى status";
                    
                    // حذف عمود is_active القديم
                    $drop_is_active = $db->query("ALTER TABLE admins DROP COLUMN is_active");
                    if ($drop_is_active) {
                        $results[] = "✅ تم حذف عمود is_active القديم";
                    } else {
                        $errors[] = "❌ فشل في حذف عمود is_active: " . $db->error;
                    }
                } else {
                    $errors[] = "❌ فشل في نقل البيانات: " . $db->error;
                }
            } else {
                $errors[] = "❌ فشل في إضافة عمود status: " . $db->error;
            }
        } elseif (isset($existing_columns['status'])) {
            $results[] = "✅ عمود status موجود بالفعل";
        }
        
        // التحقق من الأعمدة المطلوبة الأخرى
        $required_columns = [
            'username' => "VARCHAR(50) UNIQUE NOT NULL",
            'password' => "VARCHAR(255) NOT NULL",
            'full_name' => "VARCHAR(100)",
            'email' => "VARCHAR(100)",
            'phone' => "VARCHAR(20)",
            'permissions' => "JSON",
            'last_login' => "TIMESTAMP NULL",
            'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
            'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ];
        
        foreach ($required_columns as $column => $definition) {
            if (!isset($existing_columns[$column])) {
                $add_column = $db->query("ALTER TABLE admins ADD COLUMN $column $definition");
                if ($add_column) {
                    $results[] = "✅ تم إضافة عمود $column";
                } else {
                    $errors[] = "❌ فشل في إضافة عمود $column: " . $db->error;
                }
            }
        }
    }
    
    // التحقق من وجود مدير افتراضي
    $admin_count = $db->query("SELECT COUNT(*) as count FROM admins")->fetch_assoc()['count'];
    if ($admin_count == 0) {
        $results[] = "🔄 إنشاء مدير افتراضي...";
        
        $default_admin = [
            'username' => 'admin',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'full_name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'status' => 'active',
            'permissions' => json_encode(['manage_users', 'view_all_data', 'manage_system', 'view_reports', 'manage_admins'])
        ];
        
        $stmt = $db->prepare("INSERT INTO admins (username, password, full_name, email, status, permissions) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", 
            $default_admin['username'],
            $default_admin['password'],
            $default_admin['full_name'],
            $default_admin['email'],
            $default_admin['status'],
            $default_admin['permissions']
        );
        
        if ($stmt->execute()) {
            $results[] = "✅ تم إنشاء المدير الافتراضي (admin/admin123)";
        } else {
            $errors[] = "❌ فشل في إنشاء المدير الافتراضي: " . $stmt->error;
        }
        $stmt->close();
    }
    
    // عرض النتائج
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>✅ النتائج الإيجابية:</h4>";
    foreach ($results as $result) {
        echo "<p>$result</p>";
    }
    echo "</div>";
    
    if (!empty($errors)) {
        echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>❌ الأخطاء:</h4>";
        foreach ($errors as $error) {
            echo "<p>$error</p>";
        }
        echo "</div>";
    }
    
    // عرض هيكل الجدول النهائي
    echo "<div style='background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>📋 هيكل جدول المديرين النهائي:</h4>";
    $columns_result = $db->query("SHOW COLUMNS FROM admins");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($column = $columns_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='admin_manage_admins.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>اختبار إدارة المديرين</a>";
    echo "<a href='admin_login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 5px;'>تسجيل الدخول</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
