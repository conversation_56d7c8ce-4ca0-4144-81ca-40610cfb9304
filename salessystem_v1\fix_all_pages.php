<?php
/**
 * إصلاح جميع الصفحات لتستخدم قاعدة البيانات الجديدة
 */

echo '<h1>🔧 إصلاح جميع الصفحات</h1>';

// قائمة الصفحات التي تحتاج إصلاح
$pages_to_fix = [
    'supervisor_reports.php',
    'supervisor_financial.php', 
    'supervisor_activity.php',
    'admin_reports.php',
    'admin_financial.php',
    'admin_activity.php'
];

echo '<h2>إصلاح الصفحات:</h2>';

foreach ($pages_to_fix as $page) {
    if (file_exists($page)) {
        echo "<h3>إصلاح $page:</h3>";
        
        $content = file_get_contents($page);
        $original_content = $content;
        
        // استبدال require_once للـ config
        $content = str_replace(
            "require_once __DIR__ . '/config/init.php';",
            "require_once __DIR__ . '/config/simple_db_config.php';\nsession_start();",
            $content
        );
        
        // استبدال التحقق من تسجيل الدخول للمشرفين
        if (strpos($page, 'supervisor_') === 0) {
            $content = preg_replace(
                '/\/\/ التحقق من تسجيل دخول المدير.*?exit\(\);\s*}/s',
                "// التحقق من تسجيل دخول المشرف\nif (!isset(\$_SESSION['supervisor_logged_in']) || !\$_SESSION['supervisor_logged_in']) {\n    header(\"Location: work_management_login.php\");\n    exit();\n}",
                $content
            );
        }
        
        // استبدال التحقق من تسجيل الدخول للمديرين
        if (strpos($page, 'admin_') === 0) {
            $content = preg_replace(
                '/\/\/ التحقق من تسجيل دخول المدير.*?exit\(\);\s*}/s',
                "// التحقق من تسجيل دخول المدير\nif (!isset(\$_SESSION['admin_logged_in']) || !\$_SESSION['admin_logged_in']) {\n    header(\"Location: work_management_login.php\");\n    exit();\n}",
                $content
            );
        }
        
        // استبدال getUnifiedDB بـ getSimpleDB
        $content = str_replace('getUnifiedDB()', 'getSimpleDB()', $content);
        
        // استبدال isAdminLoggedIn() checks
        $content = str_replace(
            'if (!isAdminLoggedIn()) {',
            'if (!isset($_SESSION[\'admin_logged_in\']) || !$_SESSION[\'admin_logged_in\']) {',
            $content
        );
        
        // استبدال hasAdminPermission() checks
        $content = preg_replace(
            '/if \(!hasAdminPermission\([^)]+\)\) \{[^}]+\}/s',
            '',
            $content
        );
        
        if ($content !== $original_content) {
            if (file_put_contents($page, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $page</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح $page</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $page لا يحتاج إصلاح</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ $page غير موجود</p>";
    }
}

// إصلاح ملفات الـ headers
echo '<h2>إصلاح ملفات الـ Headers:</h2>';

$header_files = [
    'includes/supervisor_header.php',
    'includes/admin_header_new.php'
];

foreach ($header_files as $header_file) {
    if (file_exists($header_file)) {
        echo "<h3>إصلاح $header_file:</h3>";
        
        $content = file_get_contents($header_file);
        $original_content = $content;
        
        // استبدال getUnifiedDB بـ getSimpleDB
        $content = str_replace('getUnifiedDB()', 'getSimpleDB()', $content);
        
        // إضافة require للـ config إذا لم يكن موجود
        if (strpos($content, 'simple_db_config.php') === false) {
            $content = "<?php require_once __DIR__ . '/../config/simple_db_config.php'; ?>\n" . $content;
        }
        
        if ($content !== $original_content) {
            if (file_put_contents($header_file, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $header_file</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح $header_file</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $header_file لا يحتاج إصلاح</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ $header_file غير موجود</p>";
    }
}

// إنشاء ملفات logout محدثة
echo '<h2>إنشاء ملفات Logout محدثة:</h2>';

// admin_logout محدث
$admin_logout_content = '<?php
session_start();

// مسح جلسة المدير
unset($_SESSION["admin_logged_in"]);
unset($_SESSION["admin_id"]);
unset($_SESSION["admin_username"]);
unset($_SESSION["admin_name"]);
unset($_SESSION["admin_department"]);
unset($_SESSION["admin_role"]);

// تدمير الجلسة
session_destroy();

// إعادة التوجه لصفحة تسجيل الدخول
header("Location: work_management_login.php");
exit;
?>';

if (file_put_contents('admin_logout.php', $admin_logout_content)) {
    echo '<p style="color: green;">✅ تم إنشاء admin_logout.php محدث</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء admin_logout.php</p>';
}

// user_logout محدث
$user_logout_content = '<?php
session_start();

// مسح جلسة المستخدم
unset($_SESSION["user_logged_in"]);
unset($_SESSION["user_id"]);
unset($_SESSION["username"]);
unset($_SESSION["user_name"]);

// تدمير الجلسة
session_destroy();

// إعادة التوجه لصفحة تسجيل الدخول
header("Location: work_management_login.php");
exit;
?>';

if (file_put_contents('logout.php', $user_logout_content)) {
    echo '<p style="color: green;">✅ تم تحديث logout.php</p>';
} else {
    echo '<p style="color: red;">❌ فشل في تحديث logout.php</p>';
}

// اختبار الصفحات المُصلحة
echo '<h2>اختبار الصفحات المُصلحة:</h2>';

require_once 'config/simple_db_config.php';
$test_db = getSimpleDB();

if ($test_db) {
    echo '<p style="color: green;">✅ اتصال قاعدة البيانات يعمل</p>';
    
    foreach ($pages_to_fix as $page) {
        if (file_exists($page)) {
            echo "<p><a href='$page' target='_blank' style='color: blue;'>🔗 اختبار $page</a></p>";
        }
    }
    
    $test_db->close();
} else {
    echo '<p style="color: red;">❌ اتصال قاعدة البيانات لا يعمل</p>';
}

echo '<div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h2>✅ تم إصلاح جميع الصفحات!</h2>';
echo '<h3>الإصلاحات المطبقة:</h3>';
echo '<ul>';
echo '<li>تحديث require_once لاستخدام simple_db_config.php</li>';
echo '<li>استبدال getUnifiedDB() بـ getSimpleDB()</li>';
echo '<li>تحديث التحقق من تسجيل الدخول</li>';
echo '<li>إزالة التحقق من الصلاحيات المعقدة</li>';
echo '<li>تحديث ملفات logout</li>';
echo '</ul>';
echo '<h3>الخطوات التالية:</h3>';
echo '<ol>';
echo '<li><a href="work_management_login.php">اختبار تسجيل الدخول</a></li>';
echo '<li><a href="test_sessions.php">اختبار الجلسات</a></li>';
echo '</ol>';
echo '</div>';
?>
