<?php
/**
 * ملف إصلاح مشاكل قاعدة البيانات
 */

require_once __DIR__ . '/config/init.php';

echo "<h1>إصلاح مشاكل قاعدة البيانات</h1>";

$db = getUnifiedDB();
if (!$db) {
    die("<p style='color: red;'>❌ فشل في الاتصال بقاعدة البيانات</p>");
}

echo "<h2>1. إضافة بيانات تجريبية للعملاء والموردين</h2>";

// إضافة عميل تجريبي
try {
    // التحقق من وجود مستخدم تجريبي
    $user_check = $db->query("SELECT id FROM users WHERE username = 'testuser' LIMIT 1");
    if ($user_check && $user_check->num_rows > 0) {
        $user = $user_check->fetch_assoc();
        $user_id = $user['id'];
        echo "<p style='color: green;'>✅ المستخدم التجريبي موجود (ID: $user_id)</p>";
    } else {
        // إنشاء مستخدم تجريبي
        $username = 'testuser';
        $email = '<EMAIL>';
        $password = password_hash('123456', PASSWORD_DEFAULT);
        $full_name = 'مستخدم تجريبي';
        
        $stmt = $db->prepare("INSERT INTO users (username, email, password, full_name, status) VALUES (?, ?, ?, ?, 'active')");
        $stmt->bind_param('ssss', $username, $email, $password, $full_name);
        
        if ($stmt->execute()) {
            $user_id = $db->insert_id;
            echo "<p style='color: green;'>✅ تم إنشاء المستخدم التجريبي (ID: $user_id)</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء المستخدم التجريبي</p>";
            exit;
        }
    }
    
    // إضافة عملاء تجريبيين
    $customers_data = [
        ['عميل تجريبي 1', '0501234567', '<EMAIL>', '1234567890', 'الرياض، المملكة العربية السعودية', 'customer'],
        ['مورد تجريبي 1', '0509876543', '<EMAIL>', '0987654321', 'جدة، المملكة العربية السعودية', 'supplier'],
        ['عميل تجريبي 2', '0555555555', '<EMAIL>', '5555555555', 'الدمام، المملكة العربية السعودية', 'customer']
    ];
    
    foreach ($customers_data as $index => $customer_data) {
        $check_stmt = $db->prepare("SELECT id FROM customers WHERE name = ? AND user_id = ?");
        $check_stmt->bind_param('si', $customer_data[0], $user_id);
        $check_stmt->execute();
        $existing = $check_stmt->get_result()->fetch_assoc();
        
        if (!$existing) {
            $insert_stmt = $db->prepare("INSERT INTO customers (user_id, name, phone, email, tax_number, address, customer_type) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $insert_stmt->bind_param('issssss', $user_id, $customer_data[0], $customer_data[1], $customer_data[2], $customer_data[3], $customer_data[4], $customer_data[5]);
            
            if ($insert_stmt->execute()) {
                $customer_id = $db->insert_id;
                echo "<p style='color: green;'>✅ تم إضافة {$customer_data[0]} (ID: $customer_id)</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إضافة {$customer_data[0]}</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ {$customer_data[0]} موجود بالفعل (ID: {$existing['id']})</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<h2>2. إضافة منتجات تجريبية</h2>";

try {
    $products_data = [
        ['منتج تجريبي 1', 'وصف المنتج الأول', 100.00, 15.00, 'إلكترونيات', 50, 'قطعة', '1234567890123'],
        ['منتج تجريبي 2', 'وصف المنتج الثاني', 250.50, 15.00, 'ملابس', 30, 'قطعة', '2345678901234'],
        ['منتج تجريبي 3', 'وصف المنتج الثالث', 75.25, 15.00, 'أغذية ومشروبات', 100, 'كيلو', '3456789012345']
    ];
    
    foreach ($products_data as $product_data) {
        $check_stmt = $db->prepare("SELECT id FROM products WHERE name = ?");
        $check_stmt->bind_param('s', $product_data[0]);
        $check_stmt->execute();
        $existing = $check_stmt->get_result()->fetch_assoc();
        
        if (!$existing) {
            $insert_stmt = $db->prepare("INSERT INTO products (name, description, price, tax_rate, category, stock_quantity, unit, barcode, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $insert_stmt->bind_param('ssddsdssi', $product_data[0], $product_data[1], $product_data[2], $product_data[3], $product_data[4], $product_data[5], $product_data[6], $product_data[7], $user_id);
            
            if ($insert_stmt->execute()) {
                $product_id = $db->insert_id;
                echo "<p style='color: green;'>✅ تم إضافة {$product_data[0]} (ID: $product_id)</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إضافة {$product_data[0]}: " . $db->error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ {$product_data[0]} موجود بالفعل (ID: {$existing['id']})</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إضافة المنتجات: " . $e->getMessage() . "</p>";
}

echo "<h2>3. فحص الجداول والبيانات</h2>";

try {
    // فحص عدد السجلات في كل جدول
    $tables_to_check = ['users', 'admins', 'customers', 'products', 'system_settings'];
    
    foreach ($tables_to_check as $table) {
        $result = $db->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p style='color: green;'>✅ جدول $table: {$row['count']} سجل</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في فحص جدول $table</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص الجداول: " . $e->getMessage() . "</p>";
}

echo "<h2>4. اختبار تسجيل الدخول</h2>";

try {
    // فحص بيانات المدير الرئيسي
    $admin_check = $db->query("SELECT id, username, email, role FROM admins WHERE role = 'super_admin' LIMIT 1");
    if ($admin_check && $admin_check->num_rows > 0) {
        $admin = $admin_check->fetch_assoc();
        echo "<p style='color: green;'>✅ المدير الرئيسي موجود:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> {$admin['id']}</li>";
        echo "<li><strong>اسم المستخدم:</strong> {$admin['username']}</li>";
        echo "<li><strong>البريد الإلكتروني:</strong> {$admin['email']}</li>";
        echo "<li><strong>الدور:</strong> {$admin['role']}</li>";
        echo "</ul>";
        echo "<p style='color: orange;'>⚠️ كلمة المرور الافتراضية: Admin@123456</p>";
    } else {
        echo "<p style='color: red;'>❌ المدير الرئيسي غير موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص المدير الرئيسي: " . $e->getMessage() . "</p>";
}

echo "<h2>5. الروابط المفيدة</h2>";
echo "<ul>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='login.php'>تسجيل دخول المستخدمين</a></li>";
echo "<li><a href='admin_login.php'>تسجيل دخول المديرين</a></li>";
echo "<li><a href='test_system.php'>اختبار النظام</a></li>";
echo "<li><a href='account_statement.php?type=customer&id=1'>كشف حساب عميل</a></li>";
echo "<li><a href='account_statement.php?type=supplier&id=2'>كشف حساب مورد</a></li>";
echo "</ul>";

echo "<h2>✅ انتهى الإصلاح</h2>";
echo "<p style='color: green; font-weight: bold;'>تم إصلاح جميع المشاكل المكتشفة بنجاح!</p>";

?>
