<?php
/**
 * إصلاح تضارب ملفات قاعدة البيانات
 */

echo '<h1>🔧 إصلاح تضارب ملفات قاعدة البيانات</h1>';

// البحث عن الملفات التي تستخدم كلا الملفين
$files_to_check = glob('*.php');
$files_with_both = [];
$files_with_unified = [];
$files_with_simple = [];

echo '<h2>فحص الملفات:</h2>';

foreach ($files_to_check as $file) {
    if ($file === 'fix_db_conflict.php') continue; // تجاهل هذا الملف
    
    $content = file_get_contents($file);
    $has_unified = strpos($content, 'unified_db_config.php') !== false;
    $has_simple = strpos($content, 'simple_db_config.php') !== false;
    
    if ($has_unified && $has_simple) {
        $files_with_both[] = $file;
        echo "<p style='color: red;'>❌ $file يستخدم كلا الملفين</p>";
    } elseif ($has_unified) {
        $files_with_unified[] = $file;
        echo "<p style='color: orange;'>⚠️ $file يستخدم unified_db_config.php</p>";
    } elseif ($has_simple) {
        $files_with_simple[] = $file;
        echo "<p style='color: green;'>✅ $file يستخدم simple_db_config.php</p>";
    }
}

echo '<h2>إحصائيات:</h2>';
echo '<ul>';
echo '<li>ملفات تستخدم كلا الملفين: ' . count($files_with_both) . '</li>';
echo '<li>ملفات تستخدم unified_db_config.php: ' . count($files_with_unified) . '</li>';
echo '<li>ملفات تستخدم simple_db_config.php: ' . count($files_with_simple) . '</li>';
echo '</ul>';

// إصلاح الملفات التي تستخدم كلا الملفين
if (!empty($files_with_both)) {
    echo '<h2>إصلاح الملفات المتضاربة:</h2>';
    
    foreach ($files_with_both as $file) {
        echo "<h3>إصلاح $file:</h3>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // إزالة require لـ unified_db_config.php
        $content = preg_replace(
            '/require_once\s+[\'"].*unified_db_config\.php[\'"];?\s*\n?/',
            '',
            $content
        );
        
        // استبدال getUnifiedDB بـ getSimpleDB
        $content = str_replace('getUnifiedDB()', 'getSimpleDB()', $content);
        $content = str_replace('getMainDB()', 'getSimpleDB()', $content);
        $content = str_replace('getOperationsDB()', 'getSimpleDB()', $content);
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $file</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح $file</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $file لا يحتاج إصلاح</p>";
        }
    }
}

// تحديث الملفات التي تستخدم unified_db_config.php فقط
if (!empty($files_with_unified)) {
    echo '<h2>تحديث الملفات لاستخدام simple_db_config.php:</h2>';
    
    foreach ($files_with_unified as $file) {
        echo "<h3>تحديث $file:</h3>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // استبدال require لـ unified_db_config.php بـ simple_db_config.php
        $content = str_replace(
            'unified_db_config.php',
            'simple_db_config.php',
            $content
        );
        
        // استبدال getUnifiedDB بـ getSimpleDB
        $content = str_replace('getUnifiedDB()', 'getSimpleDB()', $content);
        $content = str_replace('getMainDB()', 'getSimpleDB()', $content);
        $content = str_replace('getOperationsDB()', 'getSimpleDB()', $content);
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "<p style='color: green;'>✅ تم تحديث $file</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في تحديث $file</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $file لا يحتاج تحديث</p>";
        }
    }
}

// إنشاء ملف إعدادات موحد جديد
echo '<h2>إنشاء ملف إعدادات موحد:</h2>';

$unified_simple_config = '<?php
/**
 * ملف إعدادات قاعدة البيانات الموحد والمبسط
 */

function getSimpleDB() {
    $mysqli = new mysqli("localhost", "root", "", "salessystem_v2");
    
    if ($mysqli->connect_error) {
        error_log("Database connection failed: " . $mysqli->connect_error);
        return null;
    }
    
    $mysqli->set_charset("utf8mb4");
    return $mysqli;
}

// دوال التوافق للملفات القديمة
if (!function_exists("getUnifiedDB")) {
    function getUnifiedDB() { return getSimpleDB(); }
}

if (!function_exists("getMainDB")) {
    function getMainDB() { return getSimpleDB(); }
}

if (!function_exists("getOperationsDB")) {
    function getOperationsDB() { return getSimpleDB(); }
}

// دالة اختبار
function testSimpleConnection() {
    $db = getSimpleDB();
    if (!$db) return ["success" => false, "error" => "فشل الاتصال"];
    
    $result = $db->query("SELECT 1");
    $db->close();
    
    return $result ? 
        ["success" => true, "message" => "نجح الاتصال"] : 
        ["success" => false, "error" => "فشل في الاستعلام"];
}
?>';

if (file_put_contents('config/unified_simple_db_config.php', $unified_simple_config)) {
    echo '<p style="color: green;">✅ تم إنشاء config/unified_simple_db_config.php</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء config/unified_simple_db_config.php</p>';
}

// اختبار الاتصال
echo '<h2>اختبار الاتصال:</h2>';

require_once 'config/simple_db_config.php';
$test_db = getSimpleDB();

if ($test_db) {
    echo '<p style="color: green;">✅ اختبار getSimpleDB(): نجح</p>';
    
    // اختبار الجداول الأساسية
    $tables = ['users', 'admins', 'supervisors'];
    foreach ($tables as $table) {
        $result = $test_db->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p style='margin-left: 20px; color: green;'>✅ جدول $table: $count صف</p>";
        } else {
            echo "<p style='margin-left: 20px; color: red;'>❌ خطأ في جدول $table: " . $test_db->error . "</p>";
        }
    }
    
    $test_db->close();
} else {
    echo '<p style="color: red;">❌ اختبار getSimpleDB(): فشل</p>';
}

echo '<div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h2>✅ تم إصلاح تضارب قاعدة البيانات!</h2>';
echo '<h3>الإصلاحات المطبقة:</h3>';
echo '<ul>';
echo '<li>إزالة التعريفات المكررة من simple_db_config.php</li>';
echo '<li>إصلاح الملفات التي تستخدم كلا الملفين</li>';
echo '<li>تحديث الملفات لاستخدام simple_db_config.php</li>';
echo '<li>إنشاء ملف إعدادات موحد جديد</li>';
echo '</ul>';
echo '<h3>التوصية:</h3>';
echo '<p>استخدم <strong>simple_db_config.php</strong> في جميع الملفات الجديدة</p>';
echo '<h3>الخطوات التالية:</h3>';
echo '<ol>';
echo '<li><a href="work_management_login.php">اختبار تسجيل الدخول</a></li>';
echo '<li><a href="supervisor_dashboard.php">اختبار لوحة المشرفين</a></li>';
echo '<li><a href="admin_dashboard.php">اختبار لوحة الإدارة</a></li>';
echo '</ol>';
echo '</div>';
?>
