<?php
/**
 * إصلاح أخطاء $$ في جميع ملفات PHP
 */

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = [
    'admin_users.php',
    'admin_reports.php',
    'admin_financial.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$fixed_files = [];
$errors = [];

echo "<h2>🔧 إصلاح أخطاء $$ في ملفات PHP</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>🎯 المشكلة:</h4>";
echo "<p>تم العثور على أخطاء في استخدام <code>\$\$variable</code> بدلاً من <code>\$variable</code></p>";
echo "<h4>🔧 الحل:</h4>";
echo "<p>استبدال جميع <code>\$\$</code> بـ <code>\$</code> في المتغيرات</p>";
echo "</div>";

foreach ($files_to_fix as $file) {
    $file_path = __DIR__ . '/' . $file;
    
    echo "<h3>🔧 إصلاح: $file</h3>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    if (!file_exists($file_path)) {
        echo "<span style='color: orange;'>⚠️ الملف غير موجود</span><br>";
        echo "</div>";
        continue;
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $file";
        echo "<span style='color: red;'>❌ فشل في قراءة الملف</span><br>";
        echo "</div>";
        continue;
    }
    
    $original_content = $content;
    
    // عد الأخطاء قبل الإصلاح
    $error_count_before = substr_count($content, '$$');
    
    if ($error_count_before === 0) {
        echo "<span style='color: green;'>✅ لا توجد أخطاء $$ في هذا الملف</span><br>";
        echo "</div>";
        continue;
    }
    
    echo "<span style='color: orange;'>⚠️ تم العثور على $error_count_before خطأ $$</span><br>";
    
    // إصلاح الأخطاء
    $patterns_to_fix = [
        // إصلاح المتغيرات العادية
        '/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => '$$$1[',
        
        // إصلاح المتغيرات في echo
        '/echo\s+\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'echo $$1[',
        
        // إصلاح المتغيرات في htmlspecialchars
        '/htmlspecialchars\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'htmlspecialchars($$1[',
        
        // إصلاح المتغيرات في date
        '/date\([^,]+,\s*strtotime\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'date($1, strtotime($$1[',
        
        // إصلاح المتغيرات في substr
        '/substr\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'substr($$1[',
        
        // إصلاح المتغيرات في strtoupper
        '/strtoupper\(substr\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'strtoupper(substr($$1[',
        
        // إصلاح المتغيرات في الشروط
        '/if\s*\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'if ($$1[',
        '/if\s*\(!\s*empty\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'if (!empty($$1[',
        '/if\s*\(empty\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'if (empty($$1[',
        
        // إصلاح المتغيرات في المقارنات
        '/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[\s*[^]]*\]\s*===/' => '$$1[$2] ===',
        '/\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[\s*[^]]*\]\s*!==/' => '$$1[$2] !==',
        
        // إصلاح unset
        '/unset\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'unset($$1[',
        
        // إصلاح number_format
        '/number_format\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'number_format($$1[',
        '/number_format\(\(\$\$([a-zA-Z_][a-zA-Z0-9_]*)\[/' => 'number_format(($$1[',
    ];
    
    $changes_made = false;
    foreach ($patterns_to_fix as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $content = $new_content;
            $changes_made = true;
        }
    }
    
    // إصلاح بسيط لجميع حالات $$
    $content = preg_replace('/\$\$([a-zA-Z_][a-zA-Z0-9_]*)/', '$$$1', $content);
    $changes_made = true;
    
    // عد الأخطاء بعد الإصلاح
    $error_count_after = substr_count($content, '$$');
    
    if ($error_count_after < $error_count_before) {
        echo "<span style='color: green;'>✅ تم إصلاح " . ($error_count_before - $error_count_after) . " خطأ</span><br>";
        
        if ($error_count_after > 0) {
            echo "<span style='color: orange;'>⚠️ متبقي $error_count_after خطأ (قد يحتاج إصلاح يدوي)</span><br>";
        }
        
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.double_dollar_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $fixed_files[] = $file;
            echo "<span style='color: green; font-weight: bold;'>✅ تم حفظ الإصلاحات</span><br>";
            echo "<span style='color: blue;'>📁 نسخة احتياطية: " . basename($backup_path) . "</span><br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $file";
            echo "<span style='color: red;'>❌ فشل في حفظ الملف</span><br>";
        }
    } else {
        echo "<span style='color: red;'>❌ لم يتم إصلاح أي أخطاء</span><br>";
    }
    
    echo "</div>";
}

// عرض الملخص
echo "<h3>📋 ملخص إصلاح أخطاء $$</h3>";

if (!empty($fixed_files)) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>✅ الملفات المُصلحة:</h4>";
    foreach ($fixed_files as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #ffebee; color: #d32f2f; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

// رسالة النتيجة
if (count($fixed_files) > 0) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>🎉 تم إصلاح " . count($fixed_files) . " ملف بنجاح!</h3>";
    echo "<p>تم إصلاح جميع أخطاء $$ في الملفات المحددة.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3e0; color: #f57c00; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>⚠️ لم يتم إصلاح أي ملفات</h3>";
    echo "<p>قد تحتاج الملفات إصلاح يدوي.</p>";
    echo "</div>";
}

// روابط الاختبار
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار الملفات المُصلحة:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;'>";
foreach ($fixed_files as $file) {
    echo "<a href='$file' target='_blank' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 8px; font-size: 14px; margin: 5px;'>$file</a>";
}
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='comprehensive_error_scanner.php' style='background: #2196f3; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🔍 فحص شامل</a>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🏠 لوحة التحكم</a>";
echo "</div>";
?>
