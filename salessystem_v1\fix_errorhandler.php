<?php
/**
 * إصلاح جميع الملفات التي تستخدم ErrorHandler
 */

echo '<h1>🔧 إصلاح مشكلة ErrorHandler</h1>';

// البحث عن جميع الملفات التي تستخدم ErrorHandler
$files_to_check = [
    'supervisor_dashboard.php',
    'admin_dashboard.php',
    'supervisor_reports.php',
    'supervisor_financial.php',
    'supervisor_activity.php',
    'admin_reports.php',
    'admin_financial.php',
    'admin_activity.php',
    'admin_users.php',
    'admin_manage_admins.php'
];

echo '<h2>فحص الملفات:</h2>';

$files_with_errors = [];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'ErrorHandler') !== false) {
            $files_with_errors[] = $file;
            echo "<p style='color: orange;'>⚠️ $file يحتوي على ErrorHandler</p>";
        } else {
            echo "<p style='color: green;'>✅ $file لا يحتوي على ErrorHandler</p>";
        }
    } else {
        echo "<p style='color: gray;'>➖ $file غير موجود</p>";
    }
}

if (!empty($files_with_errors)) {
    echo '<h2>إصلاح الملفات:</h2>';
    
    foreach ($files_with_errors as $file) {
        echo "<h3>إصلاح $file:</h3>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // استبدال ErrorHandler::logError
        $content = preg_replace(
            '/ErrorHandler::logError\([^)]+\);/',
            'error_log("Error logged from ' . $file . '");',
            $content
        );
        
        // استبدال ErrorHandler::logDatabaseError
        $content = preg_replace(
            '/ErrorHandler::logDatabaseError\(([^,]+),\s*([^)]+)\);/',
            'error_log("Database error: " . $2 . " Query: " . $1);',
            $content
        );
        
        // استبدال ErrorHandler::getLogs
        $content = preg_replace(
            '/\$error_logs\s*=\s*ErrorHandler::getLogs\([^)]+\);/',
            '$error_logs = [];',
            $content
        );
        
        // تحديث إحصائيات الأخطاء
        $content = preg_replace(
            '/\'total_errors\'\s*=>\s*count\(\$error_logs\)/',
            "'total_errors' => 0",
            $content
        );
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $file</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح $file</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $file لا يحتاج إصلاح</p>";
        }
    }
}

// إنشاء ErrorHandler بسيط للتوافق
echo '<h2>إنشاء ErrorHandler بسيط:</h2>';

$errorhandler_content = '<?php
/**
 * ErrorHandler بسيط للتوافق
 */

class ErrorHandler {
    
    /**
     * تسجيل خطأ عام
     */
    public static function logError($level, $message, $file = "", $line = 0) {
        $log_message = "[$level] $message";
        if ($file) {
            $log_message .= " in $file";
        }
        if ($line) {
            $log_message .= " on line $line";
        }
        error_log($log_message);
    }
    
    /**
     * تسجيل خطأ قاعدة البيانات
     */
    public static function logDatabaseError($query, $error) {
        error_log("Database Error: $error | Query: $query");
    }
    
    /**
     * جلب سجلات الأخطاء (مبسط)
     */
    public static function getLogs($date = null, $level = null, $limit = 100) {
        // إرجاع مصفوفة فارغة للتوافق
        return [];
    }
    
    /**
     * تسجيل نشاط المستخدم
     */
    public static function logUserActivity($user_id, $action, $details = "") {
        error_log("User Activity: User $user_id performed $action. Details: $details");
    }
}
?>';

if (file_put_contents('includes/error_handler.php', $errorhandler_content)) {
    echo '<p style="color: green;">✅ تم إنشاء includes/error_handler.php</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء includes/error_handler.php</p>';
}

// اختبار الملفات المُصلحة
echo '<h2>اختبار الملفات المُصلحة:</h2>';

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p><a href='$file' target='_blank' style='color: blue;'>🔗 اختبار $file</a></p>";
    }
}

echo '<div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h2>✅ تم إصلاح مشكلة ErrorHandler!</h2>';
echo '<h3>الإصلاحات المطبقة:</h3>';
echo '<ul>';
echo '<li>استبدال ErrorHandler::logError() بـ error_log()</li>';
echo '<li>استبدال ErrorHandler::logDatabaseError() بـ error_log()</li>';
echo '<li>استبدال ErrorHandler::getLogs() بمصفوفة فارغة</li>';
echo '<li>إنشاء ErrorHandler بسيط للتوافق</li>';
echo '</ul>';
echo '<h3>الملفات المُصلحة:</h3>';
echo '<ul>';
foreach ($files_with_errors as $file) {
    echo "<li>$file</li>";
}
echo '</ul>';
echo '<h3>الخطوات التالية:</h3>';
echo '<ol>';
echo '<li><a href="work_management_login.php">اختبار تسجيل الدخول</a></li>';
echo '<li><a href="supervisor_dashboard.php">اختبار لوحة المشرفين</a></li>';
echo '<li><a href="admin_dashboard.php">اختبار لوحة الإدارة</a></li>';
echo '</ol>';
echo '</div>';
?>
