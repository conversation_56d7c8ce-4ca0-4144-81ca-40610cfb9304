<?php
/**
 * إصلاح مسارات الملفات الخاطئة
 */

echo '<h1>🔧 إصلاح مسارات الملفات</h1>';

// البحث عن الملفات التي تحتوي على مسارات خاطئة
$files_to_check = glob('*.php');
$files_with_errors = [];

echo '<h2>فحص المسارات:</h2>';

foreach ($files_to_check as $file) {
    if ($file === 'fix_file_paths.php') continue; // تجاهل هذا الملف
    
    $content = file_get_contents($file);
    
    // البحث عن مسارات خاطئة
    $patterns = [
        '__DIR__\.\'config/' => '__DIR__.\'/config/',
        '__DIR__\.\'includes/' => '__DIR__.\'/includes/',
        '__DIR__\.\'assets/' => '__DIR__.\'/assets/',
        '__DIR__\.\'uploads/' => '__DIR__.\'/uploads/',
    ];
    
    $has_errors = false;
    foreach ($patterns as $wrong => $correct) {
        if (preg_match('/' . $wrong . '/', $content)) {
            $has_errors = true;
            break;
        }
    }
    
    if ($has_errors) {
        $files_with_errors[] = $file;
        echo "<p style='color: red;'>❌ $file يحتوي على مسارات خاطئة</p>";
    } else {
        echo "<p style='color: green;'>✅ $file مسارات صحيحة</p>";
    }
}

// إصلاح الملفات
if (!empty($files_with_errors)) {
    echo '<h2>إصلاح المسارات:</h2>';
    
    foreach ($files_with_errors as $file) {
        echo "<h3>إصلاح $file:</h3>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // إصلاح المسارات
        $fixes = [
            "__DIR__.'config/" => "__DIR__.'/config/",
            "__DIR__.'includes/" => "__DIR__.'/includes/",
            "__DIR__.'assets/" => "__DIR__.'/assets/",
            "__DIR__.'uploads/" => "__DIR__.'/uploads/",
            "__DIR__ . 'config/" => "__DIR__ . '/config/",
            "__DIR__ . 'includes/" => "__DIR__ . '/includes/",
            "__DIR__ . 'assets/" => "__DIR__ . '/assets/",
            "__DIR__ . 'uploads/" => "__DIR__ . '/uploads/",
        ];
        
        foreach ($fixes as $wrong => $correct) {
            $content = str_replace($wrong, $correct, $content);
        }
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $file</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح $file</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $file لا يحتاج إصلاح</p>";
        }
    }
}

// فحص وجود الملفات المطلوبة
echo '<h2>فحص وجود الملفات المطلوبة:</h2>';

$required_files = [
    'config/simple_db_config.php',
    'includes/header_simple.php',
    'includes/footer.php',
    'includes/functions.php'
];

foreach ($required_files as $required_file) {
    if (file_exists($required_file)) {
        echo "<p style='color: green;'>✅ $required_file موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $required_file غير موجود</p>";
        
        // إنشاء الملفات المفقودة
        if ($required_file === 'includes/functions.php') {
            $functions_content = '<?php
/**
 * دوال مساعدة للنظام
 */

// دالة التحقق من تسجيل الدخول (للتوافق)
function redirectIfNotLoggedIn() {
    if (!isset($_SESSION["user_logged_in"]) || !$_SESSION["user_logged_in"]) {
        header("Location: work_management_login.php");
        exit();
    }
}

// دالة تنسيق التاريخ
function formatDate($date) {
    return date("Y-m-d H:i:s", strtotime($date));
}

// دالة تنسيق المبلغ
function formatAmount($amount) {
    return number_format($amount, 2) . " ريال";
}
?>';
            
            if (!is_dir('includes')) {
                mkdir('includes', 0755, true);
            }
            
            if (file_put_contents($required_file, $functions_content)) {
                echo "<p style='color: green; margin-left: 20px;'>✅ تم إنشاء $required_file</p>";
            }
        }
        
        if ($required_file === 'includes/header_simple.php') {
            $header_content = '<?php
/**
 * رأس الصفحة للنظام
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المبيعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
';
            
            if (file_put_contents($required_file, $header_content)) {
                echo "<p style='color: green; margin-left: 20px;'>✅ تم إنشاء $required_file</p>";
            }
        }
        
        if ($required_file === 'includes/footer.php') {
            $footer_content = '<?php
/**
 * تذييل الصفحة للنظام
 */
?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
            
            if (file_put_contents($required_file, $footer_content)) {
                echo "<p style='color: green; margin-left: 20px;'>✅ تم إنشاء $required_file</p>";
            }
        }
    }
}

// اختبار تحميل الملفات
echo '<h2>اختبار تحميل الملفات:</h2>';

try {
    require_once 'config/simple_db_config.php';
    echo '<p style="color: green;">✅ تم تحميل config/simple_db_config.php</p>';
    
    $db = getSimpleDB();
    if ($db) {
        echo '<p style="color: green;">✅ اتصال قاعدة البيانات يعمل</p>';
        $db->close();
    } else {
        echo '<p style="color: red;">❌ اتصال قاعدة البيانات لا يعمل</p>';
    }
} catch (Exception $e) {
    echo '<p style="color: red;">❌ خطأ في تحميل config/simple_db_config.php: ' . $e->getMessage() . '</p>';
}

try {
    if (file_exists('includes/functions.php')) {
        require_once 'includes/functions.php';
        echo '<p style="color: green;">✅ تم تحميل includes/functions.php</p>';
    }
} catch (Exception $e) {
    echo '<p style="color: red;">❌ خطأ في تحميل includes/functions.php: ' . $e->getMessage() . '</p>';
}

echo '<div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h2>✅ تم إصلاح مسارات الملفات!</h2>';
echo '<h3>الإصلاحات المطبقة:</h3>';
echo '<ul>';
echo '<li>إصلاح مسارات __DIR__ المفقودة الشرطة المائلة</li>';
echo '<li>إنشاء الملفات المفقودة في مجلد includes</li>';
echo '<li>اختبار تحميل الملفات</li>';
echo '</ul>';
echo '<h3>الملفات المُصلحة:</h3>';
echo '<ul>';
foreach ($files_with_errors as $file) {
    echo "<li>$file</li>";
}
echo '</ul>';
echo '<h3>الخطوات التالية:</h3>';
echo '<ol>';
echo '<li><a href="index.php">اختبار index.php</a></li>';
echo '<li><a href="work_management_login.php">اختبار تسجيل الدخول</a></li>';
echo '<li><a href="supervisor_dashboard.php">اختبار لوحة المشرفين</a></li>';
echo '</ol>';
echo '</div>';
?>
