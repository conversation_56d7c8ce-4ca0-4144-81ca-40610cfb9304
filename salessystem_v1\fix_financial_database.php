<?php
/**
 * إصلاح قاعدة البيانات للتقارير المالية
 */

require_once __DIR__ . '/config/simple_db_config.php';

$db = getSimpleDB();

if (!$db) {
    die("فشل في الاتصال بقاعدة البيانات");
}

echo "<h2>إصلاح قاعدة البيانات للتقارير المالية</h2>";

$fixes_applied = [];
$errors = [];

// 1. التأكد من وجود عمود user_id في جدول sales
echo "<h3>1. فحص وإصلاح جدول sales:</h3>";
$sales_exists = $db->query("SHOW TABLES LIKE 'sales'");
if ($sales_exists && $sales_exists->num_rows > 0) {
    echo "✅ جدول sales موجود<br>";
    
    // فحص عمود user_id
    $user_id_check = $db->query("SHOW COLUMNS FROM sales LIKE 'user_id'");
    if (!$user_id_check || $user_id_check->num_rows == 0) {
        echo "⚠️ عمود user_id غير موجود في جدول sales، جاري الإضافة...<br>";
        $add_user_id = $db->query("ALTER TABLE sales ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
        if ($add_user_id) {
            $fixes_applied[] = "تم إضافة عمود user_id إلى جدول sales";
            echo "✅ تم إضافة عمود user_id إلى جدول sales<br>";
        } else {
            $errors[] = "فشل في إضافة عمود user_id إلى جدول sales: " . $db->error;
            echo "❌ فشل في إضافة عمود user_id: " . $db->error . "<br>";
        }
    } else {
        echo "✅ عمود user_id موجود في جدول sales<br>";
    }
} else {
    echo "❌ جدول sales غير موجود، جاري الإنشاء...<br>";
    $create_sales = $db->query("
        CREATE TABLE sales (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL DEFAULT 1,
            customer_id INT,
            invoice_number VARCHAR(50) NOT NULL,
            date DATE NOT NULL,
            subtotal DECIMAL(10,2) DEFAULT 0,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50),
            payment_status ENUM('paid', 'unpaid', 'partial') DEFAULT 'unpaid',
            paid_amount DECIMAL(10,2) DEFAULT 0,
            remaining_amount DECIMAL(10,2) DEFAULT 0,
            payment_date DATE NULL,
            payment_reference VARCHAR(100),
            payment_notes TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY idx_invoice_number (invoice_number),
            INDEX idx_user_id (user_id),
            INDEX idx_customer_id (customer_id),
            INDEX idx_date (date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    if ($create_sales) {
        $fixes_applied[] = "تم إنشاء جدول sales";
        echo "✅ تم إنشاء جدول sales<br>";
    } else {
        $errors[] = "فشل في إنشاء جدول sales: " . $db->error;
        echo "❌ فشل في إنشاء جدول sales: " . $db->error . "<br>";
    }
}

// 2. التأكد من وجود عمود user_id في جدول purchases
echo "<h3>2. فحص وإصلاح جدول purchases:</h3>";
$purchases_exists = $db->query("SHOW TABLES LIKE 'purchases'");
if ($purchases_exists && $purchases_exists->num_rows > 0) {
    echo "✅ جدول purchases موجود<br>";
    
    // فحص عمود user_id
    $user_id_check = $db->query("SHOW COLUMNS FROM purchases LIKE 'user_id'");
    if (!$user_id_check || $user_id_check->num_rows == 0) {
        echo "⚠️ عمود user_id غير موجود في جدول purchases، جاري الإضافة...<br>";
        $add_user_id = $db->query("ALTER TABLE purchases ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
        if ($add_user_id) {
            $fixes_applied[] = "تم إضافة عمود user_id إلى جدول purchases";
            echo "✅ تم إضافة عمود user_id إلى جدول purchases<br>";
        } else {
            $errors[] = "فشل في إضافة عمود user_id إلى جدول purchases: " . $db->error;
            echo "❌ فشل في إضافة عمود user_id: " . $db->error . "<br>";
        }
    } else {
        echo "✅ عمود user_id موجود في جدول purchases<br>";
    }
} else {
    echo "❌ جدول purchases غير موجود، جاري الإنشاء...<br>";
    $create_purchases = $db->query("
        CREATE TABLE purchases (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL DEFAULT 1,
            supplier_id INT,
            invoice_number VARCHAR(50) NOT NULL,
            date DATE NOT NULL,
            subtotal DECIMAL(10,2) DEFAULT 0,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50),
            payment_status ENUM('paid', 'unpaid', 'partial') DEFAULT 'unpaid',
            paid_amount DECIMAL(10,2) DEFAULT 0,
            remaining_amount DECIMAL(10,2) DEFAULT 0,
            payment_date DATE NULL,
            payment_reference VARCHAR(100),
            payment_notes TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY idx_invoice_number (invoice_number),
            INDEX idx_user_id (user_id),
            INDEX idx_supplier_id (supplier_id),
            INDEX idx_date (date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    if ($create_purchases) {
        $fixes_applied[] = "تم إنشاء جدول purchases";
        echo "✅ تم إنشاء جدول purchases<br>";
    } else {
        $errors[] = "فشل في إنشاء جدول purchases: " . $db->error;
        echo "❌ فشل في إنشاء جدول purchases: " . $db->error . "<br>";
    }
}

// 3. التأكد من وجود عمود customer_type في جدول customers
echo "<h3>3. فحص وإصلاح جدول customers:</h3>";
$customers_exists = $db->query("SHOW TABLES LIKE 'customers'");
if ($customers_exists && $customers_exists->num_rows > 0) {
    echo "✅ جدول customers موجود<br>";
    
    // فحص عمود customer_type
    $customer_type_check = $db->query("SHOW COLUMNS FROM customers LIKE 'customer_type'");
    if (!$customer_type_check || $customer_type_check->num_rows == 0) {
        echo "⚠️ عمود customer_type غير موجود في جدول customers، جاري الإضافة...<br>";
        $add_customer_type = $db->query("ALTER TABLE customers ADD COLUMN customer_type ENUM('customer', 'supplier') NOT NULL DEFAULT 'customer'");
        if ($add_customer_type) {
            $fixes_applied[] = "تم إضافة عمود customer_type إلى جدول customers";
            echo "✅ تم إضافة عمود customer_type إلى جدول customers<br>";
        } else {
            $errors[] = "فشل في إضافة عمود customer_type إلى جدول customers: " . $db->error;
            echo "❌ فشل في إضافة عمود customer_type: " . $db->error . "<br>";
        }
    } else {
        echo "✅ عمود customer_type موجود في جدول customers<br>";
    }
    
    // فحص عمود user_id
    $user_id_check = $db->query("SHOW COLUMNS FROM customers LIKE 'user_id'");
    if (!$user_id_check || $user_id_check->num_rows == 0) {
        echo "⚠️ عمود user_id غير موجود في جدول customers، جاري الإضافة...<br>";
        $add_user_id = $db->query("ALTER TABLE customers ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
        if ($add_user_id) {
            $fixes_applied[] = "تم إضافة عمود user_id إلى جدول customers";
            echo "✅ تم إضافة عمود user_id إلى جدول customers<br>";
        } else {
            $errors[] = "فشل في إضافة عمود user_id إلى جدول customers: " . $db->error;
            echo "❌ فشل في إضافة عمود user_id: " . $db->error . "<br>";
        }
    } else {
        echo "✅ عمود user_id موجود في جدول customers<br>";
    }
} else {
    echo "❌ جدول customers غير موجود، جاري الإنشاء...<br>";
    $create_customers = $db->query("
        CREATE TABLE customers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL DEFAULT 1,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            tax_number VARCHAR(50),
            address TEXT,
            customer_type ENUM('customer', 'supplier') NOT NULL DEFAULT 'customer',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_name (name),
            INDEX idx_phone (phone),
            INDEX idx_customer_type (customer_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    if ($create_customers) {
        $fixes_applied[] = "تم إنشاء جدول customers";
        echo "✅ تم إنشاء جدول customers<br>";
    } else {
        $errors[] = "فشل في إنشاء جدول customers: " . $db->error;
        echo "❌ فشل في إنشاء جدول customers: " . $db->error . "<br>";
    }
}

// 4. إضافة بيانات تجريبية إذا لم تكن موجودة
echo "<h3>4. إضافة بيانات تجريبية:</h3>";

// فحص وجود مستخدمين
$users_count = $db->query("SELECT COUNT(*) as count FROM users");
if ($users_count) {
    $count = $users_count->fetch_assoc()['count'];
    if ($count == 0) {
        echo "⚠️ لا توجد مستخدمين، جاري إضافة مستخدم تجريبي...<br>";
        $add_user = $db->query("INSERT INTO users (username, password, full_name, email, status) VALUES ('testuser', '" . password_hash('123456', PASSWORD_DEFAULT) . "', 'مستخدم تجريبي', '<EMAIL>', 'active')");
        if ($add_user) {
            $fixes_applied[] = "تم إضافة مستخدم تجريبي";
            echo "✅ تم إضافة مستخدم تجريبي (testuser / 123456)<br>";
        }
    } else {
        echo "✅ يوجد $count مستخدم في النظام<br>";
    }
}

echo "<h3>ملخص الإصلاحات:</h3>";
if (!empty($fixes_applied)) {
    echo "<div style='color: green;'>";
    foreach ($fixes_applied as $fix) {
        echo "✅ " . $fix . "<br>";
    }
    echo "</div>";
} else {
    echo "<p style='color: blue;'>✅ جميع الجداول والأعمدة موجودة ولا تحتاج إصلاح</p>";
}

if (!empty($errors)) {
    echo "<h3>الأخطاء:</h3>";
    echo "<div style='color: red;'>";
    foreach ($errors as $error) {
        echo "❌ " . $error . "<br>";
    }
    echo "</div>";
}

echo "<br><a href='admin_financial.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار التقارير المالية</a>";

$db->close();
?>
