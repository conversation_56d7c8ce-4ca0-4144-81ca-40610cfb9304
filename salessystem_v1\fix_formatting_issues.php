<?php
/**
 * سكريپت إصلاح مشاكل التنسيق في جميع صفحات المدير
 */

// قائمة جميع صفحات المدير
$admin_pages = [
    'admin_dashboard.php',
    'admin_users.php',
    'admin_activity.php',
    'admin_reports.php', 
    'admin_financial.php',
    'admin_error_logs.php',
    'admin_system.php',
    'admin_manage_admins.php',
    'admin_user_details.php',
    'admin_invoice_details.php'
];

$fixes_applied = [];
$errors = [];

echo "<h2>🔧 إصلاح مشاكل التنسيق في صفحات المدير</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>المشاكل المكتشفة:</h4>";
echo "<ul>";
echo "<li>❌ ملفات مضغوطة في سطر واحد</li>";
echo "<li>❌ فقدان فواصل الأسطر</li>";
echo "<li>❌ مشاكل في تنسيق HTML</li>";
echo "<li>❌ مشاكل في تنسيق PHP</li>";
echo "</ul>";
echo "</div>";

foreach ($admin_pages as $page) {
    $file_path = __DIR__ . '/' . $page;
    
    if (!file_exists($file_path)) {
        $errors[] = "الملف غير موجود: $page";
        continue;
    }
    
    echo "<h3>🔄 معالجة: $page</h3>";
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $page";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // 1. إصلاح فواصل الأسطر المفقودة
    if (substr_count($content, "\n") < 10) {
        echo "⚠️ ملف مضغوط - إصلاح فواصل الأسطر...<br>";
        
        // إضافة فواصل أسطر بعد العلامات المهمة
        $content = str_replace('<?php', "<?php\n", $content);
        $content = str_replace('?>', "\n?>", $content);
        $content = str_replace(';', ";\n", $content);
        $content = str_replace('{', "{\n", $content);
        $content = str_replace('}', "\n}\n", $content);
        $content = str_replace('>', ">\n", $content);
        $content = str_replace('<', "\n<", $content);
        
        // تنظيف الفواصل الزائدة
        $content = preg_replace('/\n+/', "\n", $content);
        $content = preg_replace('/\n\s*\n/', "\n", $content);
        
        $changes_made = true;
        echo "✅ تم إصلاح فواصل الأسطر<br>";
    }
    
    // 2. إصلاح تنسيق HTML
    $html_fixes = [
        // إصلاح العلامات المفتوحة
        '/<div([^>]*)>([^<\n]*)<div/' => "<div$1>\n$2\n<div",
        '/<\/div>([^<\n]*)<div/' => "</div>\n$1\n<div",
        
        // إصلاح الفقرات
        '/<p([^>]*)>([^<\n]*)<\/p>/' => "<p$1>$2</p>\n",
        
        // إصلاح النماذج
        '/<form([^>]*)>/' => "<form$1>\n",
        '/<\/form>/' => "\n</form>\n",
        
        // إصلاح الجداول
        '/<table([^>]*)>/' => "<table$1>\n",
        '/<\/table>/' => "\n</table>\n",
        '/<tr([^>]*)>/' => "<tr$1>\n",
        '/<\/tr>/' => "\n</tr>\n",
        '/<td([^>]*)>/' => "<td$1>",
        '/<\/td>/' => "</td>\n",
    ];
    
    foreach ($html_fixes as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changes_made = true;
        }
    }
    
    // 3. إصلاح تنسيق PHP
    $php_fixes = [
        // إصلاح الدوال
        '/function\s+([^{]+){/' => "function $1 {\n",
        '/}\s*catch/' => "}\ncatch",
        '/}\s*else/' => "}\nelse",
        '/}\s*elseif/' => "}\nelseif",
        
        // إصلاح الشروط
        '/if\s*\([^)]+\)\s*{/' => "$0\n",
        '/while\s*\([^)]+\)\s*{/' => "$0\n",
        '/for\s*\([^)]+\)\s*{/' => "$0\n",
        '/foreach\s*\([^)]+\)\s*{/' => "$0\n",
        
        // إصلاح المصفوفات
        '/\[\s*\'/' => "[\n    '",
        '/\'\s*,\s*\'/' => "',\n    '",
        '/\'\s*\]/' => "'\n]",
    ];
    
    foreach ($php_fixes as $pattern => $replacement) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $changes_made = true;
        }
    }
    
    // 4. تنظيف المحتوى النهائي
    $content = preg_replace('/\n{3,}/', "\n\n", $content); // إزالة الأسطر الفارغة الزائدة
    $content = preg_replace('/\s+\n/', "\n", $content); // إزالة المسافات في نهاية الأسطر
    $content = trim($content); // إزالة المسافات من البداية والنهاية
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_path = $file_path . '.formatting_backup.' . date('Y-m-d-H-i-s');
        copy($file_path, $backup_path);
        
        // حفظ الملف المحدث
        if (file_put_contents($file_path, $content) !== false) {
            $fixes_applied[] = $page;
            echo "✅ تم حفظ التحديثات بنجاح<br>";
            echo "📁 نسخة احتياطية: " . basename($backup_path) . "<br>";
            
            // إحصائيات التحسين
            $old_lines = substr_count($original_content, "\n");
            $new_lines = substr_count($content, "\n");
            echo "📊 الأسطر: $old_lines → $new_lines<br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $page";
            echo "❌ فشل في حفظ التحديثات<br>";
        }
    } else {
        echo "ℹ️ لا توجد مشاكل تنسيق<br>";
    }
    
    echo "<hr>";
}

// عرض الملخص
echo "<h3>📋 ملخص إصلاح التنسيق:</h3>";

if (!empty($fixes_applied)) {
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>✅ الملفات المُصلحة بنجاح:</h4>";
    foreach ($fixes_applied as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>🎯 الإصلاحات المطبقة:</h3>";
echo "<ul>";
echo "<li>✅ إصلاح فواصل الأسطر المفقودة</li>";
echo "<li>✅ إصلاح تنسيق HTML</li>";
echo "<li>✅ إصلاح تنسيق PHP</li>";
echo "<li>✅ تنظيف المحتوى</li>";
echo "<li>✅ إزالة المسافات الزائدة</li>";
echo "<li>✅ تحسين قابلية القراءة</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h3>⚠️ ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>تم إنشاء نسخ احتياطية لجميع الملفات المُعدلة</li>";
echo "<li>يُنصح بفحص الملفات للتأكد من عملها بشكل صحيح</li>";
echo "<li>في حالة وجود مشاكل، يمكن استعادة النسخ الاحتياطية</li>";
echo "</ul>";
echo "</div>";

echo "<br><div style='text-align: center;'>";
echo "<a href='admin_dashboard.php' style='background: #667eea; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 5px;'>🏠 لوحة التحكم</a>";
echo "<a href='admin_users.php' style='background: #4facfe; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 5px;'>👥 إدارة المستخدمين</a>";
echo "<a href='admin_activity.php' style='background: #43e97b; color: white; padding: 12px 25px; text-decoration: none; border-radius: 8px; margin: 5px;'>📊 سجل العمليات</a>";
echo "</div>";
?>
