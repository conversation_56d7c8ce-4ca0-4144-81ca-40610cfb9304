<?php
/**
 * إصلاح جميع الملفات التي تستخدم header.php القديم
 */

echo '<h1>🔧 إصلاح ملفات Header</h1>';

// البحث عن الملفات التي تستخدم header.php
$files_to_check = glob('*.php');
$files_with_header = [];

echo '<h2>فحص الملفات:</h2>';

foreach ($files_to_check as $file) {
    if ($file === 'fix_header_includes.php') continue; // تجاهل هذا الملف
    
    $content = file_get_contents($file);
    
    if (strpos($content, 'includes/header.php') !== false) {
        $files_with_header[] = $file;
        echo "<p style='color: orange;'>⚠️ $file يستخدم header.php القديم</p>";
    } else {
        echo "<p style='color: green;'>✅ $file لا يستخدم header.php القديم</p>";
    }
}

// إصلاح الملفات
if (!empty($files_with_header)) {
    echo '<h2>إصلاح الملفات:</h2>';
    
    foreach ($files_with_header as $file) {
        echo "<h3>إصلاح $file:</h3>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        
        // استبدال header.php بـ header_simple.php
        $content = str_replace('includes/header.php', 'includes/header_simple.php', $content);
        
        // إزالة استدعاءات الدوال غير الموجودة
        $content = preg_replace('/displayMessages\(\);\s*(?:\/\/.*)?/', '// الرسائل يتم عرضها تلقائياً في header_simple.php', $content);
        $content = preg_replace('/getLanguageDir\(\)/', "'rtl'", $content);
        $content = preg_replace('/getCurrentLang\(\)/', "'ar'", $content);
        $content = preg_replace('/getLanguageSwitchUrl\([^)]+\)/', "'#'", $content);
        $content = preg_replace('/__\([^)]+\)/', "'النص'", $content);
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $file</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إصلاح $file</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $file لا يحتاج إصلاح</p>";
        }
    }
}

// إنشاء دوال مساعدة للتوافق
echo '<h2>إنشاء دوال مساعدة:</h2>';

$helper_functions = '<?php
/**
 * دوال مساعدة للتوافق مع النظام القديم
 */

// دوال اللغة
if (!function_exists("getLanguageDir")) {
    function getLanguageDir() {
        return "rtl";
    }
}

if (!function_exists("getCurrentLang")) {
    function getCurrentLang() {
        return "ar";
    }
}

if (!function_exists("getLanguageSwitchUrl")) {
    function getLanguageSwitchUrl($lang) {
        return "#";
    }
}

if (!function_exists("__")) {
    function __($key) {
        // ترجمات بسيطة
        $translations = [
            "language" => "اللغة",
            "home" => "الرئيسية",
            "sales" => "المبيعات",
            "purchases" => "المشتريات",
            "customers" => "العملاء",
            "products" => "المنتجات",
            "reports" => "التقارير",
            "settings" => "الإعدادات",
            "logout" => "تسجيل الخروج",
            "profile" => "الملف الشخصي"
        ];
        
        return isset($translations[$key]) ? $translations[$key] : $key;
    }
}

// دوال عرض الرسائل
if (!function_exists("displayMessages")) {
    function displayMessages() {
        // لا حاجة لها - الرسائل تُعرض في header_simple.php
    }
}

// دوال الفواتير
if (!function_exists("getInvoiceSetting")) {
    function getInvoiceSetting($key, $default = "") {
        $settings = [
            "company_name" => "نظام إدارة المبيعات",
            "company_logo" => "",
            "system_version" => "2.0"
        ];
        
        return isset($settings[$key]) ? $settings[$key] : $default;
    }
}
?>';

if (file_put_contents('includes/helper_functions.php', $helper_functions)) {
    echo '<p style="color: green;">✅ تم إنشاء includes/helper_functions.php</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء includes/helper_functions.php</p>';
}

// تحديث header_simple.php لتضمين الدوال المساعدة
echo '<h2>تحديث header_simple.php:</h2>';

$header_content = file_get_contents('includes/header_simple.php');
if (strpos($header_content, 'helper_functions.php') === false) {
    $updated_header = str_replace(
        '<?php',
        '<?php
require_once __DIR__ . "/helper_functions.php";',
        $header_content
    );
    
    if (file_put_contents('includes/header_simple.php', $updated_header)) {
        echo '<p style="color: green;">✅ تم تحديث header_simple.php</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في تحديث header_simple.php</p>';
    }
} else {
    echo '<p style="color: orange;">⚠️ header_simple.php محدث مسبقاً</p>';
}

// اختبار الملفات المُصلحة
echo '<h2>اختبار الملفات المُصلحة:</h2>';

foreach ($files_with_header as $file) {
    echo "<p><a href='$file' target='_blank' style='color: blue;'>🔗 اختبار $file</a></p>";
}

echo '<div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h2>✅ تم إصلاح ملفات Header!</h2>';
echo '<h3>الإصلاحات المطبقة:</h3>';
echo '<ul>';
echo '<li>استبدال includes/header.php بـ includes/header_simple.php</li>';
echo '<li>إزالة استدعاءات الدوال غير الموجودة</li>';
echo '<li>إنشاء دوال مساعدة للتوافق</li>';
echo '<li>تحديث header_simple.php لتضمين الدوال المساعدة</li>';
echo '</ul>';
echo '<h3>الملفات المُصلحة:</h3>';
echo '<ul>';
foreach ($files_with_header as $file) {
    echo "<li>$file</li>";
}
echo '</ul>';
echo '<h3>الملفات المنشأة:</h3>';
echo '<ul>';
echo '<li>includes/header_simple.php - رأس صفحة مبسط</li>';
echo '<li>includes/helper_functions.php - دوال مساعدة للتوافق</li>';
echo '</ul>';
echo '<h3>الخطوات التالية:</h3>';
echo '<ol>';
echo '<li><a href="index.php">اختبار index.php</a></li>';
echo '<li><a href="work_management_login.php">اختبار تسجيل الدخول</a></li>';
echo '<li><a href="supervisor_dashboard.php">اختبار لوحة المشرفين</a></li>';
echo '</ol>';
echo '</div>';
?>
