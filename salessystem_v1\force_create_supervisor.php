<?php
/**
 * إجبار إنشاء المشرف التجريبي
 */
require_once 'config/simple_db_config.php';

echo '<h2>إجبار إنشاء المشرف التجريبي</h2>';

// الحصول على اتصال قاعدة البيانات
$db = getSimpleDB();
if (!$db) {
    echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
    exit;
}

echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات بنجاح</p>';

// إنشاء الجداول أولاً
echo '<h3>إنشاء الجداول:</h3>';
if (createUnifiedTables($db)) {
    echo '<p style="color: green;">✅ تم إنشاء/التحقق من الجداول بنجاح</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء الجداول</p>';
    exit;
}

// حذف المشرف الموجود إن وجد (لإعادة الإنشاء)
echo '<h3>حذف المشرف الموجود (إن وجد):</h3>';
$delete_result = $db->query("DELETE FROM supervisors WHERE username = 'supervisor'");
if ($delete_result) {
    echo '<p style="color: orange;">⚠️ تم حذف المشرف الموجود (إن وجد)</p>';
} else {
    echo '<p style="color: red;">❌ فشل في حذف المشرف الموجود: ' . $db->error . '</p>';
}

// إنشاء المشرف التجريبي مباشرة
echo '<h3>إنشاء المشرف التجريبي:</h3>';

$username = 'supervisor';
$email = '<EMAIL>';
$phone = '0987654321';
$password = password_hash('supervisor123', PASSWORD_DEFAULT);
$full_name = 'مشرف تجريبي';
$department = 'الإشراف العام';
$status = 'active';
$permissions = 'supervisor_permissions';

echo '<p>بيانات المشرف التجريبي:</p>';
echo '<ul>';
echo '<li>اسم المستخدم: ' . htmlspecialchars($username) . '</li>';
echo '<li>كلمة المرور: supervisor123</li>';
echo '<li>البريد الإلكتروني: ' . htmlspecialchars($email) . '</li>';
echo '<li>الهاتف: ' . htmlspecialchars($phone) . '</li>';
echo '<li>الاسم الكامل: ' . htmlspecialchars($full_name) . '</li>';
echo '<li>القسم: ' . htmlspecialchars($department) . '</li>';
echo '<li>الحالة: ' . htmlspecialchars($status) . '</li>';
echo '<li>الصلاحيات: ' . htmlspecialchars($permissions) . '</li>';
echo '</ul>';

$insert_sql = "INSERT INTO supervisors (username, email, phone, password, full_name, department, permissions, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

$stmt = $db->prepare($insert_sql);
if ($stmt) {
    $stmt->bind_param('ssssssss', $username, $email, $phone, $password, $full_name, $department, $permissions, $status);
    
    if ($stmt->execute()) {
        $supervisor_id = $db->insert_id;
        echo '<p style="color: green;">✅ تم إنشاء المشرف التجريبي بنجاح!</p>';
        echo '<p><strong>ID المشرف:</strong> ' . $supervisor_id . '</p>';
        
        // التحقق من الإنشاء
        $verify_sql = "SELECT * FROM supervisors WHERE id = ?";
        $verify_stmt = $db->prepare($verify_sql);
        $verify_stmt->bind_param('i', $supervisor_id);
        $verify_stmt->execute();
        $verify_result = $verify_stmt->get_result();
        
        if ($verify_result && $verify_result->num_rows > 0) {
            $supervisor = $verify_result->fetch_assoc();
            echo '<h4>تفاصيل المشرف المُنشأ:</h4>';
            echo '<table border="1" style="border-collapse: collapse;">';
            foreach ($supervisor as $key => $value) {
                if ($key !== 'password') { // لا نعرض كلمة المرور المشفرة
                    echo '<tr><td><strong>' . htmlspecialchars($key) . '</strong></td><td>' . htmlspecialchars($value) . '</td></tr>';
                }
            }
            echo '</table>';
        }
        
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء المشرف التجريبي: ' . htmlspecialchars($stmt->error) . '</p>';
    }
    $stmt->close();
} else {
    echo '<p style="color: red;">❌ فشل في تحضير الاستعلام: ' . htmlspecialchars($db->error) . '</p>';
}

// اختبار دالة createDefaultSupervisor
echo '<h3>اختبار دالة createDefaultSupervisor:</h3>';
if (function_exists('createDefaultSupervisor')) {
    echo '<p style="color: green;">✅ دالة createDefaultSupervisor موجودة</p>';
    
    // حذف المشرف مرة أخرى لاختبار الدالة
    $db->query("DELETE FROM supervisors WHERE username = 'supervisor'");
    
    if (createDefaultSupervisor($db)) {
        echo '<p style="color: green;">✅ دالة createDefaultSupervisor تعمل بنجاح</p>';
    } else {
        echo '<p style="color: red;">❌ دالة createDefaultSupervisor فشلت</p>';
    }
} else {
    echo '<p style="color: red;">❌ دالة createDefaultSupervisor غير موجودة</p>';
}

// عرض جميع المشرفين النهائي
echo '<h3>جميع المشرفين في النظام:</h3>';
$all_supervisors = $db->query("SELECT id, username, full_name, email, department, status, created_at FROM supervisors ORDER BY created_at DESC");
if ($all_supervisors && $all_supervisors->num_rows > 0) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>القسم</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>';
    while ($supervisor = $all_supervisors->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($supervisor['id']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['username']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['full_name']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['email']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['department']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['status']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['created_at']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '<p style="color: green;">✅ تم العثور على ' . $all_supervisors->num_rows . ' مشرف(ين)</p>';
} else {
    echo '<p style="color: red;">❌ لا توجد مشرفين في النظام</p>';
}

echo '<h3>اختبار تسجيل الدخول:</h3>';
echo '<p><a href="work_management_login.php" style="background: #28a745; color: white; padding: 15px 25px; text-decoration: none; border-radius: 5px; font-size: 16px;">🚀 اختبار تسجيل الدخول</a></p>';
echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">';
echo '<h4>بيانات تسجيل الدخول:</h4>';
echo '<p><strong>اسم المستخدم:</strong> supervisor</p>';
echo '<p><strong>كلمة المرور:</strong> supervisor123</p>';
echo '</div>';
?>
