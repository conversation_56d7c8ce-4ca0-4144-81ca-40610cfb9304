<?php
/**
 * فحص محسن للنظام - يعمل بشكل صحيح في بيئة Windows
 */

// تعطيل عرض الأخطاء لتجنب التداخل مع التقرير
error_reporting(E_ALL);
ini_set('display_errors', 0);

echo "<h1>🔍 فحص محسن للنظام</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: #f9f9f9; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #e9ecef; }
    .summary { background: #e7f3ff; border: 2px solid #0066cc; padding: 20px; margin: 20px 0; border-radius: 10px; }
</style>";

$errors = [];
$warnings = [];
$successes = [];

// 1. فحص تحميل الملفات الأساسية
echo "<div class='section'>";
echo "<h2>1. فحص تحميل الملفات الأساسية</h2>";

$core_files = [
    'config/init.php' => 'ملف التهيئة الرئيسي',
    'config/simple_db_config.php' => 'ملف قاعدة البيانات الموحدة',
    'includes/functions.php' => 'ملف الدوال العامة',
    'includes/error_handler.php' => 'ملف معالج الأخطاء'
];

foreach ($core_files as $file => $description) {
    if (file_exists($file) && is_readable($file) && filesize($file) > 0) {
        echo "<p class='success'>✅ $description: موجود وقابل للقراءة</p>";
        $successes[] = $description;
        
        // محاولة تحميل الملف للتحقق من عدم وجود أخطاء فادحة
        try {
            ob_start();
            $old_error_reporting = error_reporting(0);
            
            // فحص محتوى الملف للتأكد من أنه ملف PHP صحيح
            $content = file_get_contents($file);
            if (strpos($content, '<?php') !== false) {
                echo "<p class='success'>✅ $description: ملف PHP صحيح</p>";
            } else {
                echo "<p class='warning'>⚠️ $description: قد لا يكون ملف PHP صحيح</p>";
                $warnings[] = "$description قد لا يكون ملف PHP صحيح";
            }
            
            error_reporting($old_error_reporting);
            ob_end_clean();
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ $description: خطأ في فحص الملف</p>";
            $errors[] = "خطأ في فحص $file: " . $e->getMessage();
        }
    } else {
        echo "<p class='error'>❌ $description: غير موجود أو غير قابل للقراءة</p>";
        $errors[] = "ملف مفقود أو غير قابل للقراءة: $file";
    }
}
echo "</div>";

// 2. فحص قاعدة البيانات والدوال
echo "<div class='section'>";
echo "<h2>2. فحص قاعدة البيانات والدوال</h2>";

try {
    // تحميل ملفات النظام
    require_once 'config/init.php';
    echo "<p class='success'>✅ تم تحميل ملف init.php بنجاح</p>";
    $successes[] = "تحميل ملف init.php";
    
    // فحص الاتصال بقاعدة البيانات
    $db = getSimpleDB();
    if ($db && !$db->connect_error) {
        echo "<p class='success'>✅ الاتصال بقاعدة البيانات: نجح</p>";
        $successes[] = "الاتصال بقاعدة البيانات";
        
        // فحص الجداول
        $required_tables = [
            'users' => 'جدول المستخدمين',
            'admins' => 'جدول المديرين',
            'customers' => 'جدول العملاء',
            'products' => 'جدول المنتجات',
            'system_settings' => 'جدول إعدادات النظام'
        ];
        
        echo "<h3>الجداول الأساسية:</h3>";
        foreach ($required_tables as $table => $description) {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                echo "<p class='success'>✅ $description: موجود ($count سجل)</p>";
                $successes[] = "$description موجود";
            } else {
                echo "<p class='error'>❌ $description: غير موجود</p>";
                $errors[] = "جدول مفقود: $table";
            }
        }
        
    } else {
        echo "<p class='error'>❌ الاتصال بقاعدة البيانات: فشل</p>";
        $errors[] = "فشل في الاتصال بقاعدة البيانات";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في تحميل النظام: " . $e->getMessage() . "</p>";
    $errors[] = "خطأ في تحميل النظام: " . $e->getMessage();
}

echo "</div>";

// 3. فحص الدوال المطلوبة
echo "<div class='section'>";
echo "<h2>3. فحص الدوال المطلوبة</h2>";

$required_functions = [
    'getUnifiedDB' => 'دالة الاتصال بقاعدة البيانات',
    'isAdminLoggedIn' => 'دالة فحص تسجيل دخول المدير',
    'hasAdminPermission' => 'دالة فحص صلاحيات المدير',
    'getCurrentAdmin' => 'دالة الحصول على المدير الحالي',
    'getSystemSettings' => 'دالة جلب إعدادات النظام',
    'updateSystemSetting' => 'دالة تحديث إعدادات النظام',
    'getSystemSetting' => 'دالة الحصول على إعداد محدد',
    'generateInvoiceNumber' => 'دالة إنشاء رقم فاتورة',
    'displayMessages' => 'دالة عرض الرسائل',
    'createAppTables' => 'دالة إنشاء الجداول',
    'ensureSuperAdminExists' => 'دالة التأكد من وجود المدير الرئيسي'
];

foreach ($required_functions as $func => $description) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ $description: موجودة</p>";
        $successes[] = $description;
    } else {
        echo "<p class='error'>❌ $description: غير موجودة</p>";
        $errors[] = "دالة مفقودة: $func";
    }
}

echo "</div>";

// 4. فحص الصفحات الرئيسية
echo "<div class='section'>";
echo "<h2>4. فحص الصفحات الرئيسية</h2>";

$main_pages = [
    'index.php' => 'الصفحة الرئيسية',
    'login.php' => 'صفحة تسجيل الدخول',
    'admin_login.php' => 'صفحة تسجيل دخول المدير',
    'admin_dashboard.php' => 'لوحة تحكم المدير',
    'admin_system.php' => 'صفحة إعدادات النظام',
    'admin_manage_admins.php' => 'صفحة إدارة المديرين'
];

foreach ($main_pages as $page => $description) {
    if (file_exists($page) && is_readable($page) && filesize($page) > 0) {
        echo "<p class='success'>✅ $description: موجودة وقابلة للقراءة</p>";
        $successes[] = $description;
    } else {
        echo "<p class='warning'>⚠️ $description: غير موجودة أو فارغة</p>";
        $warnings[] = "صفحة مفقودة أو فارغة: $page";
    }
}

echo "</div>";

// 5. فحص المجلدات المطلوبة
echo "<div class='section'>";
echo "<h2>5. فحص المجلدات المطلوبة</h2>";

$required_directories = [
    'config' => 'مجلد الإعدادات',
    'includes' => 'مجلد الملفات المضمنة',
    'logs' => 'مجلد السجلات',
    'uploads' => 'مجلد الرفع',
    'backups' => 'مجلد النسخ الاحتياطية'
];

foreach ($required_directories as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        $class = is_writable($dir) ? 'success' : 'warning';
        echo "<p class='$class'>✅ $description: موجود ($writable)</p>";
        
        if (is_writable($dir)) {
            $successes[] = "$description موجود وقابل للكتابة";
        } else {
            $warnings[] = "$description غير قابل للكتابة";
        }
    } else {
        echo "<p class='error'>❌ $description: غير موجود</p>";
        $errors[] = "مجلد مفقود: $dir";
    }
}

echo "</div>";

// 6. ملخص شامل
echo "<div class='summary'>";
echo "<h2>📊 ملخص شامل للفحص</h2>";

echo "<table>";
echo "<tr><th>النوع</th><th>العدد</th><th>النسبة</th></tr>";

$total_checks = count($successes) + count($warnings) + count($errors);
$success_percentage = $total_checks > 0 ? round((count($successes) / $total_checks) * 100, 1) : 0;
$warning_percentage = $total_checks > 0 ? round((count($warnings) / $total_checks) * 100, 1) : 0;
$error_percentage = $total_checks > 0 ? round((count($errors) / $total_checks) * 100, 1) : 0;

echo "<tr><td class='success'>✅ نجح</td><td>" . count($successes) . "</td><td>$success_percentage%</td></tr>";
echo "<tr><td class='warning'>⚠️ تحذيرات</td><td>" . count($warnings) . "</td><td>$warning_percentage%</td></tr>";
echo "<tr><td class='error'>❌ أخطاء</td><td>" . count($errors) . "</td><td>$error_percentage%</td></tr>";
echo "<tr><td><strong>المجموع</strong></td><td><strong>$total_checks</strong></td><td><strong>100%</strong></td></tr>";
echo "</table>";

// تقييم حالة النظام
if (count($errors) == 0 && count($warnings) == 0) {
    echo "<h3 class='success'>🎉 النظام يعمل بشكل مثالي!</h3>";
    echo "<p class='success'>جميع المكونات تعمل بشكل صحيح ولا توجد مشاكل.</p>";
} elseif (count($errors) == 0) {
    echo "<h3 class='info'>✅ النظام يعمل بشكل جيد</h3>";
    echo "<p class='info'>توجد بعض التحذيرات البسيطة التي لا تؤثر على عمل النظام.</p>";
} elseif (count($errors) <= 3) {
    echo "<h3 class='warning'>⚠️ النظام يحتاج إلى إصلاحات بسيطة</h3>";
    echo "<p class='warning'>توجد بعض المشاكل البسيطة التي يمكن إصلاحها بسهولة.</p>";
} else {
    echo "<h3 class='error'>🚨 النظام يحتاج إلى إصلاحات</h3>";
    echo "<p class='error'>توجد مشاكل تحتاج إلى إصلاح فوري.</p>";
}

echo "</div>";

// 7. أدوات الإصلاح
echo "<div class='section'>";
echo "<h2>7. أدوات الإصلاح والاختبار</h2>";

echo "<h3>🛠️ أدوات الإصلاح:</h3>";
echo "<ul>";
echo "<li><a href='auto_fix_all_issues.php'>الإصلاح التلقائي الشامل</a></li>";
echo "<li><a href='fix_database_issues.php'>إصلاح مشاكل قاعدة البيانات</a></li>";
echo "<li><a href='fix_admin_pages.php'>إصلاح صفحات المدير</a></li>";
echo "</ul>";

echo "<h3>🧪 أدوات الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='test_functions.php'>اختبار الدوال</a></li>";
echo "<li><a href='test_system.php'>اختبار النظام الشامل</a></li>";
echo "</ul>";

echo "<h3>🌐 الوصول للنظام:</h3>";
echo "<ul>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='admin_login.php'>تسجيل دخول المدير</a></li>";
echo "<li><a href='admin_dashboard.php'>لوحة تحكم المدير</a></li>";
echo "<li><a href='admin_system.php'>إعدادات النظام</a></li>";
echo "</ul>";

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "تم إجراء الفحص في: " . date('Y-m-d H:i:s') . " | ";
echo "الذاكرة المستخدمة: " . number_format(memory_get_usage(true) / 1024 / 1024, 2) . " MB | ";
echo "إجمالي الفحوصات: $total_checks";
echo "</p>";

?>
