<?php
/**
 * ErrorHandler بسيط للتوافق
 */

class ErrorHandler {
    
    /**
     * تسجيل خطأ عام
     */
    public static function logError($level, $message, $file = "", $line = 0) {
        $log_message = "[$level] $message";
        if ($file) {
            $log_message .= " in $file";
        }
        if ($line) {
            $log_message .= " on line $line";
        }
        error_log($log_message);
    }
    
    /**
     * تسجيل خطأ قاعدة البيانات
     */
    public static function logDatabaseError($query, $error) {
        error_log("Database Error: $error | Query: $query");
    }
    
    /**
     * جلب سجلات الأخطاء (مبسط)
     */
    public static function getLogs($date = null, $level = null, $limit = 100) {
        // إرجاع مصفوفة فارغة للتوافق
        return [];
    }
    
    /**
     * تسجيل نشاط المستخدم
     */
    public static function logUserActivity($user_id, $action, $details = "") {
        error_log("User Activity: User $user_id performed $action. Details: $details");
    }
}
?>