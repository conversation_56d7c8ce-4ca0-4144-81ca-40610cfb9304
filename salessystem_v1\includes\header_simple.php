<?php
require_once __DIR__ . "/helper_functions.php";
/**
 * رأس الصفحة المبسط للنظام
 */

// إعدادات افتراضية
$lang_dir = 'rtl';
$lang_code = 'ar';
$company_name = 'نظام إدارة المبيعات';
$company_logo = '';
$system_version = '2.0';
?>
<!DOCTYPE html>
<html lang="<?php
require_once __DIR__ . "/helper_functions.php"; echo $lang_code; ?>" dir="<?php
require_once __DIR__ . "/helper_functions.php"; echo $lang_dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php
require_once __DIR__ . "/helper_functions.php"; echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php
require_once __DIR__ . "/helper_functions.php"; echo $company_name; ?></title>
    
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" href="assets/img/favicon.ico" type="image/x-icon">

    <style>
        /* Font settings */
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        
        /* Navbar styles */
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .navbar-nav .nav-link {
            font-weight: 500;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
        }
        
        /* Card styles */
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        /* Button styles */
        .btn {
            font-weight: 500;
            border-radius: 0.375rem;
        }
        
        /* Table styles */
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }
        
        /* Alert styles */
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        /* Main content */
        .main-content {
            padding: 2rem 0;
            min-height: calc(100vh - 200px);
        }
        
        /* Footer */
        .footer {
            background-color: #343a40;
            color: #fff;
            padding: 1rem 0;
            margin-top: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <?php
require_once __DIR__ . "/helper_functions.php"; if ($company_logo): ?>
                    <img src="<?php
require_once __DIR__ . "/helper_functions.php"; echo $company_logo; ?>" alt="<?php
require_once __DIR__ . "/helper_functions.php"; echo $company_name; ?>" height="30" class="me-2">
                <?php
require_once __DIR__ . "/helper_functions.php"; endif; ?>
                <?php
require_once __DIR__ . "/helper_functions.php"; echo $company_name; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sales.php">
                            <i class="fas fa-shopping-cart me-1"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="purchases.php">
                            <i class="fas fa-shopping-bag me-1"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="customers.php">
                            <i class="fas fa-users me-1"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-1"></i> المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar me-1"></i> التقارير
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i> 
                            <?php
require_once __DIR__ . "/helper_functions.php"; echo isset($_SESSION['user_name']) ? $_SESSION['user_name'] : 'المستخدم'; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i> الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog me-2"></i> الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <?php
require_once __DIR__ . "/helper_functions.php"; if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php
require_once __DIR__ . "/helper_functions.php"; echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php
require_once __DIR__ . "/helper_functions.php"; endif; ?>
            
            <?php
require_once __DIR__ . "/helper_functions.php"; if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php
require_once __DIR__ . "/helper_functions.php"; echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php
require_once __DIR__ . "/helper_functions.php"; endif; ?>
            
            <?php
require_once __DIR__ . "/helper_functions.php"; if (isset($_SESSION['warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php
require_once __DIR__ . "/helper_functions.php"; echo $_SESSION['warning']; unset($_SESSION['warning']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php
require_once __DIR__ . "/helper_functions.php"; endif; ?>
            
            <?php
require_once __DIR__ . "/helper_functions.php"; if (isset($_SESSION['info'])): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php
require_once __DIR__ . "/helper_functions.php"; echo $_SESSION['info']; unset($_SESSION['info']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php
require_once __DIR__ . "/helper_functions.php"; endif; ?>
