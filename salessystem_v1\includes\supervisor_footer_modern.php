        </main>
    </div>

    <!-- الزر العائم للإجراءات السريعة -->
    <button class="floating-btn" onclick="toggleQuickActions()" title="الإجراءات السريعة">
        <i class="fas fa-plus"></i>
    </button>

    <!-- قائمة الإجراءات السريعة -->
    <div id="quickActionsMenu" class="position-fixed" style="bottom: 6rem; left: 2rem; z-index: 999; display: none;">
        <div class="d-flex flex-column gap-2">
            <a href="supervisor_reports.php" class="modern-btn modern-btn-primary">
                <i class="fas fa-chart-bar"></i>
                <span>تقرير سريع</span>
            </a>
            <a href="supervisor_financial.php" class="modern-btn modern-btn-secondary">
                <i class="fas fa-file-invoice-dollar"></i>
                <span>التقارير المالية</span>
            </a>
            <a href="supervisor_activity.php" class="modern-btn modern-btn-outline">
                <i class="fas fa-history"></i>
                <span>سجل العمليات</span>
            </a>
        </div>
    </div>

    <!-- تذييل الصفحة -->
    <footer class="mt-5 py-4" style="background: var(--bg-white); border-top: 1px solid var(--border-color);">
        <div class="container-fluid px-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        <i class="fas fa-copyright me-1"></i>
                        <?php echo date('Y'); ?> نظام إدارة المبيعات - قسم المشرفين
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        مرحباً <?php echo htmlspecialchars($supervisor_name); ?>
                        <span class="mx-2">|</span>
                        <i class="fas fa-clock me-1"></i>
                        <?php echo date('Y-m-d H:i'); ?>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js للرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- JavaScript مخصص -->
    <script>
        // تبديل قائمة الإجراءات السريعة
        function toggleQuickActions() {
            const menu = document.getElementById('quickActionsMenu');
            const btn = document.querySelector('.floating-btn i');
            
            if (menu.style.display === 'none' || menu.style.display === '') {
                menu.style.display = 'block';
                btn.style.transform = 'rotate(45deg)';
                menu.style.animation = 'fadeInUp 0.3s ease';
            } else {
                menu.style.display = 'none';
                btn.style.transform = 'rotate(0deg)';
            }
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('quickActionsMenu');
            const btn = document.querySelector('.floating-btn');
            
            if (!menu.contains(event.target) && !btn.contains(event.target)) {
                menu.style.display = 'none';
                document.querySelector('.floating-btn i').style.transform = 'rotate(0deg)';
            }
        });

        // تحديث الوقت كل دقيقة
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            const timeElement = document.querySelector('footer .fas.fa-clock').nextSibling;
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // تحديث الوقت كل دقيقة
        setInterval(updateTime, 60000);

        // تأثيرات التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير ظهور البطاقات
            const cards = document.querySelectorAll('.stats-card, .modern-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // تأثير hover للجداول
            const tableRows = document.querySelectorAll('.modern-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(4px)';
                    this.style.transition = 'transform 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });

            // تحسين الأزرار
            const buttons = document.querySelectorAll('.modern-btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // تأثير الموجة
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // دالة لإظهار رسائل النجاح
        function showSuccessMessage(message) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح',
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }

        // دالة لإظهار رسائل الخطأ
        function showErrorMessage(message) {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: message,
                confirmButtonText: 'حسناً'
            });
        }

        // دالة لتأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return Swal.fire({
                title: 'تأكيد الحذف',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            });
        }
    </script>

    <style>
        /* تأثيرات CSS إضافية */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* تحسينات للجوال */
        @media (max-width: 768px) {
            .floating-btn {
                bottom: 1rem;
                left: 1rem;
                width: 3rem;
                height: 3rem;
            }
            
            #quickActionsMenu {
                bottom: 5rem !important;
                left: 1rem !important;
            }
            
            .modern-btn {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }
        }
    </style>
</body>
</html>
