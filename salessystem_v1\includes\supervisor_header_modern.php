<?php
/**
 * رأس الصفحة العصري لقسم المشرفين
 */

// التحقق من تسجيل دخول المشرف
if (!isset($_SESSION['supervisor_logged_in']) || !$_SESSION['supervisor_logged_in']) {
    header("Location: work_management_login.php");
    exit();
}

$supervisor_name = $_SESSION['supervisor_name'] ?? 'مشرف';
$supervisor_username = $_SESSION['supervisor_username'] ?? '';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>لوحة تحكم المشرفين</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- التصميم العصري للمشرفين -->
    <link href="assets/css/supervisor_modern.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        /* تحسينات إضافية */
        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
        }
        
        .navbar-nav .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--radius-sm);
            margin: 0 0.25rem;
        }
        
        .navbar-nav .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: var(--shadow-lg);
            border-radius: var(--radius-md);
            padding: 0.5rem;
        }
        
        .dropdown-item {
            border-radius: var(--radius-sm);
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }
        
        .dropdown-item:hover {
            background: var(--primary-color);
            color: white;
            transform: translateX(4px);
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); box-shadow: var(--shadow-md);">
        <div class="container-fluid px-4">
            <!-- العلامة التجارية -->
            <a class="navbar-brand d-flex align-items-center" href="supervisor_dashboard.php">
                <i class="fas fa-user-tie me-2" style="color: var(--primary-light);"></i>
                لوحة تحكم المشرفين
            </a>
            
            <!-- زر القائمة للجوال -->
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- روابط التنقل -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="supervisor_dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="supervisor_reports.php">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="supervisor_financial.php">
                            <i class="fas fa-file-invoice-dollar me-1"></i>
                            التقارير المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="supervisor_activity.php">
                            <i class="fas fa-history me-1"></i>
                            سجل العمليات
                        </a>
                    </li>
                </ul>
                
                <!-- قائمة المستخدم -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar">
                                <?php echo mb_substr($supervisor_name, 0, 1, 'UTF-8'); ?>
                            </div>
                            <span class="d-none d-md-inline"><?php echo htmlspecialchars($supervisor_name); ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="settings.php">
                                    <i class="fas fa-cog me-2"></i>
                                    الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="work_management_login.php">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    تبديل القسم
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item text-danger" href="supervisor_logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="container-fluid px-4 mt-3">
            <div class="modern-alert modern-alert-success">
                <i class="fas fa-check-circle"></i>
                <span><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.remove()"></button>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error'])): ?>
        <div class="container-fluid px-4 mt-3">
            <div class="modern-alert modern-alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <span><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.remove()"></button>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['warning'])): ?>
        <div class="container-fluid px-4 mt-3">
            <div class="modern-alert modern-alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <span><?php echo $_SESSION['warning']; unset($_SESSION['warning']); ?></span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.remove()"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- بداية المحتوى -->
    <div class="supervisor-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <i class="fas fa-user-tie"></i>
                    <span>قسم المشرفين</span>
                </div>
            </div>
            
            <div class="sidebar-nav">
                <div class="sidebar-section">
                    <div class="sidebar-section-title">الإدارة الرئيسية</div>
                    <a class="sidebar-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'supervisor_dashboard.php' ? 'active' : ''; ?>" href="supervisor_dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                    <a class="sidebar-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'supervisor_activity.php' ? 'active' : ''; ?>" href="supervisor_activity.php">
                        <i class="fas fa-history"></i>
                        <span>سجل العمليات</span>
                    </a>
                </div>
                
                <div class="sidebar-section">
                    <div class="sidebar-section-title">التقارير والإحصائيات</div>
                    <a class="sidebar-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'supervisor_reports.php' ? 'active' : ''; ?>" href="supervisor_reports.php">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير الشاملة</span>
                    </a>
                    <a class="sidebar-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'supervisor_financial.php' ? 'active' : ''; ?>" href="supervisor_financial.php">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>التقارير المالية</span>
                    </a>
                </div>
                
                <div class="sidebar-section">
                    <div class="sidebar-section-title">الإعدادات</div>
                    <a class="sidebar-nav-link" href="profile.php">
                        <i class="fas fa-user"></i>
                        <span>الملف الشخصي</span>
                    </a>
                    <a class="sidebar-nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="supervisor-content">
            <!-- سيتم إدراج محتوى الصفحة هنا -->
