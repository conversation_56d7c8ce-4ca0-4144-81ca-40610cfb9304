<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()

// فحص تسجيل الدخول قبل إرسال أي محتوى
if (isLoggedIn()) {
    header("Location: index.php");
    exit();
}

require_once __DIR__.'/includes/header_simple.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header"><?php echo 'النص'; ?></div>
            <div class="card-body">
                <?php // الرسائل يتم عرضها تلقائياً في header_simple.php?>
                <form method="POST" action="includes/auth.php">
                    <div class="mb-3">
                        <label for="username" class="form-label"><?php echo 'النص'; ?></label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label"><?php echo 'النص'; ?></label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" name="login" class="btn btn-primary"><?php echo 'النص'; ?></button>
                </form>
                <div class="mt-3 text-center">
                    <a href="forgot_password.php" class="text-decoration-none text-muted">
                        <i class="fas fa-key me-1"></i>نسيت كلمة المرور؟
                    </a>
                </div>
                <div class="mt-2">
                    <?php echo 'النص'; ?> <a href="register.php"><?php echo 'النص'; ?></a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>