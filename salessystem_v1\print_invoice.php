<?php
require_once 'config/init.php';
require_once 'includes/invoice_functions.php';
redirectIfNotLoggedIn();

// جلب معلومات الشركة من الإعدادات
$company_name = getInvoiceSetting('company_name', 'نظام المبيعات');
$company_name_en = getInvoiceSetting('company_name_en', 'Sales System');
$company_address = getInvoiceSetting('company_address', 'المملكة العربية السعودية، الرياض');
$company_phone = getInvoiceSetting('company_phone', '+966 11 123 4567');
$company_email = getInvoiceSetting('company_email', '<EMAIL>');
$company_website = getInvoiceSetting('company_website');
$company_tax_number = getInvoiceSetting('company_tax_number', '300123456789003');
$company_commercial_register = getInvoiceSetting('company_commercial_register', '1010123456');
$company_logo = getInvoiceSetting('company_logo');
$show_logo = getInvoiceSetting('show_logo_on_invoices', '1') == '1';
$show_company_info = getInvoiceSetting('show_company_info_on_invoices', '1') == '1';
$currency_symbol = getInvoiceSetting('currency_symbol', 'ريال');
$decimal_places = intval(getInvoiceSetting('decimal_places', '2'));
$system_version = getInvoiceSetting('system_version', '2.0');

if (!isset($_GET['id']) || !isset($_GET['type'])) {
    header("Location: index.php");
    exit();
}

$id = intval($_GET['id']);
$type = $_GET['type'];

$db = getCurrentUserDB();

if ($type === 'sale') {
    // جلب بيانات فاتورة المبيعات
    $stmt = $db->prepare("SELECT s.*, c.name AS customer_name, c.address AS customer_address, 
                         c.phone AS customer_phone, c.tax_number AS customer_tax_number
                         FROM sales s
                         LEFT JOIN customers c ON s.customer_id = c.id
                         WHERE s.id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();
    
    if (!$invoice) {
        die("فاتورة المبيعات غير موجودة");
    }
    
    // جلب عناصر الفاتورة
    $items = $db->query("SELECT si.*, p.name AS product_name 
                        FROM sale_items si
                        JOIN products p ON si.product_id = p.id
                        WHERE si.sale_id = $id");
    
    $title = "فاتورة مبيعات";
} elseif ($type === 'purchase') {
    // جلب بيانات فاتورة المشتريات
    $stmt = $db->prepare("SELECT * FROM purchases WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();
    
    if (!$invoice) {
        die("فاتورة المشتريات غير موجودة");
    }
    
    // جلب عناصر الفاتورة
    $items = $db->query("SELECT * FROM purchase_items WHERE purchase_id = $id");
    
    $title = "فاتورة مشتريات";
} else {
    die("نوع الفاتورة غير صحيح");
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?> رقم <?php echo $invoice['invoice_number']; ?> - <?php echo htmlspecialchars($company_name); ?></title>
    <link rel="stylesheet" href="assets/css/print.css">
    <style>
        /* تنسيق خاص للفاتورة */
        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            margin: 0;
            padding: 15px;
            color: #333;
            background: white;
            direction: rtl;
            text-align: right;
        }

        .invoice-box {
            max-width: 800px;
            margin: auto;
            padding: 20px;
            border: 2px solid #000;
            background: white;
        }

        .invoice-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 3px solid #000;
        }

        .invoice-header h1 {
            font-size: 24px;
            margin: 0 0 10px 0;
            color: #000;
            font-weight: bold;
        }

        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #000;
        }

        .company-info {
            text-align: right;
            flex: 1;
            padding-right: 15px;
        }

        .invoice-info {
            text-align: left;
            flex: 1;
            padding-left: 15px;
            border-right: 1px solid #000;
        }

        .company-info h2,
        .invoice-info h2 {
            font-size: 18px;
            margin: 0 0 15px 0;
            color: #000;
            font-weight: bold;
        }

        .company-info p,
        .invoice-info p {
            margin: 5px 0;
            font-size: 12px;
            line-height: 1.4;
        }

        .client-info {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #000;
            background: #f9f9f9;
        }

        .client-info h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #000;
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #000;
            font-size: 11px;
        }

        th {
            background-color: #e0e0e0;
            font-weight: bold;
            color: #000;
        }

        .total-row {
            font-weight: bold;
            background-color: #f0f0f0;
        }

        .notes {
            margin-top: 25px;
            padding: 15px;
            border: 1px solid #000;
            background: #f9f9f9;
        }

        .notes h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #000;
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
        }

        .signatures {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }

        .signature-box {
            width: 30%;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
            font-size: 12px;
        }

        .print-info {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }

        @media print {
            body {
                padding: 0;
                margin: 0;
            }

            .invoice-box {
                border: none;
                padding: 0;
                margin: 0;
                max-width: 100%;
            }

            .no-print {
                display: none !important;
            }

            .print-info {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                border-top: 1px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-box">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <?php if ($show_logo && $company_logo && file_exists(__DIR__ . '/uploads/' . $company_logo)): ?>
                <div style="margin-bottom: 15px;">
                    <img src="uploads/<?php echo htmlspecialchars($company_logo); ?>" alt="شعار الشركة" style="max-height: 80px; width: auto;">
                </div>
            <?php endif; ?>
            <h1><?php echo htmlspecialchars($company_name); ?></h1>
            <?php if ($company_name_en): ?>
                <p style="font-size: 16px; color: #666; margin: 5px 0;"><?php echo htmlspecialchars($company_name_en); ?></p>
            <?php endif; ?>
            <h2 style="color: #e74c3c; margin: 15px 0;"><?php echo $title; ?></h2>
            <p>رقم الفاتورة: <strong><?php echo $invoice['invoice_number']; ?></strong></p>
            <p>التاريخ: <strong><?php echo date('d/m/Y', strtotime($invoice['date'])); ?></strong></p>
        </div>

        <!-- معلومات الشركة والفاتورة -->
        <div class="header">
            <div class="company-info">
                <h2><?php echo htmlspecialchars($company_name); ?></h2>
                <?php if ($show_company_info): ?>
                    <?php if ($company_address): ?>
                        <p><strong>العنوان:</strong> <?php echo htmlspecialchars($company_address); ?></p>
                    <?php endif; ?>
                    <?php if ($company_phone): ?>
                        <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($company_phone); ?></p>
                    <?php endif; ?>
                    <?php if ($company_email): ?>
                        <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($company_email); ?></p>
                    <?php endif; ?>
                    <?php if ($company_website): ?>
                        <p><strong>الموقع الإلكتروني:</strong> <?php echo htmlspecialchars($company_website); ?></p>
                    <?php endif; ?>
                    <?php if ($company_tax_number): ?>
                        <p><strong>الرقم الضريبي:</strong> <?php echo htmlspecialchars($company_tax_number); ?></p>
                    <?php endif; ?>
                    <?php if ($company_commercial_register): ?>
                        <p><strong>السجل التجاري:</strong> <?php echo htmlspecialchars($company_commercial_register); ?></p>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <div class="invoice-info">
                <h2>تفاصيل الفاتورة</h2>
                <p><strong>نوع الفاتورة:</strong> <?php echo $title; ?></p>
                <p><strong>رقم الفاتورة:</strong> <?php echo $invoice['invoice_number']; ?></p>
                <p><strong>تاريخ الإصدار:</strong> <?php echo date('d/m/Y', strtotime($invoice['date'])); ?></p>
                <p><strong>وقت الإصدار:</strong> <?php echo date('H:i:s'); ?></p>
                <p><strong>حالة الدفع:</strong>
                    <?php
                    switch($invoice['payment_status'] ?? 'unpaid') {
                        case 'paid': echo 'مدفوع بالكامل'; break;
                        case 'partial': echo 'مدفوع جزئياً'; break;
                        case 'unpaid': echo 'غير مدفوع'; break;
                        default: echo 'غير محدد'; break;
                    }
                    ?>
                </p>
                <p><strong>طريقة الدفع:</strong>
                    <?php
                    $payment_methods = [
                        'cash' => 'نقدي',
                        'card' => 'بطاقة ائتمان',
                        'bank_transfer' => 'تحويل بنكي',
                        'check' => 'شيك',
                        'installment' => 'تقسيط',
                        'other' => 'أخرى'
                    ];
                    echo $payment_methods[$invoice['payment_method'] ?? 'cash'] ?? 'نقدي';
                    ?>
                </p>
            </div>
        </div>

        <!-- معلومات العميل/المورد -->
        <div class="client-info">
            <?php if ($type === 'sale'): ?>
            <h3>معلومات العميل</h3>
            <div style="display: flex; justify-content: space-between;">
                <div style="flex: 1;">
                    <p><strong>اسم العميل:</strong> <?php echo htmlspecialchars($invoice['customer_name'] ?? 'غير محدد'); ?></p>
                    <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($invoice['customer_phone'] ?? 'غير محدد'); ?></p>
                </div>
                <div style="flex: 1;">
                    <p><strong>الرقم الضريبي:</strong> <?php echo htmlspecialchars($invoice['customer_tax_number'] ?? 'غير محدد'); ?></p>
                    <p><strong>العنوان:</strong> <?php echo htmlspecialchars($invoice['customer_address'] ?? 'غير محدد'); ?></p>
                </div>
            </div>
            <?php else: ?>
            <h3>معلومات المورد</h3>
            <p><strong>اسم المورد:</strong> <?php echo htmlspecialchars($invoice['supplier_name'] ?? 'غير محدد'); ?></p>
            <p><strong>تاريخ التوريد:</strong> <?php echo date('d/m/Y', strtotime($invoice['date'])); ?></p>
            <?php endif; ?>
        </div>

        <!-- معلومات الدفع التفصيلية -->
        <div class="client-info">
            <h3>تفاصيل الدفع</h3>
            <div style="display: flex; justify-content: space-between;">
                <div style="flex: 1;">
                    <p><strong>المبلغ الإجمالي:</strong> <?php echo formatCurrency($invoice['total_amount']); ?></p>
                    <p><strong>المبلغ المدفوع:</strong> <span style="color: green; font-weight: bold;"><?php echo formatCurrency($invoice['paid_amount'] ?? 0); ?></span></p>
                    <p><strong>المبلغ المتبقي:</strong>
                        <span style="color: <?php echo (($invoice['remaining_amount'] ?? 0) > 0) ? 'red' : 'green'; ?>; font-weight: bold;">
                            <?php echo formatCurrency($invoice['remaining_amount'] ?? 0); ?>
                        </span>
                    </p>
                </div>
                <div style="flex: 1;">
                    <?php if (!empty($invoice['payment_date'])): ?>
                    <p><strong>تاريخ الدفع:</strong> <?php echo date('d/m/Y', strtotime($invoice['payment_date'])); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($invoice['payment_reference'])): ?>
                    <p><strong>مرجع الدفع:</strong> <?php echo htmlspecialchars($invoice['payment_reference']); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($invoice['payment_notes'])): ?>
                    <p><strong>ملاحظات الدفع:</strong> <?php echo htmlspecialchars($invoice['payment_notes']); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- جدول تفاصيل الأصناف -->
        <table class="page-break-avoid">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 30%;">اسم المنتج</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 12%;">سعر الوحدة</th>
                    <th style="width: 10%;">نسبة الضريبة</th>
                    <th style="width: 12%;">قيمة الضريبة</th>
                    <th style="width: 12%;">المجموع الفرعي</th>
                    <th style="width: 14%;">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $counter = 1;
                $total_quantity = 0;
                while ($item = $items->fetch_assoc()):
                    $item_subtotal = $item['quantity'] * $item['unit_price'];
                    $total_quantity += $item['quantity'];
                ?>
                <tr>
                    <td><?php echo $counter++; ?></td>
                    <td style="text-align: right;"><?php echo htmlspecialchars($item['product_name'] ?? 'غير محدد'); ?></td>
                    <td class="number"><?php echo number_format($item['quantity'], 0); ?></td>
                    <td class="number"><?php echo number_format($item['unit_price'], 2); ?> ر.س</td>
                    <td class="number"><?php echo number_format($item['tax_rate'] ?? 0, 1); ?>%</td>
                    <td class="number"><?php echo number_format($item['tax_amount'] ?? 0, 2); ?> ر.س</td>
                    <td class="number"><?php echo number_format($item_subtotal, 2); ?> ر.س</td>
                    <td class="number"><?php echo number_format($item['total_price'], 2); ?> ر.س</td>
                </tr>
                <?php endwhile; ?>
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <td colspan="2"><strong>الإجمالي</strong></td>
                    <td class="number"><strong><?php echo number_format($total_quantity, 0); ?></strong></td>
                    <td colspan="3"></td>
                    <td class="number"><strong><?php echo number_format($invoice['subtotal'], 2); ?> ر.س</strong></td>
                    <td class="number"><strong><?php echo number_format($invoice['total_amount'], 2); ?> ر.س</strong></td>
                </tr>
                <tr class="total-row">
                    <td colspan="6"><strong>المجموع الفرعي (قبل الضريبة):</strong></td>
                    <td colspan="2" class="number"><strong><?php echo number_format($invoice['subtotal'], 2); ?> ر.س</strong></td>
                </tr>
                <tr class="total-row">
                    <td colspan="6"><strong>إجمالي ضريبة القيمة المضافة (15%):</strong></td>
                    <td colspan="2" class="number"><strong><?php echo number_format($invoice['tax_amount'], 2); ?> ر.س</strong></td>
                </tr>
                <tr class="total-row" style="background-color: #d0d0d0; font-size: 14px;">
                    <td colspan="6"><strong>المبلغ الإجمالي المستحق:</strong></td>
                    <td colspan="2" class="number"><strong><?php echo number_format($invoice['total_amount'], 2); ?> ر.س</strong></td>
                </tr>
                <tr class="total-row" style="background-color: #e8f5e8; font-size: 12px;">
                    <td colspan="6"><strong>المبلغ المدفوع:</strong></td>
                    <td colspan="2" class="number" style="color: green;"><strong><?php echo number_format($invoice['paid_amount'] ?? 0, 2); ?> ر.س</strong></td>
                </tr>
                <tr class="total-row" style="background-color: <?php echo (($invoice['remaining_amount'] ?? 0) > 0) ? '#ffe8e8' : '#e8f5e8'; ?>; font-size: 12px;">
                    <td colspan="6"><strong>المبلغ المتبقي:</strong></td>
                    <td colspan="2" class="number" style="color: <?php echo (($invoice['remaining_amount'] ?? 0) > 0) ? 'red' : 'green'; ?>;"><strong><?php echo number_format($invoice['remaining_amount'] ?? 0, 2); ?> ر.س</strong></td>
                </tr>
            </tfoot>
        </table>

        <!-- الملاحظات -->
        <?php if (!empty($invoice['notes'])): ?>
        <div class="notes page-break-avoid">
            <h4>ملاحظات إضافية</h4>
            <p><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
        </div>
        <?php endif; ?>

        <!-- شروط وأحكام -->
        <div class="notes page-break-avoid">
            <h4>الشروط والأحكام</h4>
            <ul style="margin: 0; padding-right: 20px; font-size: 10px;">
                <li>جميع الأسعار شاملة ضريبة القيمة المضافة</li>
                <li>يرجى مراجعة الفاتورة والتأكد من صحة البيانات</li>
                <li>في حالة وجود أي استفسار يرجى التواصل معنا خلال 7 أيام</li>
                <li>هذه الفاتورة صادرة إلكترونياً ولا تحتاج إلى توقيع</li>
            </ul>
        </div>

        <!-- التوقيعات -->
        <div class="signatures page-break-avoid">
            <div class="signature-box">
                <p><strong>المحاسب</strong></p>
                <p>الاسم: _______________</p>
                <p>التوقيع: _______________</p>
                <p>التاريخ: <?php echo date('d/m/Y'); ?></p>
            </div>
            <div class="signature-box">
                <p><strong>المدير المالي</strong></p>
                <p>الاسم: _______________</p>
                <p>التوقيع: _______________</p>
                <p>التاريخ: <?php echo date('d/m/Y'); ?></p>
            </div>
            <div class="signature-box">
                <p><strong><?php echo $type === 'sale' ? 'العميل' : 'المورد'; ?></strong></p>
                <p>الاسم: _______________</p>
                <p>التوقيع: _______________</p>
                <p>التاريخ: _______________</p>
            </div>
        </div>

        <!-- معلومات الطباعة -->
        <div class="print-info">
            <p>تم إنشاء هذه الفاتورة بواسطة <?php echo htmlspecialchars($company_name); ?> - إصدار <?php echo $system_version; ?> |
            تاريخ الطباعة: <?php echo date('d/m/Y H:i:s'); ?> |
            رقم الفاتورة: <?php echo $invoice['invoice_number']; ?> |
            نوع الفاتورة: <?php echo $title; ?></p>
        </div>

        <!-- أزرار الطباعة (تظهر على الشاشة فقط) -->
        <div class="no-print" style="margin-top: 30px; text-align: center; padding: 20px; border-top: 2px solid #000;">
            <button onclick="window.print()" style="background: #007bff; color: white; border: none; padding: 12px 25px; border-radius: 5px; font-size: 14px; cursor: pointer; margin: 0 10px;">
                <i class="fas fa-print"></i> طباعة الفاتورة
            </button>
            <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 5px; font-size: 14px; cursor: pointer; margin: 0 10px;">
                <i class="fas fa-times"></i> إغلاق
            </button>
            <button onclick="window.history.back()" style="background: #28a745; color: white; border: none; padding: 12px 25px; border-radius: 5px; font-size: 14px; cursor: pointer; margin: 0 10px;">
                <i class="fas fa-arrow-right"></i> العودة
            </button>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.addEventListener('load', function() {
        //     setTimeout(function() {
        //         window.print();
        //     }, 1000);
        // });

        // تحسين الطباعة
        window.addEventListener('beforeprint', function() {
            document.title = '<?php echo $title; ?> - <?php echo $invoice['invoice_number']; ?>';
        });

        window.addEventListener('afterprint', function() {
            document.title = '<?php echo $title; ?> - رقم <?php echo $invoice['invoice_number']; ?>';
        });
    </script>
</body>
</html>