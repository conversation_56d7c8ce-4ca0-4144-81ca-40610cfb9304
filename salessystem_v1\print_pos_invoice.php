<?php
/**
 * طباعة الفواتير بتنسيق POS
 * محسن للطابعات الحرارية 58mm و 80mm
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header("Location: login.php");
    exit();
}

// الحصول على معاملات الطلب
$invoice_id = intval($_GET['id'] ?? 0);
$type = $_GET['type'] ?? 'sale';
$width = $_GET['width'] ?? '80'; // عرض الورق بالمليمتر (58 أو 80)

if ($invoice_id <= 0) {
    die('معرف الفاتورة غير صحيح');
}

// تحديد نوع الفاتورة والجدول
$table = ($type === 'sale') ? 'sales' : 'purchases';
$title = ($type === 'sale') ? 'فاتورة مبيعات' : 'فاتورة مشتريات';

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // جلب بيانات الفاتورة
    $stmt = $db->prepare("SELECT * FROM $table WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ii", $invoice_id, $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception("الفاتورة غير موجودة");
    }

    $invoice = $result->fetch_assoc();
    $stmt->close();

    // جلب عناصر الفاتورة
    $items_table = ($type === 'sale') ? 'sale_items' : 'purchase_items';
    $foreign_key = ($type === 'sale') ? 'sale_id' : 'purchase_id';
    
    $items_stmt = $db->prepare("SELECT * FROM $items_table WHERE $foreign_key = ?");
    $items_stmt->bind_param("i", $invoice_id);
    $items_stmt->execute();
    $items_result = $items_stmt->get_result();
    $items = $items_result->fetch_all(MYSQLI_ASSOC);
    $items_stmt->close();

    // جلب بيانات العميل/المورد
    $customer_table = ($type === 'sale') ? 'customers' : 'suppliers';
    $customer_id_field = ($type === 'sale') ? 'customer_id' : 'supplier_id';
    
    $customer = null;
    if (!empty($invoice[$customer_id_field])) {
        $customer_stmt = $db->prepare("SELECT * FROM $customer_table WHERE id = ?");
        $customer_stmt->bind_param("i", $invoice[$customer_id_field]);
        $customer_stmt->execute();
        $customer_result = $customer_stmt->get_result();
        if ($customer_result->num_rows > 0) {
            $customer = $customer_result->fetch_assoc();
        }
        $customer_stmt->close();
    }

    // جلب بيانات الشركة
    $company_name = "شركة المبيعات";
    $company_phone = "0*********";
    $company_address = "العنوان الرئيسي";
    $company_tax = "*********";

} catch (Exception $e) {
    die('خطأ: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?> رقم <?php echo $invoice['invoice_number']; ?> - POS</title>
    <link rel="stylesheet" href="assets/css/pos-print.css">
    <style>
        /* تخصيص العرض حسب نوع الطابعة */
        @media print {
            @page {
                size: <?php echo $width; ?>mm auto;
                margin: 0;
            }
        }
        
        .pos-preview {
            max-width: <?php echo $width; ?>mm;
        }
    </style>
</head>
<body>
    <div class="pos-invoice pos-preview">
        <!-- رأس الفاتورة -->
        <div class="pos-header">
            <div class="company-name"><?php echo htmlspecialchars($company_name); ?></div>
            <div class="company-details">
                <?php if ($company_phone): ?>
                    <div>هاتف: <?php echo htmlspecialchars($company_phone); ?></div>
                <?php endif; ?>
                <?php if ($company_address): ?>
                    <div><?php echo htmlspecialchars($company_address); ?></div>
                <?php endif; ?>
                <?php if ($company_tax): ?>
                    <div>الرقم الضريبي: <?php echo htmlspecialchars($company_tax); ?></div>
                <?php endif; ?>
            </div>
            <div class="invoice-title"><?php echo $title; ?></div>
        </div>

        <!-- معلومات الفاتورة -->
        <div class="pos-info">
            <div class="pos-info-row">
                <span class="pos-info-label">رقم الفاتورة:</span>
                <span class="pos-info-value number"><?php echo htmlspecialchars($invoice['invoice_number']); ?></span>
            </div>
            <div class="pos-info-row">
                <span class="pos-info-label">التاريخ:</span>
                <span class="pos-info-value number"><?php echo date('Y-m-d', strtotime($invoice['date'])); ?></span>
            </div>
            <div class="pos-info-row">
                <span class="pos-info-label">الوقت:</span>
                <span class="pos-info-value number"><?php echo date('H:i', strtotime($invoice['created_at'])); ?></span>
            </div>
            <?php if ($customer): ?>
            <div class="pos-info-row">
                <span class="pos-info-label"><?php echo $type === 'sale' ? 'العميل:' : 'المورد:'; ?></span>
                <span class="pos-info-value"><?php echo htmlspecialchars($customer['name']); ?></span>
            </div>
            <?php if (!empty($customer['phone'])): ?>
            <div class="pos-info-row">
                <span class="pos-info-label">الهاتف:</span>
                <span class="pos-info-value number"><?php echo htmlspecialchars($customer['phone']); ?></span>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- جدول المنتجات -->
        <table class="pos-table">
            <thead>
                <tr>
                    <th class="item-name">المنتج</th>
                    <th class="item-qty">الكمية</th>
                    <th class="item-price">السعر</th>
                    <th class="item-total">المجموع</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($items as $item): ?>
                <tr>
                    <td class="item-name"><?php echo htmlspecialchars($item['product_name']); ?></td>
                    <td class="item-qty number"><?php echo number_format($item['quantity'], 0); ?></td>
                    <td class="item-price number currency"><?php echo number_format($item['unit_price'], 2); ?></td>
                    <td class="item-total number currency"><?php echo number_format($item['total_price'], 2); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <!-- الإجماليات -->
        <div class="pos-totals">
            <div class="pos-total-row">
                <span class="pos-total-label">المجموع الفرعي:</span>
                <span class="pos-total-value number currency"><?php echo number_format($invoice['subtotal'] ?? 0, 2); ?> ر.س</span>
            </div>

            <?php if (isset($invoice['discount_amount']) && $invoice['discount_amount'] > 0): ?>
            <div class="pos-total-row">
                <span class="pos-total-label">الخصم:</span>
                <span class="pos-total-value number currency">-<?php echo number_format($invoice['discount_amount'], 2); ?> ر.س</span>
            </div>
            <?php endif; ?>

            <?php if (isset($invoice['tax_amount']) && $invoice['tax_amount'] > 0): ?>
            <div class="pos-total-row">
                <span class="pos-total-label">الضريبة<?php echo isset($invoice['tax_rate']) ? ' (' . $invoice['tax_rate'] . '%)' : ''; ?>:</span>
                <span class="pos-total-value number currency"><?php echo number_format($invoice['tax_amount'], 2); ?> ر.س</span>
            </div>
            <?php endif; ?>

            <div class="pos-total-row grand-total">
                <span class="pos-total-label">الإجمالي:</span>
                <span class="pos-total-value number currency"><?php echo number_format($invoice['total_amount'] ?? 0, 2); ?> ر.س</span>
            </div>
        </div>

        <!-- معلومات الدفع -->
        <div class="pos-payment">
            <?php
            $paid_amount = $invoice['paid_amount'] ?? 0;
            $total_amount = $invoice['total_amount'] ?? 0;
            ?>
            <div class="pos-total-row">
                <span class="pos-total-label">المبلغ المدفوع:</span>
                <span class="pos-total-value number currency"><?php echo number_format($paid_amount, 2); ?> ر.س</span>
            </div>

            <?php
            $remaining = $total_amount - $paid_amount;
            if ($remaining > 0):
            ?>
            <div class="pos-total-row">
                <span class="pos-total-label">المتبقي:</span>
                <span class="pos-total-value number currency"><?php echo number_format($remaining, 2); ?> ر.س</span>
            </div>
            <?php elseif ($remaining < 0): ?>
            <div class="pos-total-row">
                <span class="pos-total-label">الباقي للعميل:</span>
                <span class="pos-total-value number currency"><?php echo number_format(abs($remaining), 2); ?> ر.س</span>
            </div>
            <?php endif; ?>

            <?php if (isset($invoice['payment_status'])): ?>
            <div class="pos-total-row">
                <span class="pos-total-label">حالة الدفع:</span>
                <span class="pos-total-value">
                    <?php
                    switch($invoice['payment_status']) {
                        case 'paid': echo 'مدفوع'; break;
                        case 'unpaid': echo 'غير مدفوع'; break;
                        case 'partial': echo 'مدفوع جزئياً'; break;
                        default: echo $invoice['payment_status'];
                    }
                    ?>
                </span>
            </div>
            <?php endif; ?>
        </div>

        <!-- ذيل الفاتورة -->
        <div class="pos-footer">
            <div class="thank-you">شكراً لتعاملكم معنا</div>
            <div class="contact-info">للاستفسار: <?php echo htmlspecialchars($company_phone); ?></div>
            <?php if (!empty($invoice['notes'])): ?>
            <div class="notes">ملاحظات: <?php echo htmlspecialchars($invoice['notes']); ?></div>
            <?php endif; ?>
        </div>

        <!-- خط القطع -->
        <div class="pos-cut-line"></div>
    </div>

    <!-- أزرار التحكم (مخفية عند الطباعة) -->
    <div class="no-print" style="text-align: center; margin: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h4>خيارات الطباعة</h4>
        <div style="margin: 15px 0;">
            <label>عرض الورق: </label>
            <select id="paperWidth" onchange="changePaperWidth()" style="margin: 0 10px; padding: 5px;">
                <option value="58" <?php echo $width == '58' ? 'selected' : ''; ?>>58mm (صغير)</option>
                <option value="80" <?php echo $width == '80' ? 'selected' : ''; ?>>80mm (متوسط)</option>
            </select>
        </div>
        <div style="margin: 15px 0;">
            <button onclick="window.print()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">
                🖨️ طباعة
            </button>
            <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">
                ❌ إغلاق
            </button>
            <button onclick="location.reload()" style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">
                🔄 تحديث
            </button>
        </div>
        <div style="font-size: 12px; color: #666; margin-top: 10px;">
            💡 نصيحة: تأكد من ضبط إعدادات الطابعة على "بدون هوامش" للحصول على أفضل نتيجة
        </div>
    </div>

    <script>
        // تغيير عرض الورق
        function changePaperWidth() {
            const width = document.getElementById('paperWidth').value;
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('width', width);
            window.location.href = currentUrl.toString();
        }

        // طباعة تلقائية عند التحميل (اختياري)
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }

        // تحسين تجربة الطباعة
        window.addEventListener('beforeprint', function() {
            document.title = '<?php echo $title; ?> - <?php echo $invoice['invoice_number']; ?>';
        });

        window.addEventListener('afterprint', function() {
            // يمكن إضافة إجراءات بعد الطباعة هنا
            console.log('تمت الطباعة بنجاح');
        });
    </script>
</body>
</html>
