<?php
/**
 * صفحة الملف الشخصي للمستخدم
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// فحص تسجيل الدخول قبل إرسال أي محتوى
redirectIfNotLoggedIn();

require_once __DIR__ . '/includes/header_simple.php';

$user_data = null;
$update_success = false;
$errors = [];

// الحصول على بيانات المستخدم الحالي
$db = getUnifiedDB();
if (!$db) {
    $errors[] = "خطأ في الاتصال بقاعدة البيانات";
} else {
    try {
        $stmt = $db->prepare("SELECT id, username, full_name, email, created_at FROM users WHERE id = ?");
        $stmt->bind_param("i", $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $user_data = $result->fetch_assoc();
        $stmt->close();
    } catch (Exception $e) {
        $errors[] = "خطأ في جلب بيانات المستخدم: " . $e->getMessage();
    }
}

// معالجة تحديث البيانات الأساسية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');

    // التحقق من صحة البيانات
    if (empty($full_name)) {
        $errors[] = "الاسم الكامل مطلوب";
    }

    if (empty($email)) {
        $errors[] = "البريد الإلكتروني مطلوب";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "البريد الإلكتروني غير صحيح";
    }

    // التحقق من عدم وجود بريد إلكتروني مكرر
    if (empty($errors) && $db) {
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->bind_param("si", $email, $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $errors[] = "البريد الإلكتروني موجود بالفعل لمستخدم آخر";
        }
        $stmt->close();
    }

    // تحديث البيانات إذا لم توجد أخطاء
    if (empty($errors) && $db) {
        try {
            $stmt = $db->prepare("UPDATE users SET full_name = ?, email = ? WHERE id = ?");
            $stmt->bind_param("ssi", $full_name, $email, $_SESSION['user_id']);

            if ($stmt->execute()) {
                $_SESSION['success'] = "تم تحديث البيانات الأساسية بنجاح";

                // تحديث بيانات المستخدم المعروضة
                $user_data['full_name'] = $full_name;
                $user_data['email'] = $email;
            } else {
                $errors[] = "حدث خطأ أثناء تحديث البيانات";
            }
            $stmt->close();
        } catch (Exception $e) {
            $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
        }
    }

    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // التحقق من صحة البيانات
    if (empty($current_password)) {
        $errors[] = "يجب إدخال كلمة المرور الحالية";
    }

    if (empty($new_password)) {
        $errors[] = "يجب إدخال كلمة المرور الجديدة";
    } elseif (strlen($new_password) < 6) {
        $errors[] = "كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل";
    }

    if (empty($confirm_password)) {
        $errors[] = "يجب تأكيد كلمة المرور الجديدة";
    } elseif ($new_password !== $confirm_password) {
        $errors[] = "كلمة المرور الجديدة وتأكيدها غير متطابقتين";
    }

    // التحقق من كلمة المرور الحالية
    if (empty($errors) && $db) {
        $stmt = $db->prepare("SELECT password FROM users WHERE id = ?");
        $stmt->bind_param("i", $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $user_password_data = $result->fetch_assoc();
        $stmt->close();

        if (!password_verify($current_password, $user_password_data['password'])) {
            $errors[] = "كلمة المرور الحالية غير صحيحة";
        }
    }

    // تحديث كلمة المرور إذا لم توجد أخطاء
    if (empty($errors) && $db) {
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $db->prepare("UPDATE users SET password = ? WHERE id = ?");
            $stmt->bind_param("si", $hashed_password, $_SESSION['user_id']);

            if ($stmt->execute()) {
                $_SESSION['success'] = "تم تغيير كلمة المرور بنجاح";
            } else {
                $errors[] = "حدث خطأ أثناء تغيير كلمة المرور";
            }
            $stmt->close();
        } catch (Exception $e) {
            $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
        }
    }

    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// الرسائل يتم عرضها تلقائياً في header_simple.php?>

<!-- تضمين ملف CSS الخاص بالملف الشخصي -->
<link rel="stylesheet" href="assets/css/profile.css">

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card profile-card fade-in">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-circle"></i>
                        الملف الشخصي
                    </h4>
                </div>
                <div class="card-body">
                    <?php if ($user_data): ?>
                    
                    <!-- معلومات المستخدم الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="avatar-placeholder bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                     style="width: 120px; height: 120px; font-size: 48px;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <h5 class="mt-3"><?php echo htmlspecialchars($user_data['full_name']); ?></h5>
                                <p class="text-muted">@<?php echo htmlspecialchars($user_data['username']); ?></p>
                                <small class="text-muted">
                                    عضو منذ: <?php echo date('d/m/Y', strtotime($user_data['created_at'])); ?>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h5>معلومات الحساب</h5>
                            <table class="table table-borderless info-table">
                                <tr>
                                    <td><strong>اسم المستخدم:</strong></td>
                                    <td><?php echo htmlspecialchars($user_data['username']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم الكامل:</strong></td>
                                    <td><?php echo htmlspecialchars($user_data['full_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td><?php echo htmlspecialchars($user_data['email']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($user_data['created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <hr>

                    <!-- أزرار التحكم -->
                    <div class="text-center">
                        <button type="button" class="btn btn-primary btn-profile me-2" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                            <i class="fas fa-edit"></i> تعديل البيانات
                        </button>
                        <button type="button" class="btn btn-warning btn-profile" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </button>
                    </div>
                    
                    <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        لا يمكن جلب بيانات المستخدم. يرجى المحاولة مرة أخرى.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- روابط إضافية -->
            <div class="card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs"></i>
                        إعدادات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إدارة الحساب:</h6>
                            <div class="d-grid gap-2">
                                <a href="index.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                                <a href="logout.php" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>إحصائيات سريعة:</h6>
                            <?php
                            if ($db) {
                                try {
                                    $customers_result = $db->query("SELECT COUNT(*) as count FROM customers WHERE user_id = {$_SESSION['user_id']}");
                                    $customers_count = $customers_result ? $customers_result->fetch_assoc()['count'] : 0;

                                    $sales_result = $db->query("SELECT COUNT(*) as count FROM sales WHERE user_id = {$_SESSION['user_id']}");
                                    $sales_count = $sales_result ? $sales_result->fetch_assoc()['count'] : 0;

                                    $purchases_result = $db->query("SELECT COUNT(*) as count FROM purchases WHERE user_id = {$_SESSION['user_id']}");
                                    $purchases_count = $purchases_result ? $purchases_result->fetch_assoc()['count'] : 0;
                                    ?>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-users text-primary"></i> العملاء: <?php echo $customers_count; ?></li>
                                        <li><i class="fas fa-shopping-cart text-success"></i> المبيعات: <?php echo $sales_count; ?></li>
                                        <li><i class="fas fa-truck text-info"></i> المشتريات: <?php echo $purchases_count; ?></li>
                                    </ul>
                                    <?php
                                } catch (Exception $e) {
                                    echo '<p class="text-muted">لا يمكن جلب الإحصائيات</p>';
                                }
                            } else {
                                echo '<p class="text-muted">لا يمكن الاتصال بقاعدة البيانات</p>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة منبثقة لتعديل البيانات الأساسية -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editProfileModalLabel">
                    <i class="fas fa-edit"></i> تعديل البيانات الأساسية
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="<?php echo 'النص'; ?>"></button>
            </div>
            <form method="POST" id="editProfileForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal_username_display" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="modal_username_display"
                                       value="<?php echo htmlspecialchars($user_data['username']); ?>"
                                       disabled>
                                <small class="form-text text-muted">اسم المستخدم غير قابل للتعديل</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modal_full_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="modal_full_name" name="full_name"
                                       value="<?php echo htmlspecialchars($user_data['full_name']); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="modal_email" class="form-label">البريد الإلكتروني *</label>
                        <input type="email" class="form-control" id="modal_email" name="email"
                               value="<?php echo htmlspecialchars($user_data['email']); ?>" required>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> سيتم تحديث البيانات الأساسية فقط. لتغيير كلمة المرور، استخدم النافذة المخصصة لذلك.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" name="update_profile" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة منبثقة لتغيير كلمة المرور -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="changePasswordModalLabel">
                    <i class="fas fa-key"></i> تغيير كلمة المرور
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="<?php echo 'النص'; ?>"></button>
            </div>
            <form method="POST" id="changePasswordForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="modal_current_password" class="form-label">كلمة المرور الحالية *</label>
                        <input type="password" class="form-control" id="modal_current_password" name="current_password" required>
                    </div>

                    <div class="mb-3">
                        <label for="modal_new_password" class="form-label">كلمة المرور الجديدة *</label>
                        <input type="password" class="form-control" id="modal_new_password" name="new_password" minlength="6" required>
                        <small class="form-text text-muted">يجب أن تكون 6 أحرف على الأقل</small>
                    </div>

                    <div class="mb-3">
                        <label for="modal_confirm_password" class="form-label">تأكيد كلمة المرور الجديدة *</label>
                        <input type="password" class="form-control" id="modal_confirm_password" name="confirm_password" minlength="6" required>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تأكد من حفظ كلمة المرور الجديدة في مكان آمن. لن تتمكن من استردادها لاحقاً.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" name="change_password" class="btn btn-warning">
                        <i class="fas fa-key"></i> تغيير كلمة المرور
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// دالة لإعادة تعيين النموذج في النافذة المنبثقة
function resetEditForm() {
    document.getElementById('modal_full_name').value = '<?php echo addslashes($user_data['full_name'] ?? ''); ?>';
    document.getElementById('modal_email').value = '<?php echo addslashes($user_data['email'] ?? ''); ?>';
}

function resetPasswordForm() {
    document.getElementById('changePasswordForm').reset();
}

// التحقق من تطابق كلمات المرور في النافذة المنبثقة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من تطابق كلمات المرور
    document.getElementById('modal_confirm_password').addEventListener('input', function() {
        const newPassword = document.getElementById('modal_new_password').value;
        const confirmPassword = this.value;

        if (newPassword && confirmPassword && newPassword !== confirmPassword) {
            this.setCustomValidity('كلمة المرور غير متطابقة');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });

    // التحقق من قوة كلمة المرور
    document.getElementById('modal_new_password').addEventListener('input', function() {
        const password = this.value;
        const strengthIndicator = document.getElementById('password-strength');

        if (password.length === 0) {
            if (strengthIndicator) strengthIndicator.style.display = 'none';
            return;
        }

        let strength = 0;
        if (password.length >= 6) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;

        // إنشاء مؤشر قوة كلمة المرور إذا لم يكن موجوداً
        if (!strengthIndicator) {
            const indicator = document.createElement('div');
            indicator.id = 'password-strength';
            indicator.className = 'password-strength mt-2';
            this.parentNode.appendChild(indicator);
        }

        const indicator = document.getElementById('password-strength');
        indicator.style.display = 'block';

        if (strength < 2) {
            indicator.innerHTML = '<div class="password-strength-bar password-strength-weak"></div><small class="text-danger">ضعيفة</small>';
        } else if (strength < 4) {
            indicator.innerHTML = '<div class="password-strength-bar password-strength-medium"></div><small class="text-warning">متوسطة</small>';
        } else {
            indicator.innerHTML = '<div class="password-strength-bar password-strength-strong"></div><small class="text-success">قوية</small>';
        }
    });

    // إعادة تعيين النماذج عند إغلاق النوافذ المنبثقة
    document.getElementById('editProfileModal').addEventListener('hidden.bs.modal', function() {
        resetEditForm();
    });

    document.getElementById('changePasswordModal').addEventListener('hidden.bs.modal', function() {
        resetPasswordForm();
        // إخفاء مؤشر قوة كلمة المرور
        const strengthIndicator = document.getElementById('password-strength');
        if (strengthIndicator) {
            strengthIndicator.style.display = 'none';
        }
    });

    // التحقق من النماذج قبل الإرسال
    document.getElementById('editProfileForm').addEventListener('submit', function(e) {
        const fullName = document.getElementById('modal_full_name').value.trim();
        const email = document.getElementById('modal_email').value.trim();

        if (!fullName) {
            e.preventDefault();
            alert('يرجى إدخال الاسم الكامل');
            return false;
        }

        if (!email || !isValidEmail(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }
    });

    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
        const currentPassword = document.getElementById('modal_current_password').value;
        const newPassword = document.getElementById('modal_new_password').value;
        const confirmPassword = document.getElementById('modal_confirm_password').value;

        if (!currentPassword) {
            e.preventDefault();
            alert('يرجى إدخال كلمة المرور الحالية');
            return false;
        }

        if (!newPassword || newPassword.length < 6) {
            e.preventDefault();
            alert('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
            return false;
        }

        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('كلمة المرور الجديدة وتأكيدها غير متطابقتين');
            return false;
        }
    });
});

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة لإظهار رسائل النجاح
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
