<?php
/**
 * اختبار سريع لقاعدة البيانات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo '<h2>🔍 اختبار سريع لقاعدة البيانات</h2>';

// اختبار الاتصالات المختلفة
$connections = [
    ['localhost', 'root', '', 'u193708811_system_main'],
    ['localhost', 'root', '', 'salessystem_v2'],
    ['127.0.0.1', 'root', '', 'u193708811_system_main'],
    ['127.0.0.1', 'root', '', 'salessystem_v2'],
    ['localhost', 'u193708811_system_main', 'dNz35nd5@', 'u193708811_system_main'],
    ['localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main']
];

echo '<h3>اختبار الاتصالات المختلفة:</h3>';

$working_connection = null;

foreach ($connections as $i => $conn) {
    list($host, $user, $pass, $db_name) = $conn;
    
    echo "<h4>محاولة " . ($i + 1) . ": $host / $user / " . ($pass ? '***' : 'بدون كلمة مرور') . " / $db_name</h4>";
    
    try {
        $mysqli = new mysqli($host, $user, $pass, $db_name);
        
        if ($mysqli->connect_error) {
            echo "<p style='color: red;'>❌ فشل: " . htmlspecialchars($mysqli->connect_error) . "</p>";
        } else {
            echo "<p style='color: green;'>✅ نجح الاتصال!</p>";
            
            // اختبار الجداول
            $tables = ['users', 'admins', 'supervisors'];
            foreach ($tables as $table) {
                $result = $mysqli->query("SHOW TABLES LIKE '$table'");
                if ($result && $result->num_rows > 0) {
                    echo "<p style='margin-left: 20px; color: green;'>✅ جدول $table موجود</p>";
                } else {
                    echo "<p style='margin-left: 20px; color: orange;'>⚠️ جدول $table غير موجود</p>";
                }
            }
            
            $working_connection = $conn;
            $mysqli->close();
            break; // توقف عند أول اتصال ناجح
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

if ($working_connection) {
    echo '<h3>✅ تم العثور على اتصال صالح!</h3>';
    echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px;">';
    echo '<h4>بيانات الاتصال الصالح:</h4>';
    echo '<ul>';
    echo '<li>الخادم: ' . htmlspecialchars($working_connection[0]) . '</li>';
    echo '<li>المستخدم: ' . htmlspecialchars($working_connection[1]) . '</li>';
    echo '<li>كلمة المرور: ' . ($working_connection[2] ? '***' : 'بدون كلمة مرور') . '</li>';
    echo '<li>قاعدة البيانات: ' . htmlspecialchars($working_connection[3]) . '</li>';
    echo '</ul>';
    echo '</div>';
    
    // إنشاء ملف إعدادات مؤقت
    $config_content = "<?php
// إعدادات قاعدة البيانات الصالحة
\$db_host = '{$working_connection[0]}';
\$db_user = '{$working_connection[1]}';
\$db_pass = '{$working_connection[2]}';
\$db_name = '{$working_connection[3]}';

function getWorkingDB() {
    global \$db_host, \$db_user, \$db_pass, \$db_name;
    
    \$mysqli = new mysqli(\$db_host, \$db_user, \$db_pass, \$db_name);
    
    if (\$mysqli->connect_error) {
        error_log('Database connection failed: ' . \$mysqli->connect_error);
        return null;
    }
    
    \$mysqli->set_charset('utf8mb4');
    return \$mysqli;
}
?>";
    
    file_put_contents('config/working_db_config.php', $config_content);
    echo '<p style="color: green;">✅ تم إنشاء ملف config/working_db_config.php</p>';
    
} else {
    echo '<h3>❌ لم يتم العثور على اتصال صالح!</h3>';
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px;">';
    echo '<h4>الحلول المقترحة:</h4>';
    echo '<ul>';
    echo '<li>تأكد من تشغيل XAMPP/MySQL</li>';
    echo '<li>تحقق من إعدادات قاعدة البيانات</li>';
    echo '<li>أنشئ قاعدة بيانات جديدة باسم "salessystem_v2"</li>';
    echo '<li>استخدم المستخدم "root" بدون كلمة مرور للتطوير المحلي</li>';
    echo '</ul>';
    echo '</div>';
}

// اختبار إنشاء قاعدة بيانات جديدة
echo '<h3>إنشاء قاعدة بيانات جديدة:</h3>';
try {
    $mysqli = new mysqli('localhost', 'root', '');
    if (!$mysqli->connect_error) {
        echo '<p style="color: green;">✅ اتصال بالخادم نجح</p>';
        
        // إنشاء قاعدة البيانات
        $create_db = "CREATE DATABASE IF NOT EXISTS salessystem_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
        if ($mysqli->query($create_db)) {
            echo '<p style="color: green;">✅ تم إنشاء قاعدة البيانات salessystem_v2</p>';
            
            // اختبار الاتصال بقاعدة البيانات الجديدة
            $mysqli->select_db('salessystem_v2');
            echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات الجديدة</p>';
            
            // إنشاء ملف إعدادات للقاعدة الجديدة
            $new_config = "<?php
// إعدادات قاعدة البيانات الجديدة
function getSimpleDB() {
    \$mysqli = new mysqli('localhost', 'root', '', 'salessystem_v2');
    
    if (\$mysqli->connect_error) {
        error_log('Database connection failed: ' . \$mysqli->connect_error);
        return null;
    }
    
    \$mysqli->set_charset('utf8mb4');
    return \$mysqli;
}
?>";
            file_put_contents('config/simple_db_config.php', $new_config);
            echo '<p style="color: green;">✅ تم إنشاء ملف config/simple_db_config.php</p>';
            
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء قاعدة البيانات: ' . $mysqli->error . '</p>';
        }
    } else {
        echo '<p style="color: red;">❌ فشل الاتصال بالخادم: ' . $mysqli->connect_error . '</p>';
    }
} catch (Exception $e) {
    echo '<p style="color: red;">❌ خطأ: ' . htmlspecialchars($e->getMessage()) . '</p>';
}

echo '<h3>الخطوات التالية:</h3>';
echo '<ol>';
echo '<li><a href="setup_new_database.php">إعداد قاعدة البيانات الجديدة</a></li>';
echo '<li><a href="work_management_login.php">اختبار تسجيل الدخول</a></li>';
echo '</ol>';
?>
