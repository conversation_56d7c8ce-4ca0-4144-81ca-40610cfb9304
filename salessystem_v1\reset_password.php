<?php
session_start();
require_once 'config/simple_db_config.php';
require_once 'config/email_config.php';
require_once 'includes/functions.php';

// إذا كان المستخدم مسجل دخوله، إعادة توجيه للصفحة الرئيسية
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$message = '';
$error = '';
$token_valid = false;
$user_data = null;

// تنظيف الرموز المنتهية الصلاحية أولاً
cleanExpiredTokens();

// التحقق من صحة الرمز
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];
    $user_data = validateResetToken($token);

    if ($user_data) {
        $token_valid = true;
    } else {
        $error = 'رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية';
    }
} else {
    $error = 'رابط غير صحيح';
}

// معالجة إعادة تعيين كلمة المرور
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reset_password']) && $token_valid) {
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    $token = $_POST['token'];
    
    if (empty($new_password) || empty($confirm_password)) {
        $error = 'يرجى إدخال كلمة المرور وتأكيدها';
    } elseif (strlen($new_password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($new_password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيدها غير متطابقتين';
    } else {
        $db = getDB();
        if ($db) {
            // التحقق مرة أخرى من صحة الرمز
            $stmt = $db->prepare("SELECT id FROM users WHERE reset_token = ? AND reset_token_expires > NOW() AND status = 'active'");
            $stmt->bind_param("s", $token);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $user = $result->fetch_assoc();
                
                // تشفير كلمة المرور الجديدة
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                
                // تحديث كلمة المرور وحذف رمز إعادة التعيين
                $update_stmt = $db->prepare("UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?");
                $update_stmt->bind_param("si", $hashed_password, $user['id']);
                
                if ($update_stmt->execute()) {
                    $message = 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.';
                    $token_valid = false; // منع إعادة استخدام النموذج
                    
                    // تسجيل العملية
                    error_log("Password reset completed for user ID: " . $user['id']);
                } else {
                    $error = 'حدث خطأ أثناء تحديث كلمة المرور. يرجى المحاولة مرة أخرى.';
                }
                $update_stmt->close();
            } else {
                $error = 'رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية';
                $token_valid = false;
            }
            $stmt->close();
        } else {
            $error = 'خطأ في الاتصال بقاعدة البيانات';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - نظام إدارة المبيعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .reset-password-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 20px;
        }
        .reset-password-header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .reset-password-header h2 {
            margin: 0;
            font-weight: 600;
        }
        .reset-password-body {
            padding: 2rem;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            border: none;
            padding: 12px;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .back-to-login {
            text-align: center;
            margin-top: 1rem;
        }
        .back-to-login a {
            color: #6b7280;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        .back-to-login a:hover {
            color: #2563eb;
        }
        .user-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #2563eb;
        }
        .password-requirements {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }
        .password-strength {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            margin-top: 0.5rem;
            overflow: hidden;
        }
        .password-strength-bar {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="reset-password-container">
        <div class="reset-password-header">
            <i class="fas fa-lock fa-2x mb-3"></i>
            <h2>إعادة تعيين كلمة المرور</h2>
            <p class="mb-0">أدخل كلمة المرور الجديدة</p>
        </div>
        
        <div class="reset-password-body">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <div class="text-center">
                    <a href="login.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </a>
                </div>
            <?php elseif ($token_valid && $user_data): ?>
                <div class="user-info">
                    <strong>المستخدم:</strong> <?php echo htmlspecialchars($user_data['full_name'] ?: $user_data['username']); ?><br>
                    <strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($user_data['email']); ?>
                </div>
                
                <form method="POST" action="" id="resetForm">
                    <input type="hidden" name="token" value="<?php echo htmlspecialchars($_GET['token']); ?>">
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               placeholder="كلمة المرور الجديدة" required minlength="6">
                        <label for="new_password">
                            <i class="fas fa-lock me-2"></i>كلمة المرور الجديدة
                        </label>
                        <div class="password-requirements">
                            يجب أن تكون كلمة المرور 6 أحرف على الأقل
                        </div>
                        <div class="password-strength">
                            <div class="password-strength-bar" id="strengthBar"></div>
                        </div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               placeholder="تأكيد كلمة المرور" required minlength="6">
                        <label for="confirm_password">
                            <i class="fas fa-lock me-2"></i>تأكيد كلمة المرور
                        </label>
                    </div>
                    
                    <button type="submit" name="reset_password" class="btn btn-primary w-100">
                        <i class="fas fa-save me-2"></i>
                        حفظ كلمة المرور الجديدة
                    </button>
                </form>
            <?php endif; ?>
            
            <div class="back-to-login">
                <a href="login.php">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة إلى تسجيل الدخول
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من قوة كلمة المرور
        document.getElementById('new_password')?.addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('strengthBar');
            
            let strength = 0;
            if (password.length >= 6) strength += 25;
            if (password.match(/[a-z]/)) strength += 25;
            if (password.match(/[A-Z]/)) strength += 25;
            if (password.match(/[0-9]/)) strength += 25;
            
            strengthBar.style.width = strength + '%';
            
            if (strength < 50) {
                strengthBar.style.backgroundColor = '#ef4444';
            } else if (strength < 75) {
                strengthBar.style.backgroundColor = '#f59e0b';
            } else {
                strengthBar.style.backgroundColor = '#10b981';
            }
        });
        
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password')?.addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // التحقق من النموذج قبل الإرسال
        document.getElementById('resetForm')?.addEventListener('submit', function(e) {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيدها غير متطابقتين');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        });
    </script>
</body>
</html>
