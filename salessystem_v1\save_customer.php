<?php
/**
 * حفظ وتعديل العملاء والموردين
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "طريقة إرسال غير صحيحة";
    header("Location: customers.php");
    exit();
}

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    ErrorHandler::logDatabaseError('getCurrentUserDB()', $db ? $db->connect_error : 'Database connection is null');
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات";
    header("Location: customers.php");
    exit();
}

try {
    // استلام البيانات
    $customer_id = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : null;
    $name = trim($_POST['name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $tax_number = trim($_POST['tax_number'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $customer_type = $_POST['customer_type'] ?? 'customer';

    // التحقق من البيانات الأساسية
    if (empty($name)) {
        throw new Exception("الاسم مطلوب");
    }

    if (!in_array($customer_type, ['customer', 'supplier'])) {
        throw new Exception("نوع العميل غير صحيح");
    }

    // التحقق من صحة البريد الإلكتروني
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("البريد الإلكتروني غير صحيح");
    }

    // الحصول على اسم الجدول مع البادئة
    $username = $_SESSION['username'];
    $customers_table = getUserTableName('customers', $username);

    // التحقق من عدم تكرار الاسم
    if ($customer_id) {
        // في حالة التعديل
        $check_stmt = $db->prepare("SELECT id FROM `$customers_table` WHERE name = ? AND customer_type = ? AND user_id = ? AND id != ?");
        $check_stmt->bind_param("ssii", $name, $customer_type, $_SESSION['user_id'], $customer_id);
    } else {
        // في حالة الإضافة
        $check_stmt = $db->prepare("SELECT id FROM `$customers_table` WHERE name = ? AND customer_type = ? AND user_id = ?");
        $check_stmt->bind_param("ssi", $name, $customer_type, $_SESSION['user_id']);
    }

    if (!$check_stmt->execute()) {
        ErrorHandler::logDatabaseError("Check duplicate customer name", $check_stmt->error, [
            'name' => $name,
            'customer_type' => $customer_type,
            'user_id' => $_SESSION['user_id']
        ]);
        throw new Exception("حدث خطأ في التحقق من البيانات");
    }

    $existing_customer = $check_stmt->get_result()->fetch_assoc();
    $check_stmt->close();

    if ($existing_customer) {
        $type_label = $customer_type === 'supplier' ? 'مورد' : 'عميل';
        throw new Exception("يوجد $type_label آخر بنفس الاسم");
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    if (!empty($email)) {
        if ($customer_id) {
            $check_email_stmt = $db->prepare("SELECT id FROM `$customers_table` WHERE email = ? AND user_id = ? AND id != ?");
            $check_email_stmt->bind_param("sii", $email, $_SESSION['user_id'], $customer_id);
        } else {
            $check_email_stmt = $db->prepare("SELECT id FROM `$customers_table` WHERE email = ? AND user_id = ?");
            $check_email_stmt->bind_param("si", $email, $_SESSION['user_id']);
        }

        $check_email_stmt->execute();
        $existing_email = $check_email_stmt->get_result()->fetch_assoc();
        $check_email_stmt->close();

        if ($existing_email) {
            throw new Exception("البريد الإلكتروني مستخدم من قبل");
        }
    }

    // إعداد البيانات
    $customer_data = [
        'name' => $name,
        'phone' => $phone,
        'email' => $email,
        'tax_number' => $tax_number,
        'address' => $address,
        'customer_type' => $customer_type
    ];

    if ($customer_id) {
        // تعديل عميل/مورد موجود باستخدام الدالة الجديدة
        $affected_rows = updateWithUserId('customers', $customer_data, "id = $customer_id", $username);

        if ($affected_rows > 0) {
            $type_label = $customer_type === 'supplier' ? 'المورد' : 'العميل';
            logActivity('customer_update', 'customers', $customer_id, null, [
                'name' => $name,
                'customer_type' => $customer_type
            ], "تعديل $type_label");

            $_SESSION['success'] = "تم تعديل $type_label بنجاح";
        } else {
            throw new Exception("لم يتم العثور على البيانات أو لا توجد تغييرات");
        }
    } else {
        // إضافة عميل/مورد جديد باستخدام الدالة الجديدة
        $new_customer_id = insertWithUserId('customers', $customer_data, $username);

        if ($new_customer_id) {
            $type_label = $customer_type === 'supplier' ? 'مورد' : 'عميل';

            logActivity('customer_create', 'customers', $new_customer_id, null, [
                'name' => $name,
                'customer_type' => $customer_type
            ], "إضافة $type_label جديد");

            $_SESSION['success'] = "تم إضافة $type_label بنجاح (ID: $new_customer_id)";
        } else {
            throw new Exception("حدث خطأ أثناء إضافة البيانات");
        }
    }

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Customer save error: ' . $e->getMessage(), __FILE__, __LINE__, [
        'customer_id' => $customer_id ?? null,
        'name' => $name ?? null,
        'customer_type' => $customer_type ?? null,
        'user_id' => $_SESSION['user_id'] ?? null
    ]);
    $_SESSION['error'] = "خطأ في حفظ البيانات: " . $e->getMessage();
} finally {
    if (isset($db)) {
        $db->close();
    }
}

// إعادة التوجيه مع الحفاظ على نوع العميل
$redirect_type = $_POST['customer_type'] ?? 'customer';
header("Location: customers.php?type=$redirect_type");
exit();
?>
