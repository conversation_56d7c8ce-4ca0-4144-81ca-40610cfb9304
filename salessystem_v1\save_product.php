<?php
/**
 * حفظ وتعديل المنتجات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "طريقة إرسال غير صحيحة";
    header("Location: products.php");
    exit();
}

// الحصول على اتصال قاعدة البيانات الموحدة
$db = getDB();
if ($db === null || $db->connect_error) {
    ErrorHandler::logDatabaseError('getDB()', $db ? $db->connect_error : 'Database connection is null');
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات";
    header("Location: products.php");
    exit();
}

try {
    // استلام البيانات
    $product_id = !empty($_POST['product_id']) ? intval($_POST['product_id']) : null;
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $tax_rate = floatval($_POST['tax_rate'] ?? 15);
    $category = trim($_POST['category'] ?? '');

    // التحقق من البيانات الأساسية
    if (empty($name)) {
        throw new Exception("اسم المنتج مطلوب");
    }

    if ($price < 0) {
        throw new Exception("السعر يجب أن يكون أكبر من أو يساوي صفر");
    }

    if ($tax_rate < 0 || $tax_rate > 100) {
        throw new Exception("نسبة الضريبة يجب أن تكون بين 0 و 100");
    }

    // التحقق من عدم تكرار اسم المنتج (المنتجات مشتركة)
    if ($product_id) {
        // في حالة التعديل
        $check_stmt = $db->prepare("SELECT id FROM products WHERE name = ? AND id != ?");
        $check_stmt->bind_param("si", $name, $product_id);
    } else {
        // في حالة الإضافة
        $check_stmt = $db->prepare("SELECT id FROM products WHERE name = ?");
        $check_stmt->bind_param("s", $name);
    }

    if (!$check_stmt->execute()) {
        ErrorHandler::logDatabaseError("Check duplicate product name", $check_stmt->error, [
            'name' => $name,
            'product_id' => $product_id
        ]);
        throw new Exception("حدث خطأ في التحقق من البيانات");
    }

    $existing_product = $check_stmt->get_result()->fetch_assoc();
    $check_stmt->close();

    if ($existing_product) {
        throw new Exception("يوجد منتج آخر بنفس الاسم في النظام");
    }

    // إعداد البيانات
    $product_data = [
        'name' => $name,
        'description' => $description,
        'price' => $price,
        'tax_rate' => $tax_rate,
        'category' => $category
    ];

    if ($product_id) {
        // تعديل منتج موجود (مشترك) باستخدام الدالة المحدثة
        $affected_rows = updateWithUserId('products', $product_data, "id = $product_id");

        if ($affected_rows > 0) {
            logActivity('product_update', 'products', $product_id, null, [
                'name' => $name,
                'price' => $price,
                'tax_rate' => $tax_rate
            ], 'تعديل منتج مشترك');

            $_SESSION['success'] = "تم تعديل المنتج المشترك بنجاح";
        } else {
            throw new Exception("لم يتم العثور على المنتج أو لا توجد تغييرات");
        }
    } else {
        // إضافة منتج جديد (مشترك) باستخدام الدالة المحدثة
        $new_product_id = insertWithUserId('products', $product_data);

        if ($new_product_id) {
            logActivity('product_create', 'products', $new_product_id, null, [
                'name' => $name,
                'price' => $price,
                'tax_rate' => $tax_rate
            ], 'إضافة منتج مشترك جديد');

            $_SESSION['success'] = "تم إضافة المنتج المشترك بنجاح (ID: $new_product_id)";
        } else {
            throw new Exception("حدث خطأ أثناء إضافة المنتج");
        }
    }

} catch (Exception $e) {
    ErrorHandler::logError('ERROR', 'Product save error: ' . $e->getMessage(), __FILE__, __LINE__, [
        'product_id' => $product_id ?? null,
        'name' => $name ?? null,
        'price' => $price ?? null,
        'user_id' => $_SESSION['user_id'] ?? null
    ]);
    $_SESSION['error'] = "خطأ في حفظ المنتج: " . $e->getMessage();
} finally {
    if (isset($db)) {
        $db->close();
    }
}

header("Location: products.php");
exit();
?>
