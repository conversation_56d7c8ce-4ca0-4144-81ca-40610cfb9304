<?php
/**
 * إعداد قاعدة البيانات الجديدة
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo '<h1>🚀 إعداد قاعدة البيانات الجديدة</h1>';

try {
    // الاتصال بالخادم
    $mysqli = new mysqli('localhost', 'root', '', '');
    
    if ($mysqli->connect_error) {
        throw new Exception('فشل الاتصال بالخادم: ' . $mysqli->connect_error);
    }
    
    echo '<p style="color: green;">✅ تم الاتصال بخادم MySQL</p>';
    
    // إنشاء قاعدة البيانات
    $create_db = "CREATE DATABASE IF NOT EXISTS salessystem_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
    if ($mysqli->query($create_db)) {
        echo '<p style="color: green;">✅ تم إنشاء قاعدة البيانات salessystem_v2</p>';
    } else {
        throw new Exception('فشل في إنشاء قاعدة البيانات: ' . $mysqli->error);
    }
    
    // الاتصال بقاعدة البيانات الجديدة
    $mysqli->select_db('salessystem_v2');
    echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات الجديدة</p>';
    
    // إنشاء الجداول
    echo '<h2>إنشاء الجداول:</h2>';
    
    // جدول المديرين
    $admins_sql = "CREATE TABLE IF NOT EXISTS `admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `phone` varchar(20) DEFAULT NULL,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `role` enum('super_admin','admin','manager') DEFAULT 'admin',
        `permissions` text DEFAULT NULL,
        `status` enum('active','inactive') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($mysqli->query($admins_sql)) {
        echo '<p style="color: green;">✅ تم إنشاء جدول المديرين</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء جدول المديرين: ' . $mysqli->error . '</p>';
    }
    
    // جدول المشرفين
    $supervisors_sql = "CREATE TABLE IF NOT EXISTS `supervisors` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `phone` varchar(20) DEFAULT NULL,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `department` varchar(50) DEFAULT NULL,
        `permissions` text DEFAULT NULL,
        `status` enum('active','inactive') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($mysqli->query($supervisors_sql)) {
        echo '<p style="color: green;">✅ تم إنشاء جدول المشرفين</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء جدول المشرفين: ' . $mysqli->error . '</p>';
    }
    
    // جدول المستخدمين
    $users_sql = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `password` varchar(255) NOT NULL,
        `name` varchar(100) DEFAULT NULL,
        `status` enum('active','inactive') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($mysqli->query($users_sql)) {
        echo '<p style="color: green;">✅ تم إنشاء جدول المستخدمين</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء جدول المستخدمين: ' . $mysqli->error . '</p>';
    }
    
    // إنشاء البيانات الأولية
    echo '<h2>إنشاء البيانات الأولية:</h2>';
    
    // إنشاء المدير الرئيسي
    $admin_check = $mysqli->query("SELECT COUNT(*) as count FROM admins WHERE username = 'admin'");
    $admin_exists = $admin_check->fetch_assoc()['count'] > 0;
    
    if (!$admin_exists) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $admin_insert = "INSERT INTO admins (username, email, password, full_name, role, status) VALUES ('admin', '<EMAIL>', '$admin_password', 'المدير الرئيسي', 'super_admin', 'active')";
        
        if ($mysqli->query($admin_insert)) {
            echo '<p style="color: green;">✅ تم إنشاء المدير الرئيسي (admin / admin123)</p>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء المدير الرئيسي: ' . $mysqli->error . '</p>';
        }
    } else {
        echo '<p style="color: orange;">⚠️ المدير الرئيسي موجود مسبقاً</p>';
    }
    
    // إنشاء المشرف التجريبي
    $supervisor_check = $mysqli->query("SELECT COUNT(*) as count FROM supervisors WHERE username = 'supervisor'");
    $supervisor_exists = $supervisor_check->fetch_assoc()['count'] > 0;
    
    if (!$supervisor_exists) {
        $supervisor_password = password_hash('supervisor123', PASSWORD_DEFAULT);
        $supervisor_insert = "INSERT INTO supervisors (username, email, phone, password, full_name, department, status) VALUES ('supervisor', '<EMAIL>', '0987654321', '$supervisor_password', 'مشرف تجريبي', 'الإشراف العام', 'active')";
        
        if ($mysqli->query($supervisor_insert)) {
            echo '<p style="color: green;">✅ تم إنشاء المشرف التجريبي (supervisor / supervisor123)</p>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء المشرف التجريبي: ' . $mysqli->error . '</p>';
        }
    } else {
        echo '<p style="color: orange;">⚠️ المشرف التجريبي موجود مسبقاً</p>';
    }
    
    // إنشاء مستخدم تجريبي
    $user_check = $mysqli->query("SELECT COUNT(*) as count FROM users WHERE username = 'employee'");
    $user_exists = $user_check->fetch_assoc()['count'] > 0;
    
    if (!$user_exists) {
        $user_password = password_hash('employee123', PASSWORD_DEFAULT);
        $user_insert = "INSERT INTO users (username, email, password, name, status) VALUES ('employee', '<EMAIL>', '$user_password', 'موظف تجريبي', 'active')";
        
        if ($mysqli->query($user_insert)) {
            echo '<p style="color: green;">✅ تم إنشاء الموظف التجريبي (employee / employee123)</p>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء الموظف التجريبي: ' . $mysqli->error . '</p>';
        }
    } else {
        echo '<p style="color: orange;">⚠️ الموظف التجريبي موجود مسبقاً</p>';
    }
    
    // إنشاء ملف إعدادات قاعدة البيانات البسيط
    $simple_config = '<?php
/**
 * إعدادات قاعدة البيانات البسيطة
 */

function getSimpleDB() {
    $mysqli = new mysqli("localhost", "root", "", "salessystem_v2");
    
    if ($mysqli->connect_error) {
        error_log("Database connection failed: " . $mysqli->connect_error);
        return null;
    }
    
    $mysqli->set_charset("utf8mb4");
    return $mysqli;
}

// دوال التوافق
function getUnifiedDB() { return getSimpleDB(); }
function getMainDB() { return getSimpleDB(); }
function getOperationsDB() { return getSimpleDB(); }
?>';
    
    file_put_contents('config/simple_db_config.php', $simple_config);
    echo '<p style="color: green;">✅ تم إنشاء ملف config/simple_db_config.php</p>';
    
    $mysqli->close();
    
    echo '<div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">';
    echo '<h2>✅ تم إعداد قاعدة البيانات بنجاح!</h2>';
    echo '<h3>بيانات تسجيل الدخول:</h3>';
    echo '<ul>';
    echo '<li><strong>المدير:</strong> admin / admin123</li>';
    echo '<li><strong>المشرف:</strong> supervisor / supervisor123</li>';
    echo '<li><strong>الموظف:</strong> employee / employee123</li>';
    echo '</ul>';
    echo '<h3>الخطوات التالية:</h3>';
    echo '<ol>';
    echo '<li><a href="update_login_handler.php">تحديث معالج تسجيل الدخول</a></li>';
    echo '<li><a href="work_management_login.php">اختبار تسجيل الدخول</a></li>';
    echo '</ol>';
    echo '</div>';
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 20px; border-radius: 10px;">';
    echo '<h2>❌ حدث خطأ:</h2>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<h3>الحلول المقترحة:</h3>';
    echo '<ul>';
    echo '<li>تأكد من تشغيل XAMPP</li>';
    echo '<li>تأكد من تشغيل خدمة MySQL</li>';
    echo '<li>تحقق من إعدادات قاعدة البيانات</li>';
    echo '</ul>';
    echo '</div>';
}
?>
