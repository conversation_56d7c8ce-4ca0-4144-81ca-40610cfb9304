<?php
// ملف اختبار بسيط لكشف الحساب
require_once __DIR__ . "/config/init.php";

echo "<h1>اختبار كشف الحساب البسيط</h1>";

try {
    $db = getUnifiedDB();
    if (!$db) {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    
    $username = $_SESSION["username"] ?? "testuser";
    $user_id = $_SESSION["user_id"] ?? 1;
    
    $sales_table = getUserTableName("sales", $username);
    $purchases_table = getUserTableName("purchases", $username);
    
    $sales_count = $db->query("SELECT COUNT(*) as count FROM `$sales_table` WHERE user_id = $user_id")->fetch_assoc()["count"];
    $purchases_count = $db->query("SELECT COUNT(*) as count FROM `$purchases_table` WHERE user_id = $user_id")->fetch_assoc()["count"];
    
    echo "<p>المبيعات: $sales_count</p>";
    echo "<p>المشتريات: $purchases_count</p>";
    echo "<p>الإجمالي: " . ($sales_count + $purchases_count) . "</p>";
    
    if ($sales_count == 3 && $purchases_count == 1) {
        echo "<p style=\"color: green; font-weight: bold;\">✅ النتائج صحيحة!</p>";
    } else {
        echo "<p style=\"color: red; font-weight: bold;\">❌ النتائج غير صحيحة</p>";
    }
    
} catch (Exception $e) {
    echo "<p style=\"color: red;\">خطأ: " . $e->getMessage() . "</p>";
}
?>