<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول البسيط</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            width: 100%;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .btn-test {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        
        .btn-test:hover {
            background: #0056b3;
        }
        
        .loading {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn-loading .loading {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="text-center mb-4">اختبار تسجيل الدخول البسيط</h2>
        
        <form id="loginForm">
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" name="username" class="form-control" value="supervisor" required>
            </div>
            
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" name="password" class="form-control" value="supervisor123" required>
            </div>
            
            <div class="form-group">
                <label>القسم:</label>
                <select name="department" class="form-control" required>
                    <option value="">اختر القسم</option>
                    <option value="employees">الموظفين</option>
                    <option value="supervisors" selected>المشرفين</option>
                    <option value="management">الإدارة</option>
                </select>
            </div>
            
            <button type="submit" class="btn-test" id="submitBtn">
                <span class="btn-text">تسجيل الدخول</span>
                <span class="loading"></span>
            </button>
        </form>
        
        <div class="mt-3">
            <h5>اختبارات أخرى:</h5>
            <button onclick="testAjax()" class="btn btn-secondary btn-sm">اختبار AJAX</button>
            <button onclick="testFetch()" class="btn btn-info btn-sm">اختبار Fetch</button>
            <button onclick="testConsole()" class="btn btn-warning btn-sm">اختبار Console</button>
        </div>
        
        <div id="results" class="mt-3"></div>
    </div>

    <script>
        // اختبار تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const form = e.target;
            const submitBtn = document.getElementById('submitBtn');
            const username = form.username.value.trim();
            const password = form.password.value.trim();
            const department = form.department.value.trim();
            
            console.log('بدء تسجيل الدخول:', { username, password, department });
            
            // التحقق من البيانات
            if (!username || !password || !department) {
                Swal.fire({
                    icon: 'warning',
                    title: 'بيانات ناقصة',
                    text: 'يرجى إدخال جميع البيانات المطلوبة'
                });
                return;
            }
            
            // إضافة حالة التحميل
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;
            
            try {
                // إنشاء FormData
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                formData.append('department', department);
                formData.append('ajax', '1');
                
                console.log('إرسال البيانات:', {
                    username: formData.get('username'),
                    password: formData.get('password'),
                    department: formData.get('department'),
                    ajax: formData.get('ajax')
                });
                
                // إرسال الطلب
                const response = await fetch('work_login_handler.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('استجابة الخادم:', response);
                console.log('حالة الاستجابة:', response.status);
                console.log('نوع المحتوى:', response.headers.get('content-type'));
                
                // قراءة النص أولاً
                const responseText = await response.text();
                console.log('نص الاستجابة:', responseText);
                
                // محاولة تحويل إلى JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('JSON المحول:', result);
                } catch (jsonError) {
                    console.error('خطأ في تحويل JSON:', jsonError);
                    throw new Error('استجابة غير صحيحة من الخادم: ' + responseText);
                }
                
                if (result.success) {
                    await Swal.fire({
                        icon: 'success',
                        title: 'تم تسجيل الدخول بنجاح',
                        text: result.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    console.log('التوجه إلى:', result.redirect);
                    window.location.href = result.redirect;
                    
                } else {
                    await Swal.fire({
                        icon: 'error',
                        title: 'خطأ في تسجيل الدخول',
                        text: result.message
                    });
                }
                
            } catch (error) {
                console.error('خطأ في الطلب:', error);
                
                await Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ: ' + error.message
                });
                
            } finally {
                // إزالة حالة التحميل
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
            }
        });
        
        // اختبار AJAX
        function testAjax() {
            console.log('اختبار AJAX...');
            
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'work_login_handler.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    console.log('AJAX Status:', xhr.status);
                    console.log('AJAX Response:', xhr.responseText);
                    
                    document.getElementById('results').innerHTML = 
                        '<h6>نتيجة AJAX:</h6><pre>' + xhr.responseText + '</pre>';
                }
            };
            
            xhr.send('username=supervisor&password=supervisor123&department=supervisors&ajax=1');
        }
        
        // اختبار Fetch
        async function testFetch() {
            console.log('اختبار Fetch...');
            
            try {
                const response = await fetch('work_login_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=supervisor&password=supervisor123&department=supervisors&ajax=1'
                });
                
                const text = await response.text();
                console.log('Fetch Response:', text);
                
                document.getElementById('results').innerHTML = 
                    '<h6>نتيجة Fetch:</h6><pre>' + text + '</pre>';
                    
            } catch (error) {
                console.error('Fetch Error:', error);
                document.getElementById('results').innerHTML = 
                    '<h6>خطأ Fetch:</h6><pre>' + error.message + '</pre>';
            }
        }
        
        // اختبار Console
        function testConsole() {
            console.log('اختبار Console...');
            console.log('المتصفح:', navigator.userAgent);
            console.log('الموقع:', window.location.href);
            console.log('Fetch متاح:', typeof fetch !== 'undefined');
            console.log('XMLHttpRequest متاح:', typeof XMLHttpRequest !== 'undefined');
            
            document.getElementById('results').innerHTML = 
                '<h6>معلومات المتصفح:</h6>' +
                '<p>المتصفح: ' + navigator.userAgent + '</p>' +
                '<p>الموقع: ' + window.location.href + '</p>' +
                '<p>Fetch متاح: ' + (typeof fetch !== 'undefined') + '</p>' +
                '<p>XMLHttpRequest متاح: ' + (typeof XMLHttpRequest !== 'undefined') + '</p>';
        }
    </script>
</body>
</html>
