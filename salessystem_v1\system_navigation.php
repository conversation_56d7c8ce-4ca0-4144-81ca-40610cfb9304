<?php
/**
 * صفحة التنقل بين أقسام النظام
 */
session_start();

// التحقق من وجود أي جلسة نشطة
$user_logged_in = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'];
$admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'];
$supervisor_logged_in = isset($_SESSION['supervisor_logged_in']) && $_SESSION['supervisor_logged_in'];

// جلب معلومات المستخدم النشط
$current_user = '';
$current_role = '';

if ($user_logged_in) {
    $current_user = $_SESSION['username'] ?? 'موظف';
    $current_role = 'موظف';
} elseif ($admin_logged_in) {
    $current_user = $_SESSION['admin_username'] ?? 'مدير';
    $current_role = 'مدير';
} elseif ($supervisor_logged_in) {
    $current_user = $_SESSION['supervisor_username'] ?? 'مشرف';
    $current_role = 'مشرف';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التنقل بين أقسام النظام</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .navigation-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            max-width: 800px;
            width: 100%;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .user-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: inline-block;
        }

        .sections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .section-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            border: 2px solid transparent;
        }

        .section-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }

        .section-card.employees {
            border-color: #667eea;
        }

        .section-card.supervisors {
            border-color: #43e97b;
        }

        .section-card.management {
            border-color: #fa709a;
        }

        .section-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .employees .section-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .supervisors .section-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .management .section-icon {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .section-description {
            color: #7f8c8d;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .access-status {
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .access-allowed {
            background: #d4edda;
            color: #155724;
        }

        .access-restricted {
            background: #f8d7da;
            color: #721c24;
        }

        .logout-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="navigation-container">
        <h1 class="page-title">
            <i class="fas fa-sitemap me-3"></i>
            التنقل بين أقسام النظام
        </h1>

        <?php if ($current_user): ?>
        <div class="user-info">
            <i class="fas fa-user me-2"></i>
            مرحباً <strong><?php echo htmlspecialchars($current_user); ?></strong> - <?php echo $current_role; ?>
        </div>
        <?php endif; ?>

        <div class="sections-grid">
            <!-- قسم الموظفين -->
            <a href="index.php" class="section-card employees">
                <div class="section-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="section-title">نظام المبيعات والمشتريات</h3>
                <p class="section-description">
                    إدارة العمليات اليومية، المبيعات، المشتريات، العملاء والموردين
                </p>
                <div class="access-status <?php echo $user_logged_in ? 'access-allowed' : 'access-restricted'; ?>">
                    <?php echo $user_logged_in ? 'متاح - أنت مسجل دخول' : 'يتطلب تسجيل دخول كموظف'; ?>
                </div>
            </a>

            <!-- قسم المشرفين -->
            <a href="supervisor_dashboard.php" class="section-card supervisors">
                <div class="section-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <h3 class="section-title">لوحة تحكم المشرفين</h3>
                <p class="section-description">
                    مراقبة العمليات، التقارير المتقدمة، سجل الأنشطة والإحصائيات
                </p>
                <div class="access-status <?php echo $supervisor_logged_in ? 'access-allowed' : 'access-restricted'; ?>">
                    <?php echo $supervisor_logged_in ? 'متاح - أنت مسجل دخول' : 'يتطلب تسجيل دخول كمشرف'; ?>
                </div>
            </a>

            <!-- قسم الإدارة -->
            <a href="admin_dashboard.php" class="section-card management">
                <div class="section-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <h3 class="section-title">لوحة تحكم الإدارة</h3>
                <p class="section-description">
                    التحكم الكامل، إدارة المستخدمين، إعدادات النظام والصلاحيات
                </p>
                <div class="access-status <?php echo $admin_logged_in ? 'access-allowed' : 'access-restricted'; ?>">
                    <?php echo $admin_logged_in ? 'متاح - أنت مسجل دخول' : 'يتطلب تسجيل دخول كمدير'; ?>
                </div>
            </a>
        </div>

        <div class="logout-section">
            <p class="text-muted mb-3">تحتاج لتسجيل دخول مختلف؟</p>
            <a href="work_management_login.php" class="btn btn-primary btn-lg">
                <i class="fas fa-sign-in-alt me-2"></i>
                صفحة تسجيل الدخول
            </a>
            
            <?php if ($current_user): ?>
            <a href="<?php 
                if ($user_logged_in) echo 'logout.php';
                elseif ($admin_logged_in) echo 'admin_logout.php';
                elseif ($supervisor_logged_in) echo 'supervisor_logout.php';
            ?>" class="btn btn-outline-danger btn-lg ms-2">
                <i class="fas fa-sign-out-alt me-2"></i>
                تسجيل الخروج
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات الظهور
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.section-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 * (index + 1));
            });
        });
    </script>
</body>
</html>
