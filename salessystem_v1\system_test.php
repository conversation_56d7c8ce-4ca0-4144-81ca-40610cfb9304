<?php
/**
 * اختبار شامل للنظام
 */
require_once 'config/simple_db_config.php';

echo '<h1>اختبار شامل للنظام</h1>';

// 1. اختبار قاعدة البيانات
echo '<h2>1. اختبار قاعدة البيانات</h2>';
$db = getSimpleDB();
if ($db) {
    echo '<p style="color: green;">✅ الاتصال بقاعدة البيانات: نجح</p>';
    
    // إنشاء الجداول والبيانات الأولية
    createUnifiedTables($db);
    insertInitialData($db);
    
    // التحقق من الجداول المطلوبة
    $required_tables = ['users', 'admins', 'supervisors', 'customers', 'products', 'sales', 'purchases'];
    foreach ($required_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ جدول $table: موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول $table: غير موجود</p>";
        }
    }
} else {
    echo '<p style="color: red;">❌ الاتصال بقاعدة البيانات: فشل</p>';
}

// 2. اختبار الملفات الأساسية
echo '<h2>2. اختبار الملفات الأساسية</h2>';
$required_files = [
    'work_management_login.php' => 'صفحة تسجيل الدخول',
    'work_login_handler.php' => 'معالج تسجيل الدخول',
    'index.php' => 'نظام المبيعات',
    'admin_dashboard.php' => 'لوحة تحكم المدير',
    'supervisor_dashboard.php' => 'لوحة تحكم المشرفين',
    'supervisor_reports.php' => 'تقارير المشرفين',
    'supervisor_financial.php' => 'التقارير المالية للمشرفين',
    'supervisor_activity.php' => 'سجل العمليات للمشرفين',
    'supervisor_logout.php' => 'تسجيل خروج المشرفين',
    'system_navigation.php' => 'صفحة التنقل المركزية'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description ($file): موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file): غير موجود</p>";
    }
}

// 3. اختبار ملفات المشرفين
echo '<h2>3. اختبار ملفات المشرفين</h2>';
$supervisor_files = [
    'includes/supervisor_header.php' => 'رأس صفحة المشرفين',
    'includes/supervisor_sidebar.php' => 'الشريط الجانبي للمشرفين',
    'includes/supervisor_footer.php' => 'تذييل صفحة المشرفين'
];

foreach ($supervisor_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description ($file): موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file): غير موجود</p>";
    }
}

// 4. اختبار بيانات تسجيل الدخول
echo '<h2>4. اختبار بيانات تسجيل الدخول</h2>';
if ($db) {
    // التحقق من المدير الرئيسي
    $admin_result = $db->query("SELECT * FROM admins WHERE username = 'admin' AND role = 'super_admin'");
    if ($admin_result && $admin_result->num_rows > 0) {
        echo '<p style="color: green;">✅ المدير الرئيسي: موجود (admin / admin123)</p>';
    } else {
        echo '<p style="color: red;">❌ المدير الرئيسي: غير موجود</p>';
    }
    
    // التحقق من المشرف التجريبي
    $supervisor_result = $db->query("SELECT * FROM supervisors WHERE username = 'supervisor'");
    if ($supervisor_result && $supervisor_result->num_rows > 0) {
        echo '<p style="color: green;">✅ المشرف التجريبي: موجود (supervisor / supervisor123)</p>';
    } else {
        echo '<p style="color: red;">❌ المشرف التجريبي: غير موجود</p>';
        // إنشاء المشرف التجريبي
        createDefaultSupervisor($db);
        echo '<p style="color: orange;">⚠️ تم إنشاء المشرف التجريبي</p>';
    }
    
    // التحقق من وجود مستخدمين
    $users_result = $db->query("SELECT COUNT(*) as count FROM users");
    if ($users_result) {
        $users_count = $users_result->fetch_assoc()['count'];
        echo "<p style='color: blue;'>ℹ️ عدد المستخدمين: $users_count</p>";
    }
}

// 5. اختبار الروابط
echo '<h2>5. اختبار الروابط</h2>';
$test_urls = [
    'work_management_login.php' => 'صفحة تسجيل الدخول',
    'system_navigation.php' => 'صفحة التنقل المركزية'
];

foreach ($test_urls as $url => $description) {
    echo "<p><a href='$url' target='_blank' style='color: blue;'>🔗 اختبار $description</a></p>";
}

// 6. ملخص الحالة
echo '<h2>6. ملخص الحالة</h2>';
echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">';
echo '<h3>بيانات تسجيل الدخول:</h3>';
echo '<ul>';
echo '<li><strong>المدير:</strong> admin / admin123</li>';
echo '<li><strong>المشرف:</strong> supervisor / supervisor123</li>';
echo '<li><strong>الموظفين:</strong> يحتاج إنشاء مستخدمين في نظام المبيعات</li>';
echo '</ul>';

echo '<h3>الصفحات الرئيسية:</h3>';
echo '<ul>';
echo '<li><strong>تسجيل الدخول:</strong> work_management_login.php</li>';
echo '<li><strong>التنقل المركزي:</strong> system_navigation.php</li>';
echo '<li><strong>نظام المبيعات:</strong> index.php</li>';
echo '<li><strong>لوحة المدير:</strong> admin_dashboard.php</li>';
echo '<li><strong>لوحة المشرفين:</strong> supervisor_dashboard.php</li>';
echo '</ul>';

echo '<h3>المزايا المطبقة:</h3>';
echo '<ul>';
echo '<li>✅ تسجيل دخول تفاعلي من البطاقات المنسدلة</li>';
echo '<li>✅ ثلاثة مستويات إدارية (موظفين، مشرفين، إدارة)</li>';
echo '<li>✅ صلاحيات منفصلة لكل مستوى</li>';
echo '<li>✅ تأثيرات بصرية متقدمة</li>';
echo '<li>✅ تنقل سلس بين الأقسام</li>';
echo '<li>✅ نظام AJAX للتسجيل الفوري</li>';
echo '</ul>';
echo '</div>';

echo '<p style="text-align: center; margin-top: 30px;">';
echo '<a href="work_management_login.php" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-size: 18px;">🚀 بدء استخدام النظام</a>';
echo '</p>';
?>
