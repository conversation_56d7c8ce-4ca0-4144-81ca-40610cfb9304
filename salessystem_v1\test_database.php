<?php
/**
 * اختبار قاعدة البيانات والتحقق من الجداول
 */
require_once 'config/simple_db_config.php';

echo '<h2>اختبار قاعدة البيانات</h2>';

// اختبار الاتصال
$db = getSimpleDB();
if (!$db) {
    echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
    exit;
}

echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات بنجاح</p>';

// إنشاء الجداول
echo '<h3>إنشاء الجداول:</h3>';
if (createUnifiedTables($db)) {
    echo '<p style="color: green;">✅ تم إنشاء الجداول بنجاح</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء الجداول</p>';
}

// إنشاء البيانات الأولية
echo '<h3>إنشاء البيانات الأولية:</h3>';
if (insertInitialData($db)) {
    echo '<p style="color: green;">✅ تم إنشاء البيانات الأولية بنجاح</p>';
} else {
    echo '<p style="color: orange;">⚠️ البيانات الأولية موجودة مسبقاً</p>';
}

// التحقق من الجداول
echo '<h3>التحقق من الجداول:</h3>';
$tables = ['users', 'admins', 'supervisors', 'customers', 'products', 'sales', 'purchases'];

foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ جدول $table موجود</p>";
        
        // عد الصفوف
        $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p style='margin-left: 20px;'>عدد الصفوف: $count</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
    }
}

// التحقق من المشرف التجريبي
echo '<h3>التحقق من المشرف التجريبي:</h3>';
$result = $db->query("SELECT * FROM supervisors WHERE username = 'supervisor'");
if ($result && $result->num_rows > 0) {
    $supervisor = $result->fetch_assoc();
    echo '<p style="color: green;">✅ المشرف التجريبي موجود</p>';
    echo '<ul>';
    echo '<li>اسم المستخدم: ' . htmlspecialchars($supervisor['username']) . '</li>';
    echo '<li>الاسم الكامل: ' . htmlspecialchars($supervisor['full_name']) . '</li>';
    echo '<li>البريد الإلكتروني: ' . htmlspecialchars($supervisor['email']) . '</li>';
    echo '<li>القسم: ' . htmlspecialchars($supervisor['department']) . '</li>';
    echo '<li>الحالة: ' . htmlspecialchars($supervisor['status']) . '</li>';
    echo '</ul>';
} else {
    echo '<p style="color: red;">❌ المشرف التجريبي غير موجود - سيتم إنشاؤه</p>';
    if (createDefaultSupervisor($db)) {
        echo '<p style="color: green;">✅ تم إنشاء المشرف التجريبي بنجاح</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء المشرف التجريبي</p>';
    }
}

// التحقق من المدير الرئيسي
echo '<h3>التحقق من المدير الرئيسي:</h3>';
$result = $db->query("SELECT * FROM admins WHERE role = 'super_admin'");
if ($result && $result->num_rows > 0) {
    $admin = $result->fetch_assoc();
    echo '<p style="color: green;">✅ المدير الرئيسي موجود</p>';
    echo '<ul>';
    echo '<li>اسم المستخدم: ' . htmlspecialchars($admin['username']) . '</li>';
    echo '<li>الاسم الكامل: ' . htmlspecialchars($admin['full_name']) . '</li>';
    echo '<li>البريد الإلكتروني: ' . htmlspecialchars($admin['email']) . '</li>';
    echo '<li>الدور: ' . htmlspecialchars($admin['role']) . '</li>';
    echo '<li>الحالة: ' . htmlspecialchars($admin['status']) . '</li>';
    echo '</ul>';
} else {
    echo '<p style="color: red;">❌ المدير الرئيسي غير موجود</p>';
}

echo '<h3>اختبار تسجيل الدخول:</h3>';
echo '<p><a href="work_management_login.php" style="color: blue;">اختبار صفحة تسجيل الدخول</a></p>';
echo '<p>بيانات تسجيل الدخول:</p>';
echo '<ul>';
echo '<li><strong>المدير:</strong> admin / admin123</li>';
echo '<li><strong>المشرف:</strong> supervisor / supervisor123</li>';
echo '</ul>';
?>
