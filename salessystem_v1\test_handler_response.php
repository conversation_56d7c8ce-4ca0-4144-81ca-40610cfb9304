<?php
/**
 * اختبار استجابة معالج تسجيل الدخول
 */

echo '<h1>🧪 اختبار استجابة معالج تسجيل الدخول</h1>';

// محاكاة طلب POST
$_POST['username'] = 'supervisor';
$_POST['password'] = 'supervisor123';
$_POST['department'] = 'supervisors';
$_POST['ajax'] = '1';
$_SERVER['REQUEST_METHOD'] = 'POST';

echo '<h2>البيانات المرسلة:</h2>';
echo '<pre>';
print_r($_POST);
echo '</pre>';

// تشغيل المعالج وجمع الاستجابة
ob_start();
include 'work_login_handler_simple.php';
$response = ob_get_clean();

echo '<h2>استجابة المعالج:</h2>';
echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">';
echo '<h3>النص الخام:</h3>';
echo '<pre style="background: #e9ecef; padding: 10px; border-radius: 4px;">';
echo htmlspecialchars($response);
echo '</pre>';

// محاولة تحويل إلى JSON
echo '<h3>تحليل JSON:</h3>';
$json_data = json_decode($response, true);
if ($json_data) {
    echo '<pre style="background: #d4edda; padding: 10px; border-radius: 4px;">';
    print_r($json_data);
    echo '</pre>';
    
    if (isset($json_data['success']) && $json_data['success']) {
        echo '<h3>✅ تسجيل الدخول ناجح</h3>';
        echo '<ul>';
        echo '<li><strong>الرسالة:</strong> ' . htmlspecialchars($json_data['message']) . '</li>';
        if (isset($json_data['data'])) {
            echo '<li><strong>البيانات:</strong></li>';
            echo '<ul>';
            foreach ($json_data['data'] as $key => $value) {
                echo '<li>' . htmlspecialchars($key) . ': ' . htmlspecialchars($value) . '</li>';
            }
            echo '</ul>';
        }
        echo '</ul>';
        
        // اختبار صفحة التوجه
        $redirect = $json_data['data']['redirect'] ?? 'غير محدد';
        echo '<h3>اختبار صفحة التوجه:</h3>';
        echo '<p><strong>الصفحة:</strong> ' . htmlspecialchars($redirect) . '</p>';
        
        if ($redirect && $redirect !== 'غير محدد') {
            if (file_exists($redirect)) {
                echo '<p style="color: green;">✅ الصفحة موجودة</p>';
                echo '<p><a href="' . htmlspecialchars($redirect) . '" target="_blank" style="background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;">فتح الصفحة</a></p>';
            } else {
                echo '<p style="color: red;">❌ الصفحة غير موجودة</p>';
            }
        } else {
            echo '<p style="color: red;">❌ صفحة التوجه غير محددة</p>';
        }
        
    } else {
        echo '<h3>❌ فشل تسجيل الدخول</h3>';
        echo '<p><strong>الرسالة:</strong> ' . htmlspecialchars($json_data['message'] ?? 'غير محدد') . '</p>';
    }
} else {
    echo '<p style="color: red;">❌ فشل في تحويل JSON</p>';
    echo '<p><strong>خطأ JSON:</strong> ' . json_last_error_msg() . '</p>';
}

echo '</div>';

// اختبار مباشر للمعالج عبر cURL
echo '<h2>اختبار cURL:</h2>';

$curl_data = [
    'username' => 'supervisor',
    'password' => 'supervisor123', 
    'department' => 'supervisors',
    'ajax' => '1'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:808/salessystem_v2/work_login_handler_simple.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($curl_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);

$curl_response = curl_exec($ch);
$curl_error = curl_error($ch);
curl_close($ch);

if ($curl_error) {
    echo '<p style="color: red;">❌ خطأ cURL: ' . htmlspecialchars($curl_error) . '</p>';
} else {
    echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">';
    echo '<h3>استجابة cURL:</h3>';
    echo '<pre style="background: #e9ecef; padding: 10px; border-radius: 4px; font-size: 12px;">';
    echo htmlspecialchars($curl_response);
    echo '</pre>';
    echo '</div>';
}

// JavaScript لاختبار AJAX
echo '<h2>اختبار JavaScript AJAX:</h2>';
?>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px;">
    <button onclick="testJavaScriptAjax()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">اختبار JavaScript</button>
    <div id="js-result" style="margin-top: 15px;"></div>
</div>

<script>
async function testJavaScriptAjax() {
    const resultDiv = document.getElementById('js-result');
    resultDiv.innerHTML = '<p>جاري الاختبار...</p>';
    
    try {
        const formData = new FormData();
        formData.append('username', 'supervisor');
        formData.append('password', 'supervisor123');
        formData.append('department', 'supervisors');
        formData.append('ajax', '1');
        
        console.log('إرسال طلب AJAX...');
        
        const response = await fetch('work_login_handler_simple.php', {
            method: 'POST',
            body: formData
        });
        
        console.log('استجابة HTTP:', response.status, response.statusText);
        
        const responseText = await response.text();
        console.log('نص الاستجابة:', responseText);
        
        let result;
        try {
            result = JSON.parse(responseText);
            console.log('JSON المحول:', result);
        } catch (jsonError) {
            console.error('خطأ JSON:', jsonError);
            resultDiv.innerHTML = `
                <div style="color: red;">
                    <h4>❌ خطأ في تحويل JSON</h4>
                    <p><strong>الخطأ:</strong> ${jsonError.message}</p>
                    <pre style="background: #f8d7da; padding: 10px; border-radius: 4px;">${responseText}</pre>
                </div>
            `;
            return;
        }
        
        if (result.success) {
            const redirectPage = result.data && result.data.redirect ? result.data.redirect : result.redirect;
            
            resultDiv.innerHTML = `
                <div style="color: green;">
                    <h4>✅ نجح الاختبار!</h4>
                    <p><strong>الرسالة:</strong> ${result.message}</p>
                    <p><strong>صفحة التوجه:</strong> ${redirectPage || 'غير محدد'}</p>
                    <p><strong>القسم:</strong> ${result.data.department}</p>
                    <p><strong>اسم المستخدم:</strong> ${result.data.username}</p>
                    ${redirectPage ? `<p><a href="${redirectPage}" target="_blank" style="background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;">فتح الصفحة</a></p>` : ''}
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style="color: red;">
                    <h4>❌ فشل الاختبار</h4>
                    <p><strong>الرسالة:</strong> ${result.message}</p>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('خطأ في الطلب:', error);
        resultDiv.innerHTML = `
            <div style="color: red;">
                <h4>❌ خطأ في الطلب</h4>
                <p><strong>الخطأ:</strong> ${error.message}</p>
            </div>
        `;
    }
}
</script>

<?php
echo '<h2>روابط مفيدة:</h2>';
echo '<div style="display: flex; gap: 10px; flex-wrap: wrap;">';
echo '<a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">صفحة تسجيل الدخول</a>';
echo '<a href="debug_redirect.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">تشخيص التوجه</a>';
echo '<a href="supervisor_dashboard.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;">لوحة المشرفين</a>';
echo '</div>';
?>
