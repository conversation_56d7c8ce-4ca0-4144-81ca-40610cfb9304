<?php
/**
 * اختبار تسجيل الدخول
 */
session_start();
require_once 'config/simple_db_config.php';

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo '<h2>اختبار تسجيل الدخول</h2>';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo '<h3>البيانات المرسلة:</h3>';
    echo '<pre>';
    print_r($_POST);
    echo '</pre>';
    
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] === '1';
    
    echo '<h3>البيانات المعالجة:</h3>';
    echo '<ul>';
    echo '<li>اسم المستخدم: ' . htmlspecialchars($username) . '</li>';
    echo '<li>كلمة المرور: ' . htmlspecialchars($password) . '</li>';
    echo '<li>القسم: ' . htmlspecialchars($department) . '</li>';
    echo '<li>AJAX: ' . ($is_ajax ? 'نعم' : 'لا') . '</li>';
    echo '</ul>';
    
    if (empty($username) || empty($password) || empty($department)) {
        echo '<p style="color: red;">❌ جميع الحقول مطلوبة</p>';
        exit;
    }
    
    // تحديد الجدول والصفحة حسب القسم
    $table = '';
    $redirect_page = '';
    $session_prefix = '';
    
    switch ($department) {
        case 'employees':
            $table = 'users';
            $redirect_page = 'index.php';
            $session_prefix = 'user_';
            break;
        case 'supervisors':
            $table = 'supervisors';
            $redirect_page = 'supervisor_dashboard.php';
            $session_prefix = 'supervisor_';
            break;
        case 'management':
            $table = 'admins';
            $redirect_page = 'admin_dashboard.php';
            $session_prefix = 'admin_';
            break;
        default:
            echo '<p style="color: red;">❌ قسم غير صحيح</p>';
            exit;
    }
    
    echo '<h3>إعدادات القسم:</h3>';
    echo '<ul>';
    echo '<li>الجدول: ' . htmlspecialchars($table) . '</li>';
    echo '<li>صفحة التوجه: ' . htmlspecialchars($redirect_page) . '</li>';
    echo '<li>بادئة الجلسة: ' . htmlspecialchars($session_prefix) . '</li>';
    echo '</ul>';
    
    // اختبار الاتصال بقاعدة البيانات
    $db = getSimpleDB();
    if (!$db) {
        echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
        exit;
    }
    
    echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات</p>';
    
    // البحث عن المستخدم
    $query = "SELECT * FROM $table WHERE username = ? AND status = 'active'";
    echo '<h3>استعلام البحث:</h3>';
    echo '<p>' . htmlspecialchars($query) . '</p>';
    
    $stmt = $db->prepare($query);
    if (!$stmt) {
        echo '<p style="color: red;">❌ فشل في تحضير الاستعلام: ' . htmlspecialchars($db->error) . '</p>';
        exit;
    }
    
    $stmt->bind_param('s', $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo '<h3>نتيجة البحث:</h3>';
    echo '<p>عدد النتائج: ' . $result->num_rows . '</p>';
    
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        echo '<h4>بيانات المستخدم:</h4>';
        echo '<ul>';
        foreach ($user as $key => $value) {
            if ($key !== 'password') {
                echo '<li>' . htmlspecialchars($key) . ': ' . htmlspecialchars($value) . '</li>';
            }
        }
        echo '</ul>';
        
        // التحقق من كلمة المرور
        if (password_verify($password, $user['password'])) {
            echo '<p style="color: green;">✅ كلمة المرور صحيحة</p>';
            
            // تسجيل الدخول
            if ($department === 'employees') {
                $_SESSION['user_logged_in'] = true;
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_name'] = $user['name'] ?? $user['full_name'] ?? $username;
            } else {
                $_SESSION[$session_prefix . 'logged_in'] = true;
                $_SESSION[$session_prefix . 'id'] = $user['id'];
                $_SESSION[$session_prefix . 'username'] = $user['username'];
                $_SESSION[$session_prefix . 'name'] = $user['name'] ?? $user['full_name'] ?? $username;
                $_SESSION[$session_prefix . 'department'] = $department;
                $_SESSION[$session_prefix . 'role'] = $user['role'] ?? $department;
            }
            
            echo '<h4>الجلسة:</h4>';
            echo '<pre>';
            print_r($_SESSION);
            echo '</pre>';
            
            if ($is_ajax) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true, 
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'redirect' => $redirect_page,
                    'department' => $department,
                    'username' => $username
                ]);
                exit;
            } else {
                echo '<p style="color: green;">✅ تم تسجيل الدخول بنجاح</p>';
                echo '<p><a href="' . htmlspecialchars($redirect_page) . '">الانتقال للصفحة الرئيسية</a></p>';
            }
            
        } else {
            echo '<p style="color: red;">❌ كلمة المرور غير صحيحة</p>';
        }
    } else {
        echo '<p style="color: red;">❌ اسم المستخدم غير موجود أو الحساب غير نشط</p>';
    }
    
} else {
    // عرض نموذج الاختبار
    echo '<form method="POST">';
    echo '<h3>اختبار تسجيل الدخول:</h3>';
    echo '<p>';
    echo '<label>اسم المستخدم:</label><br>';
    echo '<input type="text" name="username" value="supervisor" required>';
    echo '</p>';
    echo '<p>';
    echo '<label>كلمة المرور:</label><br>';
    echo '<input type="password" name="password" value="supervisor123" required>';
    echo '</p>';
    echo '<p>';
    echo '<label>القسم:</label><br>';
    echo '<select name="department" required>';
    echo '<option value="">اختر القسم</option>';
    echo '<option value="employees">الموظفين</option>';
    echo '<option value="supervisors" selected>المشرفين</option>';
    echo '<option value="management">الإدارة</option>';
    echo '</select>';
    echo '</p>';
    echo '<p>';
    echo '<label><input type="checkbox" name="ajax" value="1"> طلب AJAX</label>';
    echo '</p>';
    echo '<p>';
    echo '<button type="submit">اختبار تسجيل الدخول</button>';
    echo '</p>';
    echo '</form>';
}
?>
