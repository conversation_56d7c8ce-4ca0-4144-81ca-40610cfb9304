<?php
/**
 * اختبار الجلسات والتوجيه
 */
session_start();

echo '<h1>🔍 اختبار الجلسات والتوجيه</h1>';

// عرض جميع متغيرات الجلسة
echo '<h2>متغيرات الجلسة الحالية:</h2>';
if (!empty($_SESSION)) {
    echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">';
    echo '<pre>';
    print_r($_SESSION);
    echo '</pre>';
    echo '</div>';
} else {
    echo '<p style="color: red;">❌ لا توجد جلسة نشطة</p>';
}

// اختبار حالة تسجيل الدخول لكل قسم
echo '<h2>حالة تسجيل الدخول:</h2>';

$login_status = [
    'الموظفين' => isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'],
    'المشرفين' => isset($_SESSION['supervisor_logged_in']) && $_SESSION['supervisor_logged_in'],
    'الإدارة' => isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']
];

foreach ($login_status as $department => $status) {
    $color = $status ? 'green' : 'red';
    $icon = $status ? '✅' : '❌';
    echo "<p style='color: $color;'>$icon $department: " . ($status ? 'مسجل دخول' : 'غير مسجل') . "</p>";
}

// اختبار الوصول للصفحات
echo '<h2>اختبار الوصول للصفحات:</h2>';

$pages = [
    'index.php' => 'نظام المبيعات (الموظفين)',
    'supervisor_dashboard.php' => 'لوحة تحكم المشرفين',
    'admin_dashboard.php' => 'لوحة تحكم الإدارة'
];

foreach ($pages as $page => $description) {
    echo "<p><a href='$page' target='_blank' style='color: blue;'>🔗 اختبار $description</a></p>";
}

// نموذج تسجيل دخول سريع
echo '<h2>تسجيل دخول سريع:</h2>';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['quick_login'])) {
    $department = $_POST['department'];
    $username = $_POST['username'];
    
    // محاكاة تسجيل الدخول
    switch ($department) {
        case 'employees':
            $_SESSION['user_logged_in'] = true;
            $_SESSION['user_id'] = 1;
            $_SESSION['username'] = $username;
            $_SESSION['user_name'] = 'موظف تجريبي';
            echo '<p style="color: green;">✅ تم تسجيل دخول الموظف</p>';
            break;
        case 'supervisors':
            $_SESSION['supervisor_logged_in'] = true;
            $_SESSION['supervisor_id'] = 1;
            $_SESSION['supervisor_username'] = $username;
            $_SESSION['supervisor_name'] = 'مشرف تجريبي';
            $_SESSION['supervisor_department'] = 'supervisors';
            $_SESSION['supervisor_role'] = 'supervisor';
            echo '<p style="color: green;">✅ تم تسجيل دخول المشرف</p>';
            break;
        case 'management':
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = 1;
            $_SESSION['admin_username'] = $username;
            $_SESSION['admin_name'] = 'مدير تجريبي';
            $_SESSION['admin_department'] = 'management';
            $_SESSION['admin_role'] = 'admin';
            echo '<p style="color: green;">✅ تم تسجيل دخول المدير</p>';
            break;
    }
    
    echo '<p><a href="test_sessions.php">تحديث الصفحة لرؤية التغييرات</a></p>';
}

echo '<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 8px; max-width: 400px;">';
echo '<h3>تسجيل دخول تجريبي:</h3>';
echo '<div style="margin-bottom: 15px;">';
echo '<label>القسم:</label><br>';
echo '<select name="department" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
echo '<option value="">اختر القسم</option>';
echo '<option value="employees">الموظفين</option>';
echo '<option value="supervisors">المشرفين</option>';
echo '<option value="management">الإدارة</option>';
echo '</select>';
echo '</div>';
echo '<div style="margin-bottom: 15px;">';
echo '<label>اسم المستخدم:</label><br>';
echo '<input type="text" name="username" value="test_user" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
echo '</div>';
echo '<button type="submit" name="quick_login" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">تسجيل دخول تجريبي</button>';
echo '</form>';

// مسح الجلسة
if (isset($_POST['clear_session'])) {
    session_destroy();
    echo '<p style="color: orange;">⚠️ تم مسح الجلسة</p>';
    echo '<p><a href="test_sessions.php">تحديث الصفحة</a></p>';
}

echo '<form method="POST" style="margin-top: 20px;">';
echo '<button type="submit" name="clear_session" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">مسح الجلسة</button>';
echo '</form>';

// معلومات إضافية
echo '<h2>معلومات إضافية:</h2>';
echo '<ul>';
echo '<li>معرف الجلسة: ' . session_id() . '</li>';
echo '<li>اسم الجلسة: ' . session_name() . '</li>';
echo '<li>مسار الجلسة: ' . session_save_path() . '</li>';
echo '<li>الوقت الحالي: ' . date('Y-m-d H:i:s') . '</li>';
echo '</ul>';

// روابط مفيدة
echo '<h2>روابط مفيدة:</h2>';
echo '<div style="display: flex; gap: 10px; flex-wrap: wrap;">';
echo '<a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">صفحة تسجيل الدخول</a>';
echo '<a href="debug_login.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">تشخيص تسجيل الدخول</a>';
echo '<a href="simple_login_test.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;">اختبار بسيط</a>';
echo '</div>';
?>
