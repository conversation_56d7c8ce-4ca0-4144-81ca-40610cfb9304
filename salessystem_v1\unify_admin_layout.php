<?php
/**
 * توحيد تنسيق صفحات المدير
 */

// قائمة الصفحات التي تحتاج توحيد التنسيق
$files_to_fix = [
    'admin_dashboard.php',
    'admin_users.php'
];

echo "<h2>🎨 توحيد تنسيق صفحات المدير</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h4>🎯 المشكلة:</h4>";
echo "<p>صفحة لوحة التحكم وصفحة المستخدمين تستخدم تنسيق مختلف عن باقي الصفحات</p>";
echo "<h4>🔧 الحل:</h4>";
echo "<p>توحيد التنسيق ليطابق admin_activity.php وباقي الصفحات</p>";
echo "</div>";

$fixed_files = [];
$errors = [];

foreach ($files_to_fix as $file) {
    $file_path = __DIR__ . '/' . $file;
    
    echo "<h3>🔧 توحيد تنسيق: $file</h3>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    if (!file_exists($file_path)) {
        echo "<span style='color: orange;'>⚠️ الملف غير موجود</span><br>";
        echo "</div>";
        continue;
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        $errors[] = "فشل في قراءة الملف: $file";
        echo "<span style='color: red;'>❌ فشل في قراءة الملف</span><br>";
        echo "</div>";
        continue;
    }
    
    $original_content = $content;
    $changes_made = false;
    
    // إنشاء نسخة احتياطية
    $backup_path = $file_path . '.layout_backup.' . date('Y-m-d-H-i-s');
    copy($file_path, $backup_path);
    
    // 1. توحيد هيكل الشريط الجانبي
    $correct_sidebar = '<div class="admin-layout">
        <!-- الشريط الجانبي -->
        <nav class="modern-sidebar d-none d-lg-block">
            <div class="sidebar-section">
                <div class="sidebar-section-title">الإدارة الرئيسية</div>
                <a class="sidebar-nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a class="sidebar-nav-link" href="admin_users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a class="sidebar-nav-link" href="admin_activity.php">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">التقارير والإحصائيات</div>
                <a class="sidebar-nav-link" href="admin_reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير الشاملة</span>
                </a>
                <a class="sidebar-nav-link" href="admin_financial.php">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>التقارير المالية</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة النظام</div>
                <a class="sidebar-nav-link" href="admin_error_logs.php">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>سجل الأخطاء</span>
                </a>
                <a class="sidebar-nav-link" href="admin_system.php">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </div>
            <div class="sidebar-section">
                <div class="sidebar-section-title">إدارة المديرين</div>
                <a class="sidebar-nav-link" href="admin_manage_admins.php">
                    <i class="fas fa-user-shield"></i>
                    <span>إدارة المديرين</span>
                </a>
            </div>
        </nav>
        <!-- المحتوى الرئيسي -->
        <main class="admin-content">';
    
    // البحث عن الشريط الجانبي الحالي واستبداله
    $sidebar_patterns = [
        // النمط الأول - admin_dashboard.php
        '/<div class="row">\s*<!-- الشريط الجانبي المتطور -->\s*<nav class="modern-sidebar d-none d-lg-block">.*?<\/nav>\s*<!-- المحتوى الرئيسي المتطور -->\s*<main class="admin-content"[^>]*>/s',
        
        // النمط الثاني - admin_users.php  
        '/<div class="row">\s*<!-- الشريط الجانبي المتطور -->\s*<nav class="modern-sidebar d-none d-lg-block">.*?<\/nav>\s*<!-- المحتوى الرئيسي المتطور -->\s*<main class="admin-content">/s',
        
        // النمط العام
        '/<div class="admin-layout">\s*<div class="row">.*?<main class="admin-content"[^>]*>/s'
    ];
    
    foreach ($sidebar_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $correct_sidebar, $content);
            $changes_made = true;
            echo "<span style='color: green;'>✅ تم توحيد هيكل الشريط الجانبي</span><br>";
            break;
        }
    }
    
    // 2. إضافة class active للصفحة الحالية
    if ($file === 'admin_dashboard.php') {
        $content = str_replace(
            '<a class="sidebar-nav-link" href="admin_dashboard.php">',
            '<a class="sidebar-nav-link active" href="admin_dashboard.php">',
            $content
        );
    } elseif ($file === 'admin_users.php') {
        $content = str_replace(
            '<a class="sidebar-nav-link" href="admin_users.php">',
            '<a class="sidebar-nav-link active" href="admin_users.php">',
            $content
        );
    }
    
    // 3. توحيد تنسيق رأس الصفحة
    $header_fixes = [
        // إزالة المسافات الزائدة في رأس الصفحة
        '<div class="d-flex justify-content-between align-items-center mb-4 ">' => 
        '<div class="d-flex justify-content-between align-items-center mb-4">',
        
        // توحيد تنسيق العناوين
        '<h1 class="h2 mb-2 fw-bold gradient-text">' => 
        '<h1 class="h2 mb-2 fw-bold gradient-text">',
    ];
    
    foreach ($header_fixes as $wrong => $correct) {
        if (strpos($content, $wrong) !== false) {
            $content = str_replace($wrong, $correct, $content);
            $changes_made = true;
        }
    }
    
    // 4. توحيد تنسيق البطاقات
    $card_fixes = [
        // إزالة المسافات الزائدة في البطاقات
        '<div class="stats-card ">' => '<div class="stats-card">',
        '<div class="stats-card " >' => '<div class="stats-card">',
        '<div class="modern-card ">' => '<div class="modern-card">',
        '<div class="modern-card " >' => '<div class="modern-card">',
        '<div class="modern-card mb-4" >' => '<div class="modern-card mb-4">',
        
        // توحيد تنسيق الأيقونات
        '<div class="stats-icon" >' => '<div class="stats-icon">',
        '<div class="stats-icon">' => '<div class="stats-icon">',
    ];
    
    foreach ($card_fixes as $wrong => $correct) {
        if (strpos($content, $wrong) !== false) {
            $content = str_replace($wrong, $correct, $content);
            $changes_made = true;
        }
    }
    
    // 5. إصلاح مشاكل المصفوفات المكسورة
    $array_fixes = [
        // إصلاح المصفوفات المكسورة في admin_dashboard.php
        'echo number_format($users_stats[
    \'total_users\'
]);' => 'echo number_format($users_stats[\'total_users\']);',
        
        'echo number_format($users_stats[
    \'new_users_week\'
]);' => 'echo number_format($users_stats[\'new_users_week\']);',
        
        'echo number_format($users_stats[
    \'active_users\'
]);' => 'echo number_format($users_stats[\'active_users\']);',
        
        'echo number_format($users_stats[
    \'recent_users\'
]);' => 'echo number_format($users_stats[\'recent_users\']);',
        
        'echo number_format($today_activity[
    \'total_activities\'
]);' => 'echo number_format($today_activity[\'total_activities\']);',
        
        'echo number_format($today_activity[
    \'recent_activities\'
]);' => 'echo number_format($today_activity[\'recent_activities\']);',
        
        'echo $error_stats[
    \'critical_errors\'
] >
 0 ? \'تحتاج انتباه\' : \'مستقر\';' => 'echo $error_stats[\'critical_errors\'] > 0 ? \'تحتاج انتباه\' : \'مستقر\';',
        
        'echo $error_stats[
    \'total_errors\'
] >
 0 ? \'text-warning\' : \'text-success\';' => 'echo $error_stats[\'total_errors\'] > 0 ? \'text-warning\' : \'text-success\';',
        
        'echo number_format($error_stats[
    \'total_errors\'
]);' => 'echo number_format($error_stats[\'total_errors\']);',
        
        'echo number_format($financial_stats[
    \'today_sales\'
], 2);' => 'echo number_format($financial_stats[\'today_sales\'], 2);',
        
        'echo number_format($financial_stats[
    \'today_invoices\'
]);' => 'echo number_format($financial_stats[\'today_invoices\']);',
        
        'echo number_format($financial_stats[
    \'week_sales\'
], 2);' => 'echo number_format($financial_stats[\'week_sales\'], 2);',
        
        'echo number_format($financial_stats[
    \'week_invoices\'
]);' => 'echo number_format($financial_stats[\'week_invoices\']);',
        
        'echo number_format($error_stats[
    \'critical_errors\'
]);' => 'echo number_format($error_stats[\'critical_errors\']);',
        
        'echo number_format($error_stats[
    \'database_errors\'
]);' => 'echo number_format($error_stats[\'database_errors\']);',
        
        'echo number_format($error_stats[
    \'warnings\'
]);' => 'echo number_format($error_stats[\'warnings\']);',
    ];
    
    foreach ($array_fixes as $wrong => $correct) {
        if (strpos($content, $wrong) !== false) {
            $content = str_replace($wrong, $correct, $content);
            $changes_made = true;
        }
    }
    
    // 6. إصلاح نهاية الصفحة
    $footer_fixes = [
        '</main></div>' => '</main>
</div>',
        '</main>
</div>' => '</main>
</div>',
    ];
    
    foreach ($footer_fixes as $wrong => $correct) {
        if (strpos($content, $wrong) !== false) {
            $content = str_replace($wrong, $correct, $content);
            $changes_made = true;
        }
    }
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        if (file_put_contents($file_path, $content) !== false) {
            $fixed_files[] = $file;
            echo "<span style='color: green; font-weight: bold;'>✅ تم توحيد التنسيق بنجاح</span><br>";
            echo "<span style='color: blue;'>📁 نسخة احتياطية: " . basename($backup_path) . "</span><br>";
            
            // إحصائيات
            $old_lines = substr_count($original_content, "\n");
            $new_lines = substr_count($content, "\n");
            echo "<span style='color: gray;'>📊 الأسطر: $old_lines → $new_lines</span><br>";
        } else {
            $errors[] = "فشل في حفظ الملف: $file";
            echo "<span style='color: red;'>❌ فشل في حفظ الملف</span><br>";
        }
    } else {
        echo "<span style='color: blue;'>ℹ️ لا توجد تغييرات مطلوبة</span><br>";
    }
    
    echo "</div>";
}

// عرض الملخص
echo "<h3>📋 ملخص توحيد التنسيق</h3>";

if (!empty($fixed_files)) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>✅ الملفات المُوحدة:</h4>";
    foreach ($fixed_files as $file) {
        echo "• $file<br>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #ffebee; color: #d32f2f; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    foreach ($errors as $error) {
        echo "• $error<br>";
    }
    echo "</div>";
}

// رسالة النتيجة
if (count($fixed_files) > 0) {
    echo "<div style='background: #e8f5e8; color: #2e7d32; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>🎉 تم توحيد التنسيق بنجاح!</h3>";
    echo "<p>جميع صفحات المدير تستخدم الآن نفس التنسيق الموحد.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3e0; color: #f57c00; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;'>";
    echo "<h3>ℹ️ التنسيق موحد بالفعل</h3>";
    echo "<p>جميع الصفحات تستخدم نفس التنسيق.</p>";
    echo "</div>";
}

// روابط الاختبار
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<h4>🔗 اختبار الصفحات الموحدة:</h4>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;'>";

$test_pages = [
    'admin_dashboard.php' => 'لوحة التحكم',
    'admin_users.php' => 'إدارة المستخدمين',
    'admin_activity.php' => 'سجل العمليات',
    'admin_reports.php' => 'التقارير',
    'admin_error_logs.php' => 'سجل الأخطاء'
];

foreach ($test_pages as $page => $title) {
    $color = in_array($page, $fixed_files) ? '#4caf50' : '#2196f3';
    echo "<a href='$page' target='_blank' style='background: $color; color: white; padding: 10px 15px; text-decoration: none; border-radius: 8px; font-size: 14px; margin: 5px;'>$title</a>";
}
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='comprehensive_error_scanner.php' style='background: #ff9800; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🔍 فحص شامل</a>";
echo "<a href='admin_dashboard.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin: 10px;'>🏠 لوحة التحكم</a>";
echo "</div>";
?>
