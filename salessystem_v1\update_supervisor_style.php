<?php
/**
 * تحديث تصميم جميع صفحات المشرفين إلى التصميم العصري
 */

echo '<h1>🎨 تحديث تصميم قسم المشرفين</h1>';

// قائمة صفحات المشرفين
$supervisor_pages = [
    'supervisor_dashboard.php',
    'supervisor_reports.php',
    'supervisor_financial.php',
    'supervisor_activity.php'
];

echo '<h2>تحديث الصفحات:</h2>';

foreach ($supervisor_pages as $page) {
    if (file_exists($page)) {
        echo "<h3>تحديث $page:</h3>";
        
        $content = file_get_contents($page);
        $original_content = $content;
        $updated = false;
        
        // تحديث header
        if (strpos($content, 'supervisor_header.php') !== false) {
            $content = str_replace('supervisor_header.php', 'supervisor_header_modern.php', $content);
            $updated = true;
            echo "<p style='color: green;'>✅ تم تحديث header</p>";
        }
        
        // تحديث footer
        if (strpos($content, 'admin_footer_new.php') !== false) {
            $content = str_replace('admin_footer_new.php', 'supervisor_footer_modern.php', $content);
            $updated = true;
            echo "<p style='color: green;'>✅ تم تحديث footer</p>";
        }
        
        // إضافة page_title إذا لم يكن موجود
        if (strpos($content, '$page_title') === false) {
            $page_titles = [
                'supervisor_dashboard.php' => 'لوحة التحكم',
                'supervisor_reports.php' => 'التقارير الشاملة',
                'supervisor_financial.php' => 'التقارير المالية',
                'supervisor_activity.php' => 'سجل العمليات'
            ];
            
            if (isset($page_titles[$page])) {
                $title_line = '$page_title = \'' . $page_titles[$page] . '\';';
                $content = str_replace(
                    'require_once __DIR__ . \'/includes/supervisor_header_modern.php\';',
                    $title_line . "\nrequire_once __DIR__ . '/includes/supervisor_header_modern.php';",
                    $content
                );
                $updated = true;
                echo "<p style='color: green;'>✅ تم إضافة عنوان الصفحة</p>";
            }
        }
        
        // تحديث الكلاسات القديمة
        $class_updates = [
            'admin-layout' => 'supervisor-layout',
            'admin-content' => 'supervisor-content',
            'luxury-stats-card' => 'stats-card',
            'luxury-content' => 'stats-content',
            'luxury-info' => 'stats-info',
            'luxury-label' => 'stats-label',
            'luxury-value' => 'stats-value',
            'luxury-change' => 'stats-change',
            'luxury-icon' => 'stats-icon',
            'premium-card' => 'modern-card',
            'modern-card-enhanced' => 'modern-card'
        ];
        
        foreach ($class_updates as $old_class => $new_class) {
            if (strpos($content, $old_class) !== false) {
                $content = str_replace($old_class, $new_class, $content);
                $updated = true;
            }
        }
        
        if ($updated) {
            if (file_put_contents($page, $content)) {
                echo "<p style='color: green;'>✅ تم تحديث $page بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في تحديث $page</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ $page لا يحتاج تحديث</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ $page غير موجود</p>";
    }
}

// إنشاء ملف CSS إضافي للتحسينات
echo '<h2>إنشاء ملفات CSS إضافية:</h2>';

$additional_css = '/* تحسينات إضافية للمشرفين */

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* تحسينات البطاقات */
.stats-card {
    animation: fadeInScale 0.5s ease-out;
}

.modern-card {
    animation: slideInRight 0.6s ease-out;
}

/* تحسينات الجداول */
.modern-table tbody tr {
    transition: all 0.3s ease;
}

.modern-table tbody tr:hover {
    background: linear-gradient(90deg, var(--primary-color)05, transparent);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* تحسينات الأزرار */
.modern-btn {
    position: relative;
    overflow: hidden;
}

.modern-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

/* تحسينات الشريط الجانبي */
.sidebar-nav-link {
    position: relative;
    overflow: hidden;
}

.sidebar-nav-link::before {
    content: "";
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--primary-color)10, transparent);
    transition: right 0.3s ease;
}

.sidebar-nav-link:hover::before {
    right: 100%;
}

/* تحسينات الإحصائيات */
.stats-value {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات الشارات */
.modern-badge {
    position: relative;
    overflow: hidden;
}

.modern-badge::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.4s;
}

.modern-badge:hover::before {
    left: 100%;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .stats-value {
        font-size: 1.5rem;
    }
    
    .modern-card-header {
        padding: 1rem;
    }
    
    .modern-card-body {
        padding: 1rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .modern-sidebar,
    .floating-btn,
    #quickActionsMenu,
    .modern-btn {
        display: none !important;
    }
    
    .supervisor-content {
        margin-right: 0 !important;
    }
    
    .stats-card,
    .modern-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}';

if (file_put_contents('assets/css/supervisor_enhancements.css', $additional_css)) {
    echo '<p style="color: green;">✅ تم إنشاء assets/css/supervisor_enhancements.css</p>';
} else {
    echo '<p style="color: red;">❌ فشل في إنشاء ملف CSS الإضافي</p>';
}

// اختبار الصفحات المحدثة
echo '<h2>اختبار الصفحات المحدثة:</h2>';

foreach ($supervisor_pages as $page) {
    if (file_exists($page)) {
        echo "<p><a href='$page' target='_blank' style='color: blue; text-decoration: none; padding: 8px 16px; background: #f0f9ff; border-radius: 6px; display: inline-block; margin: 4px;'>🔗 اختبار $page</a></p>";
    }
}

echo '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin: 30px 0; text-align: center;">';
echo '<h2 style="margin-bottom: 20px;">✨ تم تحديث تصميم قسم المشرفين بنجاح!</h2>';
echo '<h3 style="margin-bottom: 15px;">المزايا الجديدة:</h3>';
echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">';
echo '<div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px;">';
echo '<h4>🎨 تصميم عصري</h4>';
echo '<p>واجهة مستخدم حديثة وأنيقة</p>';
echo '</div>';
echo '<div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px;">';
echo '<h4>📱 تصميم متجاوب</h4>';
echo '<p>يعمل بشكل مثالي على جميع الأجهزة</p>';
echo '</div>';
echo '<div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px;">';
echo '<h4>⚡ أداء محسن</h4>';
echo '<p>تحميل سريع وتفاعل سلس</p>';
echo '</div>';
echo '<div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px;">';
echo '<h4>🌙 ألوان متناسقة</h4>';
echo '<p>نظام ألوان عصري ومريح للعين</p>';
echo '</div>';
echo '</div>';
echo '<div style="margin-top: 30px;">';
echo '<a href="supervisor_dashboard.php" style="background: white; color: #667eea; padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block; margin: 10px;">🚀 جرب التصميم الجديد</a>';
echo '</div>';
echo '</div>';
?>
