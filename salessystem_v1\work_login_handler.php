<?php
/**
 * معالج تسجيل الدخول لأقسام إدارة العمل
 */

session_start();
require_once 'config/simple_db_config.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    if (isset($_GET['ajax'])) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
        exit;
    }
    header('Location: work_management_login.php');
    exit;
}

// التحقق من طلب AJAX
$is_ajax = isset($_POST['ajax']) && $_POST['ajax'] === '1';

// جلب البيانات المرسلة
$username = trim($_POST['username'] ?? '');
$password = trim($_POST['password'] ?? '');
$department = trim($_POST['department'] ?? '');

// التحقق من صحة البيانات
if (empty($username) || empty($password) || empty($department)) {
    $error_msg = 'جميع الحقول مطلوبة';
    if ($is_ajax) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => $error_msg]);
        exit;
    }
    $_SESSION['error'] = $error_msg;
    header('Location: work_management_login.php');
    exit;
}

// تحديد الجدول والصفحة حسب القسم
$table = '';
$redirect_page = '';
$session_prefix = '';

switch ($department) {
    case 'employees':
        $table = 'users';
        $redirect_page = 'index.php';
        $session_prefix = 'user_';
        break;
    case 'supervisors':
        $table = 'supervisors';
        $redirect_page = 'supervisor_dashboard.php';
        $session_prefix = 'supervisor_';
        break;
    case 'management':
        $table = 'admins';
        $redirect_page = 'admin_dashboard.php';
        $session_prefix = 'admin_';
        break;
    default:
        $_SESSION['error'] = 'قسم غير صحيح';
        header('Location: work_management_login.php');
        exit;
}

try {
    // البحث عن المستخدم في قاعدة البيانات
    $query = "SELECT * FROM $table WHERE username = ? AND status = 'active'";
    $stmt = $main_db->prepare($query);
    $stmt->bind_param('s', $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        
        // التحقق من كلمة المرور
        if (password_verify($password, $user['password'])) {
            // تسجيل الدخول ناجح
            if ($department === 'employees') {
                // للموظفين - استخدام نظام المبيعات الحالي
                $_SESSION['user_logged_in'] = true;
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_name'] = $user['name'] ?? $user['full_name'] ?? $username;
            } else {
                // للمشرفين والإدارة
                $_SESSION[$session_prefix . 'logged_in'] = true;
                $_SESSION[$session_prefix . 'id'] = $user['id'];
                $_SESSION[$session_prefix . 'username'] = $user['username'];
                $_SESSION[$session_prefix . 'name'] = $user['name'] ?? $user['full_name'] ?? $username;
                $_SESSION[$session_prefix . 'department'] = $department;
                $_SESSION[$session_prefix . 'role'] = $user['role'] ?? $department;
            }
            
            // تحديث آخر تسجيل دخول
            $update_query = "UPDATE $table SET last_login = NOW() WHERE id = ?";
            $update_stmt = $main_db->prepare($update_query);
            $update_stmt->bind_param('i', $user['id']);
            $update_stmt->execute();
            
            // تسجيل النشاط
            logActivity($session_prefix . 'login', $table, $user['id'], null, null, "تسجيل دخول $department");

            if ($is_ajax) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'redirect' => $redirect_page,
                    'department' => $department,
                    'username' => $username
                ]);
                exit;
            }

            $_SESSION['success'] = 'تم تسجيل الدخول بنجاح';
            header('Location: ' . $redirect_page);
            exit;
            
        } else {
            $error_msg = 'كلمة المرور غير صحيحة';
        }
    } else {
        $error_msg = 'اسم المستخدم غير موجود أو الحساب غير نشط';
    }

} catch (Exception $e) {
    error_log("خطأ في تسجيل الدخول: " . $e->getMessage());
    $error_msg = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
}

// في حالة فشل تسجيل الدخول
if ($is_ajax) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => $error_msg ?? 'خطأ غير معروف']);
    exit;
}

$_SESSION['error'] = $error_msg ?? 'خطأ غير معروف';
header('Location: work_management_login.php');
exit;

/**
 * دالة تسجيل النشاط
 */
function logActivity($action, $table, $user_id, $target_id = null, $old_data = null, $description = '') {
    global $main_db;
    
    try {
        $query = "INSERT INTO activity_log (action, table_name, user_id, target_id, old_data, description, created_at) 
                  VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $main_db->prepare($query);
        $stmt->bind_param('ssisss', $action, $table, $user_id, $target_id, $old_data, $description);
        $stmt->execute();
    } catch (Exception $e) {
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
    }
}
?>
