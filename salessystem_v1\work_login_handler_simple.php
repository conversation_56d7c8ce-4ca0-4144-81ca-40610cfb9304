<?php
/**
 * معالج تسجيل الدخول المحدث - يستخدم قاعدة البيانات البسيطة
 */

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set("display_errors", 1);

session_start();

// تضمين إعدادات قاعدة البيانات البسيطة
require_once "config/simple_db_config.php";

// دالة إرسال استجابة JSON
function sendJsonResponse($success, $message, $data = []) {
    header("Content-Type: application/json; charset=utf-8");
    echo json_encode([
        "success" => $success,
        "message" => $message,
        "data" => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// دالة تسجيل الأخطاء
function logError($message, $context = []) {
    error_log("Login Error: $message " . json_encode($context));
}

try {
    // التحقق من طريقة الطلب
    if ($_SERVER["REQUEST_METHOD"] !== "POST") {
        sendJsonResponse(false, "طريقة طلب غير صحيحة");
    }

    // جلب البيانات المرسلة
    $username = trim($_POST["username"] ?? "");
    $password = trim($_POST["password"] ?? "");
    $department = trim($_POST["department"] ?? "");

    // تسجيل البيانات المستلمة للتطوير
    logError("Login attempt", [
        "username" => $username,
        "department" => $department,
        "post_data" => array_keys($_POST)
    ]);

    // التحقق من صحة البيانات
    if (empty($username)) {
        sendJsonResponse(false, "اسم المستخدم مطلوب");
    }
    
    if (empty($password)) {
        sendJsonResponse(false, "كلمة المرور مطلوبة");
    }
    
    if (empty($department)) {
        sendJsonResponse(false, "القسم مطلوب");
    }

    // تحديد الجدول والصفحة حسب القسم
    $table = "";
    $redirect_page = "";
    $session_prefix = "";

    switch ($department) {
        case "employees":
            $table = "users";
            $redirect_page = "index.php";
            $session_prefix = "user_";
            break;
        case "supervisors":
            $table = "supervisors";
            $redirect_page = "supervisor_dashboard.php";
            $session_prefix = "supervisor_";
            break;
        case "management":
            $table = "admins";
            $redirect_page = "admin_dashboard.php";
            $session_prefix = "admin_";
            break;
        default:
            sendJsonResponse(false, "قسم غير صحيح: " . $department);
    }

    // الاتصال بقاعدة البيانات
    $db = getSimpleDB();
    if (!$db) {
        logError("Database connection failed");
        sendJsonResponse(false, "خطأ في الاتصال بقاعدة البيانات");
    }

    // البحث عن المستخدم في قاعدة البيانات
    $query = "SELECT * FROM $table WHERE username = ? AND status = \"active\"";
    $stmt = $db->prepare($query);
    
    if (!$stmt) {
        logError("Query preparation failed", ["error" => $db->error, "query" => $query]);
        sendJsonResponse(false, "خطأ في النظام");
    }

    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows !== 1) {
        logError("User not found", ["username" => $username, "table" => $table]);
        sendJsonResponse(false, "اسم المستخدم غير موجود أو الحساب غير نشط");
    }

    $user = $result->fetch_assoc();

    // التحقق من كلمة المرور
    if (!password_verify($password, $user["password"])) {
        logError("Invalid password", ["username" => $username]);
        sendJsonResponse(false, "كلمة المرور غير صحيحة");
    }

    // تسجيل الدخول ناجح - إنشاء الجلسة
    if ($department === "employees") {
        // للموظفين - استخدام نظام المبيعات الحالي
        $_SESSION["user_logged_in"] = true;
        $_SESSION["user_id"] = $user["id"];
        $_SESSION["username"] = $user["username"];
        $_SESSION["user_name"] = $user["name"] ?? $user["full_name"] ?? $username;
    } else {
        // للمشرفين والإدارة
        $_SESSION[$session_prefix . "logged_in"] = true;
        $_SESSION[$session_prefix . "id"] = $user["id"];
        $_SESSION[$session_prefix . "username"] = $user["username"];
        $_SESSION[$session_prefix . "name"] = $user["name"] ?? $user["full_name"] ?? $username;
        $_SESSION[$session_prefix . "department"] = $department;
        $_SESSION[$session_prefix . "role"] = $user["role"] ?? $department;
    }

    // تحديث آخر تسجيل دخول
    $update_query = "UPDATE $table SET last_login = NOW() WHERE id = ?";
    $update_stmt = $db->prepare($update_query);
    if ($update_stmt) {
        $update_stmt->bind_param("i", $user["id"]);
        $update_stmt->execute();
    }

    // تسجيل النشاط
    logError("Successful login", [
        "username" => $username,
        "department" => $department,
        "user_id" => $user["id"]
    ]);

    // إرسال استجابة النجاح
    sendJsonResponse(true, "تم تسجيل الدخول بنجاح", [
        "redirect" => $redirect_page,
        "department" => $department,
        "username" => $username,
        "user_id" => $user["id"]
    ]);

} catch (Exception $e) {
    logError("Exception occurred", [
        "message" => $e->getMessage(),
        "file" => $e->getFile(),
        "line" => $e->getLine()
    ]);
    
    sendJsonResponse(false, "حدث خطأ في النظام، يرجى المحاولة لاحقاً");
}
?>