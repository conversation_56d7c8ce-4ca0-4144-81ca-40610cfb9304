/* التنسيقات الأصلية البسيطة */
body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    font-weight: bold;
}

.card-body {
    padding: 20px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 2px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn:hover {
    opacity: 0.9;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: #f8f9fa;
    font-weight: bold;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.navbar {
    background: #343a40;
    color: white;
    padding: 10px 20px;
    margin-bottom: 20px;
}

.navbar a {
    color: white;
    text-decoration: none;
    margin-left: 15px;
}

.navbar a:hover {
    color: #ccc;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mt-3 { margin-top: 1rem; }
.mb-3 { margin-bottom: 1rem; }
.p-3 { padding: 1rem; }

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col {
    flex: 1;
    padding: 10px;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 10px;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    padding: 10px;
}

.col-md-3 {
    flex: 0 0 25%;
    padding: 10px;
}

/* تحسينات بسيطة للتنسيقات الأصلية */
.stats-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-card h3 {
    color: #007bff;
    margin-bottom: 10px;
}

.stats-card .number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.quick-actions {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.quick-actions .btn {
    margin: 5px;
    padding: 10px 20px;
}

.sidebar {
    background: #343a40;
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.sidebar .nav-link {
    color: #ccc;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 5px;
    text-decoration: none;
    display: block;
}

.sidebar .nav-link:hover {
    background: #495057;
    color: white;
}

.sidebar .nav-link.active {
    background: #007bff;
    color: white;
}

.main-content {
    padding: 20px;
}

.page-header {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.page-header h1 {
    margin: 0;
    color: #333;
}

.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.floating-buttons .btn {
    display: block;
    margin-bottom: 10px;
    border-radius: 50px;
    padding: 12px 20px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.invoice-form {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.invoice-table th,
.invoice-table td {
    padding: 12px;
    text-align: right;
    border: 1px solid #dee2e6;
}

.invoice-table th {
    background: #f8f9fa;
    font-weight: bold;
}

.invoice-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-dialog {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    text-align: left;
}

.close {
    background: none;
    border: none;
    font-size: 1.5rem;
    float: left;
    cursor: pointer;
}

/* تنسيقات الطباعة */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

/* تنسيقات الجوال */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .card {
        margin-bottom: 15px;
    }

    .table {
        font-size: 12px;
    }

    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .floating-buttons {
        bottom: 10px;
        right: 10px;
    }

    .modal-dialog {
        width: 95%;
        margin: 10px;
    }

    .col-md-6,
    .col-md-4,
    .col-md-3 {
        flex: 0 0 100%;
    }

    .row {
        margin: 0;
    }

    .col {
        padding: 5px;
    }
}
