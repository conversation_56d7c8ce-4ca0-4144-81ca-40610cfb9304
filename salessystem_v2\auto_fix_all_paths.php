<?php
/**
 * إصلاح تلقائي شامل لجميع المسارات الخاطئة في المشروع
 */

echo '<h2>إصلاح تلقائي شامل للمسارات</h2>';

// قائمة جميع الملفات في المشروع
$all_files = [
    // ملفات الموظفين
    'employees/sales_dashboard.php',
    'employees/customers.php',
    'employees/products.php',
    'employees/purchases.php',
    'employees/sales.php',
    'employees/reports.php',
    'employees/profile.php',
    'employees/index.php',
    
    // ملفات المديرين
    'admin/admin_dashboard.php',
    'admin/admin_reports.php',
    'admin/admin_manage_admins.php',
    
    // ملفات المشرفين
    'supervisors/supervisor_dashboard.php',
    
    // ملفات أخرى
    'work_login_handler_simple.php'
];

// قائمة شاملة للمسارات الخاطئة والصحيحة
$path_replacements = [
    // مسارات config
    '__DIR__.\'/../../shared/shared/config/simple_db_config.php\'' => '__DIR__.\'/../shared/config/simple_db_config.php\'',
    '__DIR__ . \'/../../shared/shared/config/simple_db_config.php\'' => '__DIR__ . \'/../shared/config/simple_db_config.php\'',
    'require_once __DIR__.\'/../../shared/shared/config/simple_db_config.php\';' => 'require_once __DIR__.\'/../shared/config/simple_db_config.php\';',
    'require_once __DIR__ . \'/../../shared/shared/config/simple_db_config.php\';' => 'require_once __DIR__ . \'/../shared/config/simple_db_config.php\';',
    
    // مسارات includes
    '__DIR__.\'/../../shared/shared/includes/' => '__DIR__.\'/../shared/includes/',
    '__DIR__ . \'/../../shared/shared/includes/' => '__DIR__ . \'/../shared/includes/',
    '/../../shared/shared/includes/' => '/../shared/includes/',
    
    // مسارات assets
    '../shared/../../shared/shared/assets/' => '../shared/assets/',
    '/../../shared/shared/assets/' => '/../shared/assets/',
    '../../shared/shared/assets/' => '../shared/assets/',
    
    // مسارات خاصة
    'shared/shared/config/' => 'shared/config/',
    'shared/shared/includes/' => 'shared/includes/',
    'shared/shared/assets/' => 'shared/assets/',
    
    // مسارات require خاصة
    'require_once "shared/shared/config/simple_db_config.php";' => 'require_once "shared/config/simple_db_config.php";',
];

$total_files = 0;
$files_modified = 0;
$total_replacements = 0;

echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>جاري الإصلاح...</h3>';

foreach ($all_files as $file_path) {
    $total_files++;
    
    if (!file_exists($file_path)) {
        echo "<p style='color: orange;'>⚠️ $file_path - الملف غير موجود</p>";
        continue;
    }
    
    $original_content = file_get_contents($file_path);
    $modified_content = $original_content;
    $file_replacements = 0;
    
    // تطبيق جميع الإصلاحات
    foreach ($path_replacements as $wrong => $correct) {
        $count = 0;
        $modified_content = str_replace($wrong, $correct, $modified_content, $count);
        if ($count > 0) {
            $file_replacements += $count;
            echo "<small style='color: blue;'>  📝 $file_path: $wrong → $correct ($count مرة)</small><br>";
        }
    }
    
    // حفظ الملف إذا تم تعديله
    if ($modified_content !== $original_content) {
        if (file_put_contents($file_path, $modified_content)) {
            echo "<p style='color: green;'>✅ تم إصلاح $file_path ($file_replacements تغيير)</p>";
            $files_modified++;
            $total_replacements += $file_replacements;
        } else {
            echo "<p style='color: red;'>❌ فشل في حفظ $file_path</p>";
        }
    } else {
        echo "<p style='color: gray;'>⚪ $file_path - لا يحتاج إصلاح</p>";
    }
}

echo '</div>';

// ملخص الإصلاحات
echo '<div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>📊 ملخص الإصلاحات:</h3>';
echo "<p><strong>إجمالي الملفات المفحوصة:</strong> $total_files</p>";
echo "<p><strong>الملفات المُعدلة:</strong> $files_modified</p>";
echo "<p><strong>إجمالي التغييرات:</strong> $total_replacements</p>";
echo '</div>';

// فحص نهائي
echo '<h3>فحص نهائي للتأكد من الإصلاح:</h3>';

$remaining_errors = 0;
$error_patterns = [
    'shared/shared',
    '/../../shared/shared/',
    '../shared/../../shared/shared/',
    '__DIR__.\'/../../shared/shared/',
    '__DIR__ . \'/../../shared/shared/'
];

foreach ($all_files as $file_path) {
    if (!file_exists($file_path)) continue;
    
    $content = file_get_contents($file_path);
    $file_has_errors = false;
    
    foreach ($error_patterns as $pattern) {
        if (strpos($content, $pattern) !== false) {
            if (!$file_has_errors) {
                echo "<p style='color: red;'>❌ $file_path لا يزال يحتوي على مسارات خاطئة:</p>";
                $file_has_errors = true;
                $remaining_errors++;
            }
            
            // العثور على السطر
            $lines = explode("\n", $content);
            foreach ($lines as $line_num => $line) {
                if (strpos($line, $pattern) !== false) {
                    echo "<small style='color: red; margin-left: 20px;'>السطر " . ($line_num + 1) . ": " . htmlspecialchars(trim($line)) . "</small><br>";
                }
            }
        }
    }
    
    if (!$file_has_errors) {
        echo "<p style='color: green;'>✅ $file_path - جميع المسارات صحيحة</p>";
    }
}

// النتيجة النهائية
if ($remaining_errors === 0) {
    echo '<div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>🎉 تم الانتهاء بنجاح!</h3>';
    echo '<p>جميع المسارات في المشروع تم إصلاحها بنجاح</p>';
    echo '<p>يمكنك الآن استخدام جميع صفحات النظام بدون أخطاء</p>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>⚠️ تحذير!</h3>';
    echo "<p>لا يزال هناك $remaining_errors ملف يحتوي على مسارات خاطئة</p>";
    echo '<p>قد تحتاج إلى إصلاح يدوي إضافي</p>';
    echo '</div>';
}

// اختبار سريع
echo '<h3>اختبار سريع:</h3>';
echo '<div style="background: #e9ecef; padding: 15px; border-radius: 8px;">';

// اختبار ملف قاعدة البيانات
if (file_exists('shared/config/simple_db_config.php')) {
    echo '<p style="color: green;">✅ ملف قاعدة البيانات موجود</p>';
    
    try {
        require_once 'shared/config/simple_db_config.php';
        echo '<p style="color: green;">✅ تم تحميل ملف قاعدة البيانات بنجاح</p>';
    } catch (Exception $e) {
        echo '<p style="color: red;">❌ خطأ في تحميل ملف قاعدة البيانات: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p style="color: red;">❌ ملف قاعدة البيانات غير موجود</p>';
}

echo '</div>';

echo '<div style="background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h4>🔗 الخطوات التالية:</h4>';
echo '<ol>';
echo '<li><a href="create_test_users.php" style="color: #007bff;">إنشاء المستخدمين التجريبيين</a></li>';
echo '<li><a href="test_work_login.php" style="color: #007bff;">اختبار نظام تسجيل الدخول</a></li>';
echo '<li><a href="work_management_login.php" style="color: #007bff;">تسجيل الدخول واستخدام النظام</a></li>';
echo '</ol>';
echo '</div>';
?>
