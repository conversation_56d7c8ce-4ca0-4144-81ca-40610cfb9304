<?php
/**
 * فحص شامل لجميع المسارات في المشروع
 */

echo '<h2>فحص شامل للمسارات في المشروع</h2>';

// قائمة المجلدات والملفات للفحص
$directories = [
    'employees' => [
        'sales_dashboard.php',
        'customers.php',
        'products.php',
        'purchases.php',
        'sales.php',
        'reports.php',
        'profile.php',
        'index.php'
    ],
    'admin' => [
        'admin_dashboard.php',
        'admin_reports.php',
        'admin_manage_admins.php'
    ],
    'supervisors' => [
        'supervisor_dashboard.php'
    ]
];

// أنماط المسارات الخاطئة للبحث عنها
$wrong_patterns = [
    'shared/shared',
    '/../../shared/shared/',
    '../shared/../../shared/shared/',
    '__DIR__.\'/../../shared/shared/',
    '__DIR__ . \'/../../shared/shared/',
    'require_once __DIR__.\'/../../shared/shared/',
    'require_once __DIR__ . \'/../../shared/shared/'
];

$total_files_checked = 0;
$files_with_errors = 0;
$total_errors = 0;

echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>نتائج الفحص:</h3>';

foreach ($directories as $dir => $files) {
    echo "<h4>📁 مجلد: $dir</h4>";
    
    foreach ($files as $file) {
        $file_path = "$dir/$file";
        $total_files_checked++;
        
        if (!file_exists($file_path)) {
            echo "<p style='color: orange;'>⚠️ $file_path - الملف غير موجود</p>";
            continue;
        }
        
        $content = file_get_contents($file_path);
        $file_errors = [];
        
        // البحث عن المسارات الخاطئة
        foreach ($wrong_patterns as $pattern) {
            if (strpos($content, $pattern) !== false) {
                // العثور على أرقام الأسطر
                $lines = explode("\n", $content);
                foreach ($lines as $line_num => $line) {
                    if (strpos($line, $pattern) !== false) {
                        $file_errors[] = [
                            'pattern' => $pattern,
                            'line' => $line_num + 1,
                            'content' => trim($line)
                        ];
                    }
                }
            }
        }
        
        if (!empty($file_errors)) {
            $files_with_errors++;
            $total_errors += count($file_errors);
            echo "<p style='color: red;'>❌ $file_path - وُجد " . count($file_errors) . " خطأ:</p>";
            echo "<ul style='margin-left: 20px;'>";
            foreach ($file_errors as $error) {
                echo "<li style='color: red; font-size: 12px;'>";
                echo "السطر {$error['line']}: <code>{$error['pattern']}</code><br>";
                echo "<small style='color: #666;'>{$error['content']}</small>";
                echo "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: green;'>✅ $file_path - جميع المسارات صحيحة</p>";
        }
    }
}

echo '</div>';

// ملخص النتائج
echo '<div style="background: ' . ($files_with_errors > 0 ? '#f8d7da' : '#d4edda') . '; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>📊 ملخص النتائج:</h3>';
echo "<p><strong>إجمالي الملفات المفحوصة:</strong> $total_files_checked</p>";
echo "<p><strong>الملفات التي تحتوي على أخطاء:</strong> $files_with_errors</p>";
echo "<p><strong>إجمالي الأخطاء:</strong> $total_errors</p>";

if ($files_with_errors === 0) {
    echo '<p style="color: #155724; font-weight: bold;">🎉 ممتاز! جميع المسارات صحيحة</p>';
} else {
    echo '<p style="color: #721c24; font-weight: bold;">⚠️ يوجد أخطاء تحتاج إصلاح</p>';
}
echo '</div>';

// فحص إضافي للملفات المهمة
echo '<h3>فحص إضافي للملفات المهمة:</h3>';

$important_files = [
    'work_management_login.php',
    'work_login_handler_simple.php',
    'shared/config/simple_db_config.php',
    'shared/includes/header_simple.php',
    'shared/includes/functions.php'
];

foreach ($important_files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file - موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $file - غير موجود</p>";
    }
}

// اختبار سريع للمسارات
echo '<h3>اختبار سريع للمسارات:</h3>';

// اختبار مسار قاعدة البيانات
$db_config_path = 'shared/config/simple_db_config.php';
if (file_exists($db_config_path)) {
    echo "<p style='color: green;'>✅ ملف قاعدة البيانات: $db_config_path</p>";
    
    // محاولة تضمين الملف
    try {
        require_once $db_config_path;
        echo "<p style='color: green;'>✅ تم تحميل ملف قاعدة البيانات بنجاح</p>";
        
        // اختبار الاتصال
        $test_db = getSimpleDB();
        if ($test_db && !$test_db->connect_error) {
            echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في تحميل ملف قاعدة البيانات: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف قاعدة البيانات غير موجود: $db_config_path</p>";
}

echo '<div style="background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h4>🔗 روابط مفيدة:</h4>';
echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;">صفحة تسجيل الدخول</a></p>';
echo '<p><a href="create_test_users.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;">إنشاء مستخدمين تجريبيين</a></p>';
echo '<p><a href="test_work_login.php" style="background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;">اختبار تسجيل الدخول</a></p>';
echo '</div>';
?>
