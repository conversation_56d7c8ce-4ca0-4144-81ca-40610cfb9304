<?php
/**
 * إنشاء المستخدمين التجريبيين لجميع الأقسام
 */
require_once 'shared/config/simple_db_config.php';

echo '<h2>إنشاء المستخدمين التجريبيين</h2>';

// الحصول على اتصال قاعدة البيانات
$db = getSimpleDB();
if (!$db) {
    echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
    exit;
}

echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات بنجاح</p>';

// إنشاء الجداول أولاً
echo '<h3>التحقق من الجداول:</h3>';

// التحقق من جدول المستخدمين
$users_table = $db->query("SHOW TABLES LIKE 'users'");
if ($users_table && $users_table->num_rows > 0) {
    echo '<p style="color: green;">✅ جدول المستخدمين موجود</p>';
} else {
    echo '<p style="color: red;">❌ جدول المستخدمين غير موجود - سيتم إنشاؤه</p>';
    $create_users = $db->query("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `email` varchar(100) NOT NULL UNIQUE,
            `phone` varchar(20) DEFAULT NULL,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) DEFAULT NULL,
            `status` enum('active','inactive') DEFAULT 'active',
            `last_login` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    if ($create_users) {
        echo '<p style="color: green;">✅ تم إنشاء جدول المستخدمين</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء جدول المستخدمين: ' . $db->error . '</p>';
    }
}

// التحقق من جدول المشرفين
$supervisors_table = $db->query("SHOW TABLES LIKE 'supervisors'");
if ($supervisors_table && $supervisors_table->num_rows > 0) {
    echo '<p style="color: green;">✅ جدول المشرفين موجود</p>';
} else {
    echo '<p style="color: red;">❌ جدول المشرفين غير موجود - سيتم إنشاؤه</p>';
    $create_supervisors = $db->query("
        CREATE TABLE IF NOT EXISTS `supervisors` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `email` varchar(100) NOT NULL UNIQUE,
            `phone` varchar(20) DEFAULT NULL,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) DEFAULT NULL,
            `department` varchar(50) DEFAULT NULL,
            `permissions` text DEFAULT NULL,
            `status` enum('active','inactive') DEFAULT 'active',
            `last_login` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    if ($create_supervisors) {
        echo '<p style="color: green;">✅ تم إنشاء جدول المشرفين</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء جدول المشرفين: ' . $db->error . '</p>';
    }
}

// التحقق من جدول المديرين
$admins_table = $db->query("SHOW TABLES LIKE 'admins'");
if ($admins_table && $admins_table->num_rows > 0) {
    echo '<p style="color: green;">✅ جدول المديرين موجود</p>';
} else {
    echo '<p style="color: red;">❌ جدول المديرين غير موجود - سيتم إنشاؤه</p>';
    $create_admins = $db->query("
        CREATE TABLE IF NOT EXISTS `admins` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `email` varchar(100) NOT NULL UNIQUE,
            `phone` varchar(20) DEFAULT NULL,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) DEFAULT NULL,
            `role` enum('super_admin','admin','manager') DEFAULT 'admin',
            `permissions` text DEFAULT NULL,
            `status` enum('active','inactive') DEFAULT 'active',
            `last_login` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    if ($create_admins) {
        echo '<p style="color: green;">✅ تم إنشاء جدول المديرين</p>';
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء جدول المديرين: ' . $db->error . '</p>';
    }
}

echo '<h3>إنشاء المستخدمين التجريبيين:</h3>';

// 1. إنشاء موظف تجريبي
$employee_check = $db->query("SELECT id FROM users WHERE username = 'employee'");
if ($employee_check && $employee_check->num_rows > 0) {
    echo '<p style="color: orange;">⚠️ الموظف التجريبي موجود مسبقاً</p>';
} else {
    $employee_password = password_hash('employee123', PASSWORD_DEFAULT);
    $employee_insert = $db->prepare("INSERT INTO users (username, email, phone, password, full_name, status) VALUES (?, ?, ?, ?, ?, ?)");
    if ($employee_insert) {
        $username = 'employee';
        $email = '<EMAIL>';
        $phone = '0123456789';
        $full_name = 'موظف تجريبي';
        $status = 'active';
        
        $employee_insert->bind_param('ssssss', $username, $email, $phone, $employee_password, $full_name, $status);
        if ($employee_insert->execute()) {
            echo '<p style="color: green;">✅ تم إنشاء الموظف التجريبي (employee / employee123)</p>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء الموظف التجريبي: ' . $employee_insert->error . '</p>';
        }
    }
}

// 2. إنشاء مشرف تجريبي
$supervisor_check = $db->query("SELECT id FROM supervisors WHERE username = 'supervisor'");
if ($supervisor_check && $supervisor_check->num_rows > 0) {
    echo '<p style="color: orange;">⚠️ المشرف التجريبي موجود مسبقاً</p>';
} else {
    $supervisor_password = password_hash('supervisor123', PASSWORD_DEFAULT);
    $supervisor_insert = $db->prepare("INSERT INTO supervisors (username, email, phone, password, full_name, department, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
    if ($supervisor_insert) {
        $username = 'supervisor';
        $email = '<EMAIL>';
        $phone = '0987654321';
        $full_name = 'مشرف تجريبي';
        $department = 'الإشراف العام';
        $status = 'active';
        
        $supervisor_insert->bind_param('sssssss', $username, $email, $phone, $supervisor_password, $full_name, $department, $status);
        if ($supervisor_insert->execute()) {
            echo '<p style="color: green;">✅ تم إنشاء المشرف التجريبي (supervisor / supervisor123)</p>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء المشرف التجريبي: ' . $supervisor_insert->error . '</p>';
        }
    }
}

// 3. إنشاء مدير تجريبي
$admin_check = $db->query("SELECT id FROM admins WHERE username = 'admin'");
if ($admin_check && $admin_check->num_rows > 0) {
    echo '<p style="color: orange;">⚠️ المدير التجريبي موجود مسبقاً</p>';
} else {
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $admin_insert = $db->prepare("INSERT INTO admins (username, email, phone, password, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
    if ($admin_insert) {
        $username = 'admin';
        $email = '<EMAIL>';
        $phone = '0555666777';
        $full_name = 'مدير تجريبي';
        $role = 'admin';
        $status = 'active';
        
        $admin_insert->bind_param('sssssss', $username, $email, $phone, $admin_password, $full_name, $role, $status);
        if ($admin_insert->execute()) {
            echo '<p style="color: green;">✅ تم إنشاء المدير التجريبي (admin / admin123)</p>';
        } else {
            echo '<p style="color: red;">❌ فشل في إنشاء المدير التجريبي: ' . $admin_insert->error . '</p>';
        }
    }
}

echo '<h3>ملخص المستخدمين التجريبيين:</h3>';
echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h4>بيانات تسجيل الدخول:</h4>';
echo '<ul>';
echo '<li><strong>الموظفين:</strong> employee / employee123</li>';
echo '<li><strong>المشرفين:</strong> supervisor / supervisor123</li>';
echo '<li><strong>الإدارة:</strong> admin / admin123</li>';
echo '</ul>';
echo '<p><strong>ملاحظة:</strong> يمكنك الآن استخدام هذه البيانات لتسجيل الدخول من صفحة إدارة العمل</p>';
echo '</div>';

echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة لصفحة تسجيل الدخول</a></p>';
?>
