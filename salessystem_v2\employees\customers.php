<?php
/**
 * صفحة إدارة العملاء والموردين
 */

require_once __DIR__ . '/../shared/config/init.php';
require_once __DIR__ . '/../shared/includes/functions.php';
require_once __DIR__ . '/../shared/includes/header_simple.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();

// معالجة حذف العميل/المورد
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $customer_id = intval($_GET['delete']);
    $username = $_SESSION['username'];

    // الحصول على أسماء الجداول مع البادئة
    $customers_table = getUserTableName('customers', $username);
    $sales_table = getUserTableName('sales', $username);
    $purchases_table = getUserTableName('purchases', $username);

    // التحقق من عدم استخدام العميل/المورد في فواتير
    $check_sales = $db->prepare("SELECT COUNT(*) as count FROM `$sales_table` WHERE customer_id = ? AND user_id = ?");
    $check_sales->bind_param("ii", $customer_id, $_SESSION['user_id']);
    $check_sales->execute();
    $sales_count = $check_sales->get_result()->fetch_assoc()['count'];
    $check_sales->close();

    $check_purchases = $db->prepare("SELECT COUNT(*) as count FROM `$purchases_table` WHERE customer_id = ? AND user_id = ?");
    $check_purchases->bind_param("ii", $customer_id, $_SESSION['user_id']);
    $check_purchases->execute();
    $purchases_count = $check_purchases->get_result()->fetch_assoc()['count'];
    $check_purchases->close();

    if ($sales_count > 0 || $purchases_count > 0) {
        $_SESSION['error'] = "لا يمكن حذف هذا العميل/المورد لأنه مستخدم في فواتير موجودة";
    } else {
        $stmt = $db->prepare("DELETE FROM `$customers_table` WHERE id = ? AND user_id = ?");
        $stmt->bind_param("ii", $customer_id, $_SESSION['user_id']);

        if ($stmt->execute()) {
            logActivity('customer_delete', 'customers', $customer_id, null, null, 'حذف عميل/مورد');
            $_SESSION['success'] = "تم حذف العميل/المورد بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء حذف العميل/المورد";
        }
        $stmt->close();
    }

    header("Location: customers.php?type=" . ($_GET['type'] ?? 'customer'));
    exit();
}

// جلب العملاء/الموردين مع البحث والترقيم
$search = $_GET['search'] ?? '';
$customer_type = $_GET['type'] ?? 'customer'; // افتراضي: عملاء
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;
$username = $_SESSION['username'];

// الحصول على اسم الجدول مع البادئة
$customers_table = getUserTableName('customers', $username);

$where_conditions = [];
$params = [];
$types = '';

// فلتر user_id (أمان)
$where_conditions[] = "user_id = ?";
$params[] = $_SESSION['user_id'];
$types .= 'i';

// فلتر نوع العميل
$where_conditions[] = "customer_type = ?";
$params[] = $customer_type;
$types .= 's';

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= 'sss';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
}

// عدد العملاء/الموردين الإجمالي
$count_sql = "SELECT COUNT(*) as total FROM `$customers_table` $where_clause";
if (!empty($params)) {
    $count_stmt = $db->prepare($count_sql);
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $total_customers = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
} else {
    $total_customers = $db->query($count_sql)->fetch_assoc()['total'];
}

$total_pages = ceil($total_customers / $limit);

// جلب العملاء/الموردين
$sql = "SELECT * FROM `$customers_table` $where_clause ORDER BY name LIMIT $limit OFFSET $offset";
if (!empty($params)) {
    $stmt = $db->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $customers = $stmt->get_result();
    $stmt->close();
} else {
    $customers = $db->query($sql);
}

// الرسائل يتم عرضها تلقائياً في header_simple.php?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    <?php echo $customer_type === 'supplier' ? t("suppliers_manage") : t("customers_manage"); ?>
                </h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                    <i class="fas fa-plus"></i>
                    <?php echo $customer_type === 'supplier' ? t("new_supplier") : t("new_customer"); ?>
                </button>
            </div>

            <div class="card-body">
                <!-- تبويب العملاء والموردين -->
                <ul class="nav nav-tabs mb-3" id="customerTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $customer_type === 'customer' ? 'active' : ''; ?>"
                           href="?type=customer<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                            <i class="fas fa-user-friends me-2"></i>
                            <?php echo 'النص';?>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $customer_type === 'supplier' ? 'active' : ''; ?>"
                           href="?type=supplier<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                            <i class="fas fa-truck me-2"></i>
                            <?php echo 'النص';?>
                        </a>
                    </li>
                </ul>

                <!-- شريط البحث -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="hidden" name="type" value="<?php echo htmlspecialchars($customer_type); ?>">
                            <input type="text" class="form-control" name="search"
                                   placeholder="<?php echo t("search"); ?> <?php echo $customer_type === 'supplier' ? t("suppliers") : t("customers"); ?>..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-outline-primary ms-2">
                                <i class="fas fa-search"></i>
                            </button>
                            <?php if (!empty($search)): ?>
                            <a href="?type=<?php echo htmlspecialchars($customer_type); ?>" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                            <?php endif; ?>
                        </form>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            <?php echo t("total"); ?> <?php echo $customer_type === 'supplier' ? t("suppliers") : t("customers"); ?>: <?php echo number_format($total_customers); ?>
                        </small>
                    </div>
                </div>

                <!-- جدول العملاء/الموردين -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo t("number"); ?></th>
                                <th><?php echo t("name"); ?></th>
                                <th><?php echo t("phone"); ?></th>
                                <th><?php echo t("email"); ?></th>
                                <th><?php echo t("tax_number"); ?></th>
                                <th><?php echo t("address"); ?></th>
                                <th><?php echo t("date"); ?></th>
                                <th><?php echo t("actions"); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($customers && $customers->num_rows > 0): ?>
                                <?php $counter = $offset + 1; ?>
                                <?php while ($customer = $customers->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $counter++; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                    </td>
                                    <td>
                                        <?php if (!empty($customer['phone'])): ?>
                                            <a href="tel:<?php echo htmlspecialchars($customer['phone']); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($customer['phone']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo t("non"); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($customer['email'])): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($customer['email']); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($customer['email']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo t("non"); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($customer['tax_number'])): ?>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($customer['tax_number']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo t("non"); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $address = htmlspecialchars($customer['address'] ?? '');
                                        echo strlen($address) > 30 ? substr($address, 0, 30) . '...' : ($address ?: ' - ');
                                        ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d', strtotime($customer['created_at'] ?? date('Y-m-d'))); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="account_statement.php?type=<?php echo $customer_type; ?>&id=<?php echo $customer['id']; ?>"
                                               class="btn btn-sm btn-outline-info"
                                               title="كشف الحساب">
                                                <i class="fas fa-file-alt"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    onclick="editCustomer(<?php echo $customer['id']; ?>)"
                                                    title=t("edit")>
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteCustomer(<?php echo $customer['id']; ?>, '<?php echo addslashes($customer['name']); ?>')"
                                                    title=t("delete")>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-user-slash fa-3x mb-3"></i>
                                            <p><?php echo t("no"); ?> <?php echo $customer_type === 'supplier' ? t("suppliers") : t("customers"); ?></p>
                                            <?php if (!empty($search)): ?>
                                                <p><?php echo t("no_data_available"); ?>  "<?php echo htmlspecialchars($search); ?>"</p>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- الترقيم -->
                <?php if ($total_pages > 1): ?>
                <?php
                // بناء معاملات الاستعلام للحفاظ على الفلاتر
                $query_params = [];
                $query_params[] = 'type=' . urlencode($customer_type);
                if (!empty($search)) $query_params[] = 'search=' . urlencode($search);
                $query_string = !empty($query_params) ? '&' . implode('&', $query_params) : '';
                ?>
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo $query_string; ?>">
                                السابق
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $query_string; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo $query_string; ?>">
                                التالي
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل عميل/مورد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    <?php echo $customer_type === 'supplier' ? t("new_supplier") : t("new_customer"); ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="customerForm" method="POST" action="save_customer.php">
                <div class="modal-body">
                    <input type="hidden" id="customerId" name="customer_id" value="">
                    <input type="hidden" id="customerType" name="customer_type" value="<?php echo htmlspecialchars($customer_type); ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerName" class="form-label"><?php echo t("name"); ?> *</label>
                                <input type="text" class="form-control" id="customerName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerPhone" class="form-label"><?php echo t("phone"); ?></label>
                                <input type="tel" class="form-control" id="customerPhone" name="phone">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerEmail" class="form-label"><?php echo t("email"); ?></label>
                                <input type="email" class="form-control" id="customerEmail" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customerTaxNumber" class="form-label"><?php echo t("tax_number"); ?></label>
                                <input type="text" class="form-control" id="customerTaxNumber" name="tax_number">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="customerAddress" class="form-label"><?php echo t("address"); ?></label>
                        <textarea class="form-control" id="customerAddress" name="address"
                                  rows="3" placeholder="<?php echo t("address"); ?> ..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        <?php echo t("cancel"); ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        <?php echo t("save"); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تعديل عميل/مورد
function editCustomer(customerId) {
    // جلب بيانات العميل/المورد عبر AJAX
    fetch(`get_customer.php?id=${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const customer = data.customer;

                // ملء النموذج
                document.getElementById('customerId').value = customer.id;
                document.getElementById('customerName').value = customer.name;
                document.getElementById('customerPhone').value = customer.phone || '';
                document.getElementById('customerEmail').value = customer.email || '';
                document.getElementById('customerTaxNumber').value = customer.tax_number || '';
                document.getElementById('customerAddress').value = customer.address || '';

                // تغيير عنوان النافذة
                const customerType = '<?php echo $customer_type; ?>';
                const typeLabel = customerType === 'supplier' ? 'المورد' : 'العميل';
                document.getElementById('addCustomerModalLabel').innerHTML =
                    `<i class="fas fa-edit me-2"></i>تعديل ${typeLabel}`;

                // إظهار النافذة
                const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
                modal.show();
            } else {
                alert('حدث خطأ في جلب البيانات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// حذف عميل/مورد
function deleteCustomer(customerId, customerName) {
    const customerType = '<?php echo $customer_type; ?>';
    const typeLabel = customerType === 'supplier' ? 'المورد' : 'العميل';

    if (confirm(`هل أنت متأكد من حذف ${typeLabel} "${customerName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        window.location.href = `customers.php?delete=${customerId}&type=${customerType}`;
    }
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.getElementById('addCustomerModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('customerForm').reset();
    document.getElementById('customerId').value = '';

    const customerType = '<?php echo $customer_type; ?>';
    const typeLabel = customerType === 'supplier' ? 'مورد' : 'عميل';
    document.getElementById('addCustomerModalLabel').innerHTML =
        `<i class="fas fa-plus me-2"></i>إضافة ${typeLabel} جديد`;
});
</script>

<!-- أزرار عائمة للفواتير السريعة -->
<div class="floating-buttons">
    <button class="floating-btn purchase-btn" onclick="window.location.href='index.php?open_quick_invoice=purchase'" title="فاتورة مشتريات سريعة">
        <i class="fas fa-shopping-cart"></i>
        <span class="btn-label">مشتريات</span>
    </button>
    <button class="floating-btn sale-btn" onclick="window.location.href='index.php?open_quick_invoice=sale'" title="فاتورة مبيعات سريعة">
        <i class="fas fa-file-invoice-dollar"></i>
        <span class="btn-label">مبيعات</span>
    </button>
</div>

<style>
/* الأزرار العائمة */
.floating-buttons {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 9999;
    display: flex;
    flex-direction: column-reverse;
    gap: 15px;
}

.floating-btn {
    width: 120px;
    height: 60px;
    border-radius: 30px;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
    flex-direction: row;
    gap: 8px;
    font-weight: 600;
    padding: 0 15px;
    text-decoration: none;
}

.floating-btn i {
    font-size: 18px;
}

.btn-label {
    font-size: 14px;
    font-weight: 600;
}

.floating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    width: 130px;
    text-decoration: none;
    color: white;
}

.sale-btn {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.sale-btn:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
}

.purchase-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.purchase-btn:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .floating-buttons {
        bottom: 80px;
        right: 20px;
    }

    .floating-btn {
        width: 100px;
        height: 50px;
        font-size: 14px;
    }

    .floating-btn:hover {
        width: 105px;
    }

    .btn-label {
        font-size: 12px;
    }
}
</style>

<?php require_once '../shared/includes/footer.php'; ?>