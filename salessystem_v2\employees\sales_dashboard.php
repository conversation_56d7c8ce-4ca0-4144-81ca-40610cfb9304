<?php
/**
 * لوحة تحكم نظام المبيعات - قسم الموظفين
 */

require_once __DIR__.'/../shared/config/simple_db_config.php';
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_logged_in']) || !$_SESSION['user_logged_in']) {
    header("Location: ../../work_management_login.php");
    exit();
}

// الحصول على اتصال قاعدة البيانات
$db = getSimpleDB();

if (!$db) {
    die('خطأ في الاتصال بقاعدة البيانات');
}

// جلب إحصائيات سريعة
$stats = [
    'total_sales' => 0,
    'total_purchases' => 0,
    'total_customers' => 0,
    'total_products' => 0
];

try {
    // إحصائيات العملاء
    $customers_result = $db->query("SELECT COUNT(*) as count FROM customers WHERE user_id = " . $_SESSION['user_id']);
    if ($customers_result) {
        $stats['total_customers'] = $customers_result->fetch_assoc()['count'];
    }
    
    // إحصائيات المنتجات (مشتركة)
    $products_result = $db->query("SELECT COUNT(*) as count FROM products");
    if ($products_result) {
        $stats['total_products'] = $products_result->fetch_assoc()['count'];
    }
    
} catch (Exception $e) {
    error_log('Error fetching stats: ' . $e->getMessage());
}

$page_title = 'لوحة تحكم المبيعات';
require_once __DIR__.'/../shared/includes/header_simple.php';
// الرسائل يتم عرضها تلقائياً في header_simple.php
?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-2 fw-bold text-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة تحكم المبيعات
                            </h1>
                            <p class="text-muted mb-0">
                                مرحباً <?php echo htmlspecialchars($_SESSION['user_name'] ?? 'المستخدم'); ?> في نظام إدارة المبيعات
                            </p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="sales.php" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                فاتورة جديدة
                            </a>
                            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="text-muted mb-2">إجمالي العملاء</h6>
                            <h3 class="mb-0 fw-bold text-primary"><?php echo number_format($stats['total_customers']); ?></h3>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>
                                نشط
                            </small>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-users text-primary fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="text-muted mb-2">إجمالي المنتجات</h6>
                            <h3 class="mb-0 fw-bold text-success"><?php echo number_format($stats['total_products']); ?></h3>
                            <small class="text-info">
                                <i class="fas fa-box me-1"></i>
                                متاح
                            </small>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-box text-success fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="text-muted mb-2">مبيعات اليوم</h6>
                            <h3 class="mb-0 fw-bold text-warning"><?php echo number_format($stats['total_sales'], 2); ?> ر.س</h3>
                            <small class="text-warning">
                                <i class="fas fa-chart-line me-1"></i>
                                اليوم
                            </small>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-dollar-sign text-warning fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="text-muted mb-2">مشتريات اليوم</h6>
                            <h3 class="mb-0 fw-bold text-danger"><?php echo number_format($stats['total_purchases'], 2); ?> ر.س</h3>
                            <small class="text-danger">
                                <i class="fas fa-shopping-bag me-1"></i>
                                اليوم
                            </small>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-shopping-bag text-danger fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <a href="sales.php" class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-shopping-cart d-block fs-3 mb-2"></i>
                                <span>فاتورة مبيعات</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <a href="purchases.php" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-shopping-bag d-block fs-3 mb-2"></i>
                                <span>فاتورة مشتريات</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <a href="customers.php" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-users d-block fs-3 mb-2"></i>
                                <span>إدارة العملاء</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <a href="reports.php" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-chart-bar d-block fs-3 mb-2"></i>
                                <span>التقارير</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر العمليات -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-history me-2 text-secondary"></i>
                            آخر العمليات
                        </h5>
                        <a href="reports.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt me-1"></i>
                            عرض الكل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fs-1 text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد عمليات حديثة</h6>
                        <p class="text-muted mb-0">ابدأ بإنشاء فاتورة جديدة لرؤية العمليات هنا</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__.'/../shared/includes/footer.php'; ?>

<style>
/* تحسينات إضافية */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.bg-opacity-10 {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}
</style>
