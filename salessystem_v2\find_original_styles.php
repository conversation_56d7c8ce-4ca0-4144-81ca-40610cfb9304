<?php
/**
 * البحث عن التنسيقات الأصلية في كامل المشروع
 */

echo '<h2>البحث عن التنسيقات الأصلية في المشروع</h2>';

// البحث في جميع ملفات CSS
$css_files = [];
$php_files_with_styles = [];
$original_styles_found = [];

// البحث في المجلدات
$directories_to_search = [
    'assets/css',
    'shared/assets/css',
    'includes',
    'shared/includes',
    'employees',
    'admin',
    'supervisors'
];

echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>البحث في ملفات CSS:</h3>';

foreach ($directories_to_search as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*.css');
        foreach ($files as $file) {
            $css_files[] = $file;
            echo "<p>📄 وُجد ملف CSS: <strong>$file</strong></p>";
            
            // فحص محتوى الملف
            $content = file_get_contents($file);
            $size = strlen($content);
            echo "<small style='margin-left: 20px; color: #666;'>الحجم: " . number_format($size) . " حرف</small><br>";
            
            // البحث عن تنسيقات أصلية
            if (strpos($content, 'التنسيقات الأصلية') !== false || 
                strpos($content, 'original') !== false ||
                strpos($content, 'simple') !== false) {
                $original_styles_found[] = $file;
                echo "<small style='margin-left: 20px; color: green;'>✅ يحتوي على تنسيقات أصلية</small><br>";
            }
        }
    }
}

echo '</div>';

echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>البحث في ملفات PHP عن التنسيقات المدمجة:</h3>';

$php_directories = ['employees', 'admin', 'supervisors', 'shared/includes'];

foreach ($php_directories as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*.php');
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // البحث عن CSS مدمج
            if (strpos($content, '<style>') !== false || 
                strpos($content, 'style=') !== false) {
                $php_files_with_styles[] = $file;
                echo "<p>📄 ملف PHP يحتوي على تنسيقات: <strong>$file</strong></p>";
                
                // استخراج التنسيقات
                preg_match_all('/<style[^>]*>(.*?)<\/style>/s', $content, $style_matches);
                if (!empty($style_matches[1])) {
                    foreach ($style_matches[1] as $i => $style_content) {
                        $style_lines = substr_count($style_content, "\n") + 1;
                        echo "<small style='margin-left: 20px; color: #666;'>كتلة CSS #" . ($i+1) . ": $style_lines سطر</small><br>";
                    }
                }
                
                // البحث عن inline styles
                preg_match_all('/style="([^"]*)"/', $content, $inline_matches);
                if (!empty($inline_matches[1])) {
                    $inline_count = count($inline_matches[1]);
                    echo "<small style='margin-left: 20px; color: #666;'>تنسيقات inline: $inline_count</small><br>";
                }
            }
        }
    }
}

echo '</div>';

// عرض التنسيقات الأصلية الموجودة
if (!empty($original_styles_found)) {
    echo '<div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>✅ التنسيقات الأصلية الموجودة:</h3>';
    
    foreach ($original_styles_found as $file) {
        echo "<h4>📄 $file</h4>";
        $content = file_get_contents($file);
        
        // عرض أول 20 سطر من الملف
        $lines = explode("\n", $content);
        $preview_lines = array_slice($lines, 0, 20);
        
        echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; overflow-x: auto;">';
        foreach ($preview_lines as $i => $line) {
            $line_num = $i + 1;
            echo "<span style='color: #666;'>$line_num:</span> " . htmlspecialchars($line) . "<br>";
        }
        if (count($lines) > 20) {
            echo "<span style='color: #666;'>... و " . (count($lines) - 20) . " سطر إضافي</span>";
        }
        echo '</div>';
        
        // إنشاء رابط لنسخ هذا الملف
        echo "<p><a href='#' onclick='copyOriginalStyle(\"$file\")' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>استخدام هذه التنسيقات</a></p>";
    }
    
    echo '</div>';
}

// البحث عن ملفات backup
echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>البحث عن ملفات النسخ الاحتياطية:</h3>';

$backup_patterns = [
    '*.backup',
    '*.bak',
    '*.old',
    '*.original',
    '*_backup_*',
    '*_original_*'
];

$backup_files = [];
foreach ($backup_patterns as $pattern) {
    $files = glob($pattern);
    $backup_files = array_merge($backup_files, $files);
    
    // البحث في المجلدات الفرعية
    foreach ($directories_to_search as $dir) {
        if (is_dir($dir)) {
            $sub_files = glob($dir . '/' . $pattern);
            $backup_files = array_merge($backup_files, $sub_files);
        }
    }
}

if (!empty($backup_files)) {
    foreach ($backup_files as $file) {
        echo "<p>📦 ملف نسخة احتياطية: <strong>$file</strong></p>";
        
        if (pathinfo($file, PATHINFO_EXTENSION) === 'css' || 
            strpos($file, 'style') !== false) {
            echo "<small style='margin-left: 20px; color: green;'>✅ قد يحتوي على تنسيقات أصلية</small><br>";
        }
    }
} else {
    echo '<p style="color: orange;">⚠️ لم يتم العثور على ملفات نسخ احتياطية</p>';
}

echo '</div>';

// إنشاء ملف CSS موحد من التنسيقات الأصلية
echo '<div style="background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>إنشاء ملف CSS موحد:</h3>';

if (!empty($original_styles_found)) {
    $unified_css = "/* ملف CSS موحد من التنسيقات الأصلية */\n";
    $unified_css .= "/* تم إنشاؤه تلقائياً في " . date('Y-m-d H:i:s') . " */\n\n";
    
    foreach ($original_styles_found as $file) {
        $content = file_get_contents($file);
        $unified_css .= "/* من الملف: $file */\n";
        $unified_css .= $content . "\n\n";
    }
    
    $unified_file = 'assets/css/unified_original_styles.css';
    if (file_put_contents($unified_file, $unified_css)) {
        echo "<p style='color: green;'>✅ تم إنشاء ملف CSS موحد: <strong>$unified_file</strong></p>";
        echo "<p><a href='$unified_file' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>عرض الملف</a></p>";
    } else {
        echo '<p style="color: red;">❌ فشل في إنشاء الملف الموحد</p>';
    }
} else {
    echo '<p style="color: orange;">⚠️ لا توجد تنسيقات أصلية لدمجها</p>';
}

echo '</div>';

// ملخص النتائج
echo '<div style="background: #fff3cd; color: #856404; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>📊 ملخص البحث:</h3>';
echo "<p><strong>ملفات CSS الموجودة:</strong> " . count($css_files) . "</p>";
echo "<p><strong>ملفات PHP بتنسيقات:</strong> " . count($php_files_with_styles) . "</p>";
echo "<p><strong>ملفات التنسيقات الأصلية:</strong> " . count($original_styles_found) . "</p>";
echo "<p><strong>ملفات النسخ الاحتياطية:</strong> " . count($backup_files) . "</p>";
echo '</div>';

// أدوات الإجراءات
echo '<div style="background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h4>🛠️ الإجراءات المتاحة:</h4>';
echo '<p><a href="restore_original_styling.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">استعادة التنسيقات الأصلية</a></p>';
echo '<p><a href="test_all_pages.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">اختبار جميع الصفحات</a></p>';
echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تسجيل الدخول</a></p>';
echo '</div>';

?>

<script>
function copyOriginalStyle(filename) {
    if (confirm('هل تريد استخدام التنسيقات من الملف: ' + filename + '؟')) {
        // يمكن إضافة AJAX هنا لنسخ الملف
        alert('سيتم تطبيق هذه التنسيقات. استخدم أداة "استعادة التنسيقات الأصلية" لتطبيق التغييرات.');
    }
}
</script>
