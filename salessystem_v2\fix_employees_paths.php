<?php
/**
 * إصلاح جميع المسارات الخاطئة في مجلد employees
 */

echo '<h2>إصلاح مسارات ملفات الموظفين</h2>';

// قائمة ملفات الموظفين
$employee_files = [
    'employees/customers.php',
    'employees/products.php',
    'employees/purchases.php',
    'employees/sales.php',
    'employees/reports.php',
    'employees/profile.php',
    'employees/settings.php',
    'employees/sales_dashboard.php'
];

// المسارات التي تحتاج إصلاح
$path_fixes = [
    // مسارات shared/shared إلى shared
    '/../../shared/shared/config/' => '/../shared/config/',
    '/../../shared/shared/includes/' => '/../shared/includes/',
    '/../../shared/shared/assets/' => '/../shared/assets/',
    '../shared/../../shared/shared/assets/' => '../shared/assets/',
    
    // مسارات config خاطئة
    '__DIR__.\'/../../shared/shared/config/simple_db_config.php\'' => '__DIR__.\'/../shared/config/simple_db_config.php\'',
    '__DIR__ . \'/../../shared/shared/config/simple_db_config.php\'' => '__DIR__ . \'/../shared/config/simple_db_config.php\'',
    
    // مسارات includes خاطئة
    '__DIR__.\'/../../shared/shared/includes/' => '__DIR__.\'/../shared/includes/',
    '__DIR__ . \'/../../shared/shared/includes/' => '__DIR__ . \'/../shared/includes/',
    
    // مسارات assets خاطئة
    '../shared/../../shared/shared/assets/' => '../shared/assets/',
    '../../shared/shared/assets/' => '../shared/assets/',
    
    // مسارات work_management_login خاطئة
    '../../work_management_login.php' => '../work_management_login.php',
    '../../../work_management_login.php' => '../work_management_login.php'
];

$total_fixes = 0;
$files_fixed = 0;

foreach ($employee_files as $file) {
    if (!file_exists($file)) {
        echo "<p style='color: orange;'>⚠️ الملف غير موجود: $file</p>";
        continue;
    }
    
    $content = file_get_contents($file);
    $original_content = $content;
    $file_fixes = 0;
    
    // تطبيق الإصلاحات
    foreach ($path_fixes as $wrong_path => $correct_path) {
        $count = 0;
        $content = str_replace($wrong_path, $correct_path, $content, $count);
        if ($count > 0) {
            $file_fixes += $count;
            echo "<small style='color: blue;'>  - تم إصلاح: $wrong_path → $correct_path ($count مرة)</small><br>";
        }
    }
    
    // حفظ الملف إذا تم تعديله
    if ($content !== $original_content) {
        if (file_put_contents($file, $content)) {
            echo "<p style='color: green;'>✅ تم إصلاح $file ($file_fixes إصلاح)</p>";
            $files_fixed++;
            $total_fixes += $file_fixes;
        } else {
            echo "<p style='color: red;'>❌ فشل في حفظ $file</p>";
        }
    } else {
        echo "<p style='color: gray;'>⚪ $file لا يحتاج إصلاح</p>";
    }
}

echo '<h3>ملخص الإصلاحات:</h3>';
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<p><strong>إجمالي الملفات المُصلحة:</strong> $files_fixed</p>";
echo "<p><strong>إجمالي الإصلاحات:</strong> $total_fixes</p>";
echo "</div>";

// فحص إضافي للتأكد من عدم وجود مسارات خاطئة
echo '<h3>فحص نهائي:</h3>';
$remaining_errors = 0;

foreach ($employee_files as $file) {
    if (!file_exists($file)) continue;
    
    $content = file_get_contents($file);
    
    // البحث عن مسارات خاطئة متبقية
    $error_patterns = [
        'shared/shared',
        '/../../shared/shared/',
        '../../../',
        '__DIR__.\'/../../shared/shared/',
        '__DIR__ . \'/../../shared/shared/'
    ];
    
    $file_errors = [];
    foreach ($error_patterns as $pattern) {
        if (strpos($content, $pattern) !== false) {
            $file_errors[] = $pattern;
        }
    }
    
    if (!empty($file_errors)) {
        echo "<p style='color: red;'>❌ $file لا يزال يحتوي على مسارات خاطئة: " . implode(', ', $file_errors) . "</p>";
        $remaining_errors++;
    } else {
        echo "<p style='color: green;'>✅ $file - جميع المسارات صحيحة</p>";
    }
}

if ($remaining_errors === 0) {
    echo '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 20px 0;">';
    echo '<h4>🎉 تم الانتهاء بنجاح!</h4>';
    echo '<p>جميع مسارات ملفات الموظفين تم إصلاحها بنجاح</p>';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;">';
    echo '<h4>⚠️ تحذير!</h4>';
    echo "<p>لا يزال هناك $remaining_errors ملف يحتوي على مسارات خاطئة</p>";
    echo '</div>';
}

echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة لصفحة تسجيل الدخول</a></p>';
?>
