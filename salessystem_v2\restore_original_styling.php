<?php
/**
 * استعادة التنسيقات الأصلية للمشروع
 */

echo '<h2>استعادة التنسيقات الأصلية</h2>';

// قائمة الملفات التي تحتاج استعادة التنسيقات
$files_to_restore = [
    // ملفات CSS
    'assets/css/style.css' => 'shared/assets/css/original_style.css',
    
    // ملفات الهيدر
    'shared/includes/header_simple.php',
    'shared/includes/admin_header_new.php',
    'shared/includes/supervisor_header_modern.php',
    
    // ملفات الموظفين
    'employees/index.php',
    'employees/sales_dashboard.php',
    'employees/sales.php',
    'employees/purchases.php',
    'employees/customers.php',
    'employees/products.php',
    'employees/reports.php',
    'employees/profile.php',
    
    // ملفات المديرين
    'admin/admin_dashboard.php',
    'admin/admin_reports.php',
    
    // ملفات المشرفين
    'supervisors/supervisor_dashboard.php'
];

$restored_files = 0;
$total_changes = 0;

echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>جاري استعادة التنسيقات الأصلية...</h3>';

// 1. استعادة ملف CSS الأساسي
if (file_exists('shared/assets/css/original_style.css')) {
    $original_css = file_get_contents('shared/assets/css/original_style.css');
    
    // إضافة تحسينات بسيطة للتنسيقات الأصلية
    $enhanced_original_css = $original_css . "\n\n" . '
/* تحسينات بسيطة للتنسيقات الأصلية */
.stats-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-card h3 {
    color: #007bff;
    margin-bottom: 10px;
}

.stats-card .number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.quick-actions {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.quick-actions .btn {
    margin: 5px;
    padding: 10px 20px;
}

.sidebar {
    background: #343a40;
    color: white;
    min-height: 100vh;
    padding: 20px;
}

.sidebar .nav-link {
    color: #ccc;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 5px;
    text-decoration: none;
    display: block;
}

.sidebar .nav-link:hover {
    background: #495057;
    color: white;
}

.sidebar .nav-link.active {
    background: #007bff;
    color: white;
}

.main-content {
    padding: 20px;
}

.page-header {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.page-header h1 {
    margin: 0;
    color: #333;
}

.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.floating-buttons .btn {
    display: block;
    margin-bottom: 10px;
    border-radius: 50px;
    padding: 12px 20px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.invoice-form {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.invoice-table th,
.invoice-table td {
    padding: 12px;
    text-align: right;
    border: 1px solid #dee2e6;
}

.invoice-table th {
    background: #f8f9fa;
    font-weight: bold;
}

.invoice-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-dialog {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    text-align: left;
}

.close {
    background: none;
    border: none;
    font-size: 1.5rem;
    float: left;
    cursor: pointer;
}

/* تنسيقات الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

/* تنسيقات الجوال */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .table {
        font-size: 12px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .floating-buttons {
        bottom: 10px;
        right: 10px;
    }
    
    .modal-dialog {
        width: 95%;
        margin: 10px;
    }
}
';
    
    if (file_put_contents('assets/css/style.css', $enhanced_original_css)) {
        echo '<p style="color: green;">✅ تم استعادة ملف CSS الأساسي مع التحسينات البسيطة</p>';
        $restored_files++;
        $total_changes++;
    } else {
        echo '<p style="color: red;">❌ فشل في استعادة ملف CSS الأساسي</p>';
    }
} else {
    echo '<p style="color: red;">❌ ملف التنسيقات الأصلية غير موجود</p>';
}

// 2. إزالة التنسيقات المعقدة من ملفات HTML
$complex_styles_to_remove = [
    // CSS Classes معقدة
    'modern-card',
    'enhanced-card',
    'dashboard-card-modern',
    'stats-card-enhanced',
    'modern-btn',
    'btn-gradient',
    'card-hover-effect',
    'animated-card',
    'shadow-lg',
    'transform-hover',
    
    // CSS Variables معقدة
    '--primary-gradient',
    '--card-shadow-hover',
    '--animation-duration',
    '--transform-scale',
    
    // Animations
    'fadeIn',
    'slideIn',
    'bounceIn',
    'zoomIn',
    'rotateIn'
];

// 3. استعادة التنسيقات البسيطة في الملفات
$files_to_simplify = [
    'employees/index.php',
    'employees/sales_dashboard.php',
    'admin/admin_dashboard.php',
    'supervisors/supervisor_dashboard.php'
];

foreach ($files_to_simplify as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $original_content = $content;
        
        // استبدال الكلاسات المعقدة بالبسيطة
        $replacements = [
            'modern-card' => 'card',
            'enhanced-card' => 'card',
            'dashboard-card-modern' => 'stats-card',
            'stats-card-enhanced' => 'stats-card',
            'modern-btn' => 'btn',
            'btn-gradient' => 'btn',
            'card-hover-effect' => 'card',
            'animated-card' => 'card',
            'shadow-lg' => '',
            'transform-hover' => '',
            'class="card shadow-lg transform-hover"' => 'class="card"',
            'class="btn modern-btn btn-gradient"' => 'class="btn btn-primary"',
            'class="stats-card-enhanced animated-card"' => 'class="stats-card"'
        ];
        
        $file_changes = 0;
        foreach ($replacements as $old => $new) {
            $count = 0;
            $content = str_replace($old, $new, $content, $count);
            $file_changes += $count;
        }
        
        // إزالة CSS inline معقد
        $content = preg_replace('/style="[^"]*transform[^"]*"/', '', $content);
        $content = preg_replace('/style="[^"]*animation[^"]*"/', '', $content);
        $content = preg_replace('/style="[^"]*transition[^"]*"/', '', $content);
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "<p style='color: green;'>✅ تم تبسيط التنسيقات في $file ($file_changes تغيير)</p>";
                $restored_files++;
                $total_changes += $file_changes;
            } else {
                echo "<p style='color: red;'>❌ فشل في تحديث $file</p>";
            }
        } else {
            echo "<p style='color: gray;'>⚪ $file لا يحتاج تغيير</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ $file غير موجود</p>";
    }
}

echo '</div>';

// ملخص النتائج
echo '<div style="background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>📊 ملخص استعادة التنسيقات:</h3>';
echo "<p><strong>الملفات المُحدثة:</strong> $restored_files</p>";
echo "<p><strong>إجمالي التغييرات:</strong> $total_changes</p>";
echo '<p><strong>النتيجة:</strong> تم استعادة التنسيقات الأصلية البسيطة بنجاح</p>';
echo '</div>';

// اختبار سريع
echo '<h3>اختبار التنسيقات المُستعادة:</h3>';
echo '<div style="background: #e9ecef; padding: 15px; border-radius: 8px;">';

if (file_exists('assets/css/style.css')) {
    $css_content = file_get_contents('assets/css/style.css');
    $css_size = strlen($css_content);
    echo "<p style='color: green;'>✅ ملف CSS الأساسي موجود (الحجم: " . number_format($css_size) . " حرف)</p>";
    
    // فحص وجود التنسيقات الأساسية
    $basic_classes = ['card', 'btn', 'table', 'form-control', 'alert'];
    foreach ($basic_classes as $class) {
        if (strpos($css_content, ".$class") !== false) {
            echo "<p style='color: green;'>✅ كلاس .$class موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ كلاس .$class غير موجود</p>";
        }
    }
} else {
    echo '<p style="color: red;">❌ ملف CSS الأساسي غير موجود</p>';
}

echo '</div>';

echo '<div style="background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h4>🔗 الخطوات التالية:</h4>';
echo '<p><a href="test_all_pages.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">اختبار جميع الصفحات</a></p>';
echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تسجيل الدخول واختبار التنسيقات</a></p>';
echo '<p><a href="employees/index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">صفحة الموظفين</a></p>';
echo '</div>';
?>
