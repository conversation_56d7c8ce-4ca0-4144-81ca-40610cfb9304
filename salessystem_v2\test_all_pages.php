<?php
/**
 * اختبار سريع لجميع صفحات النظام
 */

echo '<h2>اختبار سريع لجميع صفحات النظام</h2>';

// قائمة الصفحات للاختبار
$pages_to_test = [
    'الموظفين' => [
        'employees/index.php' => 'الصفحة الرئيسية للموظفين',
        'employees/sales_dashboard.php' => 'لوحة تحكم المبيعات',
        'employees/sales.php' => 'صفحة المبيعات',
        'employees/purchases.php' => 'صفحة المشتريات',
        'employees/customers.php' => 'إدارة العملاء',
        'employees/products.php' => 'إدارة المنتجات',
        'employees/reports.php' => 'التقارير',
        'employees/profile.php' => 'الملف الشخصي'
    ],
    'المشرفين' => [
        'supervisors/supervisor_dashboard.php' => 'لوحة تحكم المشرفين'
    ],
    'المديرين' => [
        'admin/admin_dashboard.php' => 'لوحة تحكم المديرين',
        'admin/admin_reports.php' => 'تقارير المديرين',
        'admin/admin_manage_admins.php' => 'إدارة المديرين'
    ],
    'صفحات عامة' => [
        'work_management_login.php' => 'صفحة تسجيل الدخول',
        'work_login_handler_simple.php' => 'معالج تسجيل الدخول'
    ]
];

$total_pages = 0;
$working_pages = 0;
$error_pages = 0;

echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';

foreach ($pages_to_test as $section => $pages) {
    echo "<h3>📁 قسم: $section</h3>";
    
    foreach ($pages as $page => $description) {
        $total_pages++;
        echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<strong>$description</strong> ($page)<br>";
        
        if (!file_exists($page)) {
            echo "<span style='color: red;'>❌ الملف غير موجود</span>";
            $error_pages++;
        } else {
            // فحص المحتوى للبحث عن أخطاء واضحة
            $content = file_get_contents($page);
            
            $has_errors = false;
            $error_messages = [];
            
            // فحص المسارات الخاطئة
            if (strpos($content, 'shared/shared') !== false) {
                $has_errors = true;
                $error_messages[] = 'يحتوي على مسارات خاطئة (shared/shared)';
            }
            
            // فحص أخطاء PHP الواضحة
            if (strpos($content, '<?php') === false && pathinfo($page, PATHINFO_EXTENSION) === 'php') {
                $has_errors = true;
                $error_messages[] = 'لا يحتوي على كود PHP';
            }
            
            if ($has_errors) {
                echo "<span style='color: orange;'>⚠️ يحتاج إصلاح: " . implode(', ', $error_messages) . "</span>";
                $error_pages++;
            } else {
                echo "<span style='color: green;'>✅ يبدو أنه يعمل بشكل صحيح</span>";
                $working_pages++;
            }
        }
        echo "</div>";
    }
}

echo '</div>';

// ملخص النتائج
echo '<div style="background: ' . ($error_pages > 0 ? '#fff3cd' : '#d4edda') . '; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h3>📊 ملخص النتائج:</h3>';
echo "<p><strong>إجمالي الصفحات:</strong> $total_pages</p>";
echo "<p><strong>الصفحات التي تعمل:</strong> $working_pages</p>";
echo "<p><strong>الصفحات التي تحتاج إصلاح:</strong> $error_pages</p>";

$success_rate = round(($working_pages / $total_pages) * 100, 1);
echo "<p><strong>معدل النجاح:</strong> $success_rate%</p>";

if ($error_pages === 0) {
    echo '<p style="color: #155724; font-weight: bold;">🎉 ممتاز! جميع الصفحات تعمل بشكل صحيح</p>';
} else {
    echo '<p style="color: #856404; font-weight: bold;">⚠️ بعض الصفحات تحتاج إصلاح</p>';
}
echo '</div>';

// فحص الملفات المهمة
echo '<h3>فحص الملفات الأساسية:</h3>';
echo '<div style="background: #e9ecef; padding: 15px; border-radius: 8px;">';

$important_files = [
    'shared/config/simple_db_config.php' => 'ملف قاعدة البيانات',
    'shared/includes/functions.php' => 'ملف الدوال العامة',
    'shared/includes/header_simple.php' => 'هيدر الموظفين',
    'shared/includes/admin_header_new.php' => 'هيدر المديرين',
    'shared/includes/supervisor_header_modern.php' => 'هيدر المشرفين',
    'shared/includes/footer.php' => 'فوتر عام',
    'shared/includes/admin_footer.php' => 'فوتر المديرين',
    'shared/includes/supervisor_footer_modern.php' => 'فوتر المشرفين'
];

foreach ($important_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description ($file)</p>";
    } else {
        echo "<p style='color: red;'>❌ $description ($file) - غير موجود</p>";
    }
}

echo '</div>';

// اختبار قاعدة البيانات
echo '<h3>اختبار قاعدة البيانات:</h3>';
echo '<div style="background: #e9ecef; padding: 15px; border-radius: 8px;">';

if (file_exists('shared/config/simple_db_config.php')) {
    try {
        require_once 'shared/config/simple_db_config.php';
        echo '<p style="color: green;">✅ تم تحميل ملف قاعدة البيانات</p>';
        
        $db = getSimpleDB();
        if ($db && !$db->connect_error) {
            echo '<p style="color: green;">✅ الاتصال بقاعدة البيانات يعمل</p>';
            
            // فحص الجداول المهمة
            $tables = ['users', 'admins', 'supervisors', 'sales', 'purchases'];
            foreach ($tables as $table) {
                $result = $db->query("SHOW TABLES LIKE '$table'");
                if ($result && $result->num_rows > 0) {
                    echo "<p style='color: green;'>✅ جدول $table موجود</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ جدول $table غير موجود</p>";
                }
            }
        } else {
            echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: red;">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p style="color: red;">❌ ملف قاعدة البيانات غير موجود</p>';
}

echo '</div>';

// الخطوات التالية
echo '<div style="background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<h4>🔗 الخطوات التالية:</h4>';

if ($error_pages > 0) {
    echo '<p><a href="auto_fix_all_paths.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إصلاح المسارات تلقائياً</a></p>';
}

echo '<p><a href="create_test_users.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إنشاء المستخدمين التجريبيين</a></p>';
echo '<p><a href="test_work_login.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">اختبار تسجيل الدخول</a></p>';
echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تسجيل الدخول</a></p>';
echo '</div>';
?>
