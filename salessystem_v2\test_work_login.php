<?php
/**
 * اختبار نظام تسجيل الدخول لإدارة العمل
 */
require_once 'shared/config/simple_db_config.php';

echo '<h2>اختبار نظام تسجيل الدخول</h2>';

// الحصول على اتصال قاعدة البيانات
$db = getSimpleDB();
if (!$db) {
    echo '<p style="color: red;">❌ فشل الاتصال بقاعدة البيانات</p>';
    exit;
}

echo '<p style="color: green;">✅ تم الاتصال بقاعدة البيانات بنجاح</p>';

echo '<h3>فحص الجداول والمستخدمين:</h3>';

// فحص جدول المستخدمين
echo '<h4>1. جدول المستخدمين (employees):</h4>';
$users_result = $db->query("SELECT id, username, full_name, status FROM users WHERE status = 'active'");
if ($users_result && $users_result->num_rows > 0) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
    echo '<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الحالة</th></tr>';
    while ($user = $users_result->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($user['id']) . '</td>';
        echo '<td>' . htmlspecialchars($user['username']) . '</td>';
        echo '<td>' . htmlspecialchars($user['full_name'] ?? 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($user['status']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ لا توجد مستخدمين نشطين في جدول users</p>';
}

// فحص جدول المشرفين
echo '<h4>2. جدول المشرفين (supervisors):</h4>';
$supervisors_result = $db->query("SELECT id, username, full_name, department, status FROM supervisors WHERE status = 'active'");
if ($supervisors_result && $supervisors_result->num_rows > 0) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
    echo '<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>القسم</th><th>الحالة</th></tr>';
    while ($supervisor = $supervisors_result->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($supervisor['id']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['username']) . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['full_name'] ?? 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['department'] ?? 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($supervisor['status']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ لا توجد مشرفين نشطين في جدول supervisors</p>';
}

// فحص جدول المديرين
echo '<h4>3. جدول المديرين (admins):</h4>';
$admins_result = $db->query("SELECT id, username, full_name, role, status FROM admins WHERE status = 'active'");
if ($admins_result && $admins_result->num_rows > 0) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
    echo '<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th><th>الحالة</th></tr>';
    while ($admin = $admins_result->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($admin['id']) . '</td>';
        echo '<td>' . htmlspecialchars($admin['username']) . '</td>';
        echo '<td>' . htmlspecialchars($admin['full_name'] ?? 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($admin['role'] ?? 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($admin['status']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ لا توجد مديرين نشطين في جدول admins</p>';
}

echo '<h3>اختبار تسجيل الدخول:</h3>';

// اختبار تسجيل دخول موظف
echo '<h4>اختبار تسجيل دخول الموظف:</h4>';
$test_employee = testLogin('employee', 'employee123', 'employees');
echo $test_employee;

// اختبار تسجيل دخول مشرف
echo '<h4>اختبار تسجيل دخول المشرف:</h4>';
$test_supervisor = testLogin('supervisor', 'supervisor123', 'supervisors');
echo $test_supervisor;

// اختبار تسجيل دخول مدير
echo '<h4>اختبار تسجيل دخول المدير:</h4>';
$test_admin = testLogin('admin', 'admin123', 'management');
echo $test_admin;

function testLogin($username, $password, $department) {
    global $db;
    
    // تحديد الجدول
    $table = '';
    switch ($department) {
        case 'employees':
            $table = 'users';
            break;
        case 'supervisors':
            $table = 'supervisors';
            break;
        case 'management':
            $table = 'admins';
            break;
    }
    
    if (empty($table)) {
        return '<p style="color: red;">❌ قسم غير صحيح</p>';
    }
    
    // البحث عن المستخدم
    $query = "SELECT * FROM $table WHERE username = ? AND status = 'active'";
    $stmt = $db->prepare($query);
    
    if (!$stmt) {
        return '<p style="color: red;">❌ خطأ في الاستعلام: ' . $db->error . '</p>';
    }
    
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows !== 1) {
        return '<p style="color: red;">❌ المستخدم غير موجود أو غير نشط</p>';
    }
    
    $user = $result->fetch_assoc();
    
    // التحقق من كلمة المرور
    if (!password_verify($password, $user['password'])) {
        return '<p style="color: red;">❌ كلمة المرور غير صحيحة</p>';
    }
    
    return '<p style="color: green;">✅ تسجيل الدخول ناجح للمستخدم: ' . htmlspecialchars($user['full_name'] ?? $user['username']) . '</p>';
}

echo '<h3>الروابط المفيدة:</h3>';
echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
echo '<p><a href="create_test_users.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إنشاء المستخدمين التجريبيين</a></p>';
echo '<p><a href="work_management_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">صفحة تسجيل الدخول</a></p>';
echo '<p><a href="work_login_handler_simple.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">معالج تسجيل الدخول</a></p>';
echo '</div>';
?>
