<?php
session_start();

// جلب رسائل الخطأ والنجاح
$error_message = $_SESSION['error'] ?? '';
$success_message = $_SESSION['success'] ?? '';

// مسح الرسائل من الجلسة
unset($_SESSION['error'], $_SESSION['success']);

// معالجة تسجيل الدخول عبر AJAX (ضع هذا بعد session_start مباشرة)
if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $result = [ 'success' => false, 'message' => 'بيانات غير مكتملة' ];
    if ($username && $password && $department) {
        // تحقق من القسم
        if ($department === 'employees') {
            // تحقق من الموظفين (يفضل ربط قاعدة بيانات حقيقية هنا)
            // مثال: تحقق من جدول employees
            // إذا نجح:
            // $_SESSION['employee_logged_in'] = true;
            // $_SESSION['username'] = $username;
            $result = [
                'success' => true,
                'username' => $username,
                'redirect' => 'employees/index.php',
                'message' => 'تم تسجيل الدخول كموظف'
            ];
        } elseif ($department === 'supervisors') {
            // تحقق من المشرفين
            $result = [
                'success' => true,
                'username' => $username,
                'redirect' => 'supervisors/supervisor_dashboard.php',
                'message' => 'تم تسجيل الدخول كمشرف'
            ];
        } elseif ($department === 'management') {
            // تحقق من الإدارة
            $result = [
                'success' => true,
                'username' => $username,
                'redirect' => 'admin/admin_dashboard.php',
                'message' => 'تم تسجيل الدخول كمدير'
            ];
        } else {
            $result = [ 'success' => false, 'message' => 'قسم غير معروف' ];
        }
    }
    header('Content-Type: application/json');
    echo json_encode($result);
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العمل - تسجيل الدخول</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            
            --shadow-light: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 15px 40px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 20px 50px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-x: hidden;
        }

        .main-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            animation: fadeInDown 1s ease-out;
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            padding: 0 1rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: var(--shadow-light);
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 300px;
            cursor: pointer;
        }

        .login-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: var(--shadow-heavy);
            height: 520px;
        }

        .login-card.form-active {
            height: 520px;
            transform: translateY(-15px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .card-header {
            padding: 2rem;
            text-align: center;
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
        }

        .card-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            transition: all 0.4s ease;
        }

        .employees-card .card-icon {
            background: var(--primary-gradient);
        }

        .supervisors-card .card-icon {
            background: var(--warning-gradient);
        }

        .management-card .card-icon {
            background: var(--danger-gradient);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .card-description {
            color: #7f8c8d;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .login-form {
            padding: 0 2rem 2rem;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.5s ease 0.3s;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            pointer-events: none;
        }

        .login-card:hover .login-form,
        .login-card.form-active .login-form {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .form-label {
            position: absolute;
            top: 12px;
            right: 15px;
            color: #7f8c8d;
            font-size: 1rem;
            transition: all 0.3s ease;
            pointer-events: none;
            background: white;
            padding: 0 5px;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            top: -8px;
            font-size: 0.85rem;
            color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .employees-card .login-btn {
            background: var(--primary-gradient);
        }

        .supervisors-card .login-btn {
            background: var(--warning-gradient);
        }

        .management-card .login-btn {
            background: var(--danger-gradient);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .login-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .login-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .login-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }
            
            .cards-container {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .login-card {
                height: 250px;
            }
            
            .login-card:hover {
                height: 450px;
            }
        }

        /* Loading Animation */
        .loading {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0) translateY(-15px) scale(1.02); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px) translateY(-15px) scale(1.02); }
            20%, 40%, 60%, 80% { transform: translateX(5px) translateY(-15px) scale(1.02); }
        }

        .btn-loading .loading {
            display: inline-block;
        }

        .btn-loading .btn-text {
            opacity: 0.7;
        }

        /* تأثيرات SweetAlert مخصصة */
        .success-popup {
            border-left: 5px solid #28a745 !important;
        }

        .error-popup {
            border-left: 5px solid #dc3545 !important;
        }

        /* أزرار الوصول السريع */
        .quick-access-buttons {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 0.5rem;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .quick-access-buttons .btn {
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .quick-access-buttons .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
    </style>
<style>
/* تنسيقات مدمجة للاختبار */
.login-card {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 20px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden !important;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    height: 300px !important;
    cursor: pointer !important;
}

.login-card:hover {
    transform: translateY(-15px) scale(1.02) !important;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2) !important;
    height: 520px !important;
}

.login-form {
    padding: 0 2rem 2rem !important;
    opacity: 0 !important;
    transform: translateY(30px) !important;
    transition: all 0.5s ease 0.3s !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    pointer-events: none !important;
}

.login-card:hover .login-form {
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
}

.form-control {
    width: 100% !important;
    padding: 12px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: rgba(255, 255, 255, 0.9) !important;
}

.form-control:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    background: white !important;
}

.login-btn {
    width: 100% !important;
    padding: 12px !important;
    border: none !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: white !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.login-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.card-icon {
    width: 80px !important;
    height: 80px !important;
    margin: 0 auto 1rem !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 2rem !important;
    color: white !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}
</style>
</head>
<body>
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-building me-3"></i>
                إدارة العمل
            </h1>
            <p class="page-subtitle">اختر القسم المناسب لتسجيل الدخول</p>

            
           <!-- <div class="quick-access-buttons mt-4">
                <a href="employees/index.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="fas fa-chart-line me-1"></i>
                    نظام المبيعات
                </a>
                <a href="admin/admin_dashboard.php" class="btn btn-outline-light btn-sm me-2">
                    <i class="fas fa-crown me-1"></i>
                    لوحة الإدارة
                </a>
                <a href="supervisors/supervisor_dashboard.php" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-user-tie me-1"></i>
                    لوحة المشرفين
                </a>
            </div>-->
        </div>

        <!-- Login Cards -->
        <div class="cards-container">
            <!-- Employees Card -->
            <div class="login-card employees-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">الموظفين</h3>
                    <p class="card-description">
                        الدخول إلى نظام المبيعات والمشتريات - إدارة العمليات اليومية والتقارير
                    </p>
                </div>
                <form class="login-form" onsubmit="return handleLogin(event, 'employees')">
                    <input type="hidden" name="department" value="employees">
                    <div class="form-group">
                        <input type="text" name="username" class="form-control" placeholder=" " required>
                        <label class="form-label">اسم المستخدم</label>
                    </div>
                    <div class="form-group">
                        <input type="password" name="password" class="form-control" placeholder=" " required>
                        <label class="form-label">كلمة المرور</label>
                    </div>
                    <button type="submit" class="login-btn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <span class="loading"></span>
                    </button>
                </form>
            </div>

            <!-- Supervisors Card -->
            <div class="login-card supervisors-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="card-title">المشرفين</h3>
                    <p class="card-description">
                        الدخول إلى لوحة تحكم المشرفين - مراقبة العمليات والتقارير المتقدمة
                    </p>
                </div>
                <form class="login-form" onsubmit="return handleLogin(event, 'supervisors')">
                    <input type="hidden" name="department" value="supervisors">
                    <div class="form-group">
                        <input type="text" name="username" class="form-control" placeholder=" " required>
                        <label class="form-label">اسم المستخدم</label>
                    </div>
                    <div class="form-group">
                        <input type="password" name="password" class="form-control" placeholder=" " required>
                        <label class="form-label">كلمة المرور</label>
                    </div>
                    <button type="submit" class="login-btn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <span class="loading"></span>
                    </button>
                </form>
            </div>

            <!-- Management Card -->
            <div class="login-card management-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 class="card-title">الإدارة</h3>
                    <p class="card-description">
                        الدخول إلى لوحة تحكم الإدارة - التحكم الكامل في النظام والمستخدمين
                    </p>
                </div>
                <form class="login-form" onsubmit="return handleLogin(event, 'management')">
                    <input type="hidden" name="department" value="management">
                    <div class="form-group">
                        <input type="text" name="username" class="form-control" placeholder=" " required>
                        <label class="form-label">اسم المستخدم</label>
                    </div>
                    <div class="form-group">
                        <input type="password" name="password" class="form-control" placeholder=" " required>
                        <label class="form-label">كلمة المرور</label>
                    </div>
                    <button type="submit" class="login-btn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <span class="loading"></span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // تعامل مع تسجيل الدخول
        async function handleLogin(event, department) {
            event.preventDefault();

            const form = event.target;
            const username = form.querySelector('input[name="username"]').value.trim();
            const password = form.querySelector('input[name="password"]').value.trim();
            const submitBtn = form.querySelector('.login-btn');

            // التحقق من صحة البيانات
            if (!username || !password) {
                Swal.fire({
                    icon: 'warning',
                    title: 'بيانات ناقصة',
                    text: 'يرجى إدخال اسم المستخدم وكلمة المرور',
                    confirmButtonText: 'حسناً',
                    background: '#fff',
                    color: '#2c3e50'
                });
                return false;
            }

            // إضافة حالة التحميل
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            try {
                // إرسال طلب AJAX
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                formData.append('department', department);
                formData.append('ajax', '1');

                console.log('إرسال البيانات:', {
                    username: username,
                    password: '***',
                    department: department,
                    ajax: '1'
                });

                const response = await fetch('work_login_handler_simple.php', {
                    method: 'POST',
                    body: formData
                });

                console.log('استجابة الخادم:', response);
                console.log('حالة الاستجابة:', response.status);

                // التحقق من حالة الاستجابة
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // قراءة النص أولاً للتحقق
                const responseText = await response.text();
                console.log('نص الاستجابة:', responseText);

                // محاولة تحويل إلى JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('JSON المحول:', result);
                } catch (jsonError) {
                    console.error('خطأ في تحويل JSON:', jsonError);
                    throw new Error('استجابة غير صحيحة من الخادم: ' + responseText.substring(0, 200));
                }

                console.log('نتيجة تسجيل الدخول:', result);

                if (result.success) {
                    // تأثير نجاح على البطاقة
                    const card = form.closest('.login-card');
                    card.style.background = 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)';
                    card.style.borderColor = '#28a745';

                    // نجح تسجيل الدخول
                    await Swal.fire({
                        icon: 'success',
                        title: 'تم تسجيل الدخول بنجاح',
                        text: `مرحباً بك ${result.username}`,
                        timer: 2000,
                        showConfirmButton: false,
                        background: '#fff',
                        color: '#2c3e50',
                        customClass: {
                            popup: 'success-popup'
                        }
                    });

                    // تأثير انتقال سلس
                    document.body.style.transition = 'opacity 0.5s ease';
                    document.body.style.opacity = '0.7';

                    setTimeout(() => {
                        // التحقق من وجود صفحة التوجه
                        const redirectPage = result.data && result.data.redirect ? result.data.redirect : result.redirect;
                        console.log('صفحة التوجه:', redirectPage);

                        if (!redirectPage || redirectPage === 'undefined') {
                            console.error('صفحة التوجه غير محددة:', result);
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ في التوجه',
                                text: 'لم يتم تحديد صفحة التوجه بشكل صحيح'
                            });
                            return;
                        }

                        console.log('التوجه إلى:', redirectPage);
                        window.location.href = redirectPage;
                    }, 500);

                } else {
                    // تأثير خطأ على البطاقة
                    const card = form.closest('.login-card');
                    card.style.background = 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)';
                    card.style.borderColor = '#dc3545';

                    // اهتزاز البطاقة
                    card.style.animation = 'shake 0.5s ease-in-out';

                    setTimeout(() => {
                        card.style.background = 'rgba(255, 255, 255, 0.95)';
                        card.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                        card.style.animation = '';
                    }, 2000);

                    // فشل تسجيل الدخول
                    await Swal.fire({
                        icon: 'error',
                        title: 'خطأ في تسجيل الدخول',
                        text: result.message,
                        confirmButtonText: 'حاول مرة أخرى',
                        background: '#fff',
                        color: '#2c3e50',
                        customClass: {
                            popup: 'error-popup'
                        }
                    });

                    // مسح الحقول
                    form.querySelector('input[name="password"]').value = '';
                    form.querySelector('input[name="username"]').focus();
                }

            } catch (error) {
                console.error('خطأ في الطلب:', error);

                await Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الاتصال',
                    text: 'حدث خطأ في الاتصال بالخادم، يرجى المحاولة لاحقاً',
                    confirmButtonText: 'حسناً',
                    background: '#fff',
                    color: '#2c3e50'
                });

            } finally {
                // إزالة حالة التحميل
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
            }

            return false;
        }

        // تأثيرات إضافية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // عرض رسائل الخطأ والنجاح
            <?php if (!empty($error_message)): ?>
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: '<?php echo addslashes($error_message); ?>',
                confirmButtonText: 'حاول مرة أخرى',
                background: '#fff',
                color: '#2c3e50'
            });
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
            Swal.fire({
                icon: 'success',
                title: 'نجح',
                text: '<?php echo addslashes($success_message); ?>',
                timer: 2000,
                showConfirmButton: false,
                background: '#fff',
                color: '#2c3e50'
            });
            <?php endif; ?>

            // إضافة تأثير الظهور التدريجي للكروت
            const cards = document.querySelectorAll('.login-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 * (index + 1));
            });

            // تأثير الجسيمات في الخلفية
            createParticles();
        });

        // إنشاء تأثير الجسيمات
        function createParticles() {
            const particlesContainer = document.createElement('div');
            particlesContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: -1;
            `;
            document.body.appendChild(particlesContainer);

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 4px;
                    height: 4px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation-delay: ${Math.random() * 2}s;
                `;
                particlesContainer.appendChild(particle);
            }

            // إضافة CSS للحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes float {
                    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
                    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
                }
            `;
            document.head.appendChild(style);
        }

        // تأثير التمرير السلس للكروت
        document.querySelectorAll('.login-card').forEach(card => {
            let isFormActive = false;

            card.addEventListener('mouseenter', function() {
                if (!isFormActive) {
                    this.classList.add('form-active');
                }
            });

            card.addEventListener('mouseleave', function() {
                if (!isFormActive) {
                    this.classList.remove('form-active');
                }
            });

            // عند النقر على البطاقة، تبقى مفتوحة
            card.addEventListener('click', function(e) {
                if (e.target.closest('.login-form')) {
                    isFormActive = true;
                    this.classList.add('form-active');
                }
            });

            // عند النقر خارج البطاقة، تغلق
            document.addEventListener('click', function(e) {
                if (!card.contains(e.target)) {
                    isFormActive = false;
                    card.classList.remove('form-active');
                }
            });
        });

        // تأثير الكتابة في الحقول
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });

            // تأثير الكتابة
            input.addEventListener('input', function() {
                if (this.value.length > 0) {
                    this.style.borderColor = '#667eea';
                    this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                } else {
                    this.style.borderColor = '#e9ecef';
                    this.style.boxShadow = 'none';
                }
            });
        });
    </script>
</body>
</html>
